<footer class="footer">
  <div  >
    <p *ngIf="erro" class="text-danger"><b>{{erro}}</b></p>
    <div class="row">
      <div style="display: inline-flex">
        <div class="col flex-fixed-width-item pl-0 pr-0 " [ngClass]="{'com-unidade': exibirUnidade()}"   >
          <button class="btn btn-outline-light decre"  (click)="diminuirQtde()"><i class="fas fa-minus"></i></button>
          <div  (click)="informarPeso()" class="input">
            {{itemPedido.qtde}} <span *ngIf="exibirUnidade()"> {{itemPedido.produto.unidadeMedida.sigla}}</span>
          </div>

          <button class="btn btn-outline-light incre"  (click)="aumentarQtde()" [disabled]="itemPedido.qtde === produto.qtdMaxima">
            <i class="fas fa-plus" ></i></button>
        </div>
      </div>
      <div class="col">
        <button class="btn btn-primary btn-block" (click)="adicionarProduto()"  [disabled]="produto.indisponivel || adicionandoProduto" >
          Adicionar ( <span *ngIf="!itemPedido.brinde">{{itemPedido.total | currency: 'BRL'}}</span>
                       <span *ngIf="itemPedido.brinde">{{itemPedido.valorResgatado }} {{itemPedido.produto.acumulo}}</span>)
        </button>
      </div>
    </div>
  </div>

</footer>

<div class="powered-by">
  <i class="fas fa-heart" style="color: #ff6b6b; margin-right: 4px; font-size: 10px;"></i>
  Powered by <a href="https://meucardapio.ai" target="_blank" style="color: #6c757d; text-decoration: none;">Meu Cardápio</a>
  <i class="fas fa-sparkles" style="color: #ffd93d; margin-left: 4px; font-size: 9px;"></i>
</div>

<div style="width: 100%;height: 70px;" *ngIf="isMobile">
  &nbsp;
</div>


<div id="alertaFechado" class="modal fade" tabindex="-1" role="dialog"   aria-modal="true">
  <div class="modal-dialog" style="border: solid 1px #f9f9f9;border-radius: 5px;">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-information h1 text-info"></i>
          <h4 class="mt-2"  >{{mensagemAbrirPedidos}}</h4>
          <p class="mt-3">Continue olhando nosso cardápio à vontade.</p>
          <button type="button" class="btn btn-info my-2" data-dismiss="modal" >Continuar</button>

          <button class="btn btn-primary ml-2" *ngIf="modoVisualizacao">
            <app-exibir-whatsapp [empresa]="empresa" [light]="true"></app-exibir-whatsapp>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
