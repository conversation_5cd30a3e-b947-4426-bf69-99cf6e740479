.carrinho {
  padding: 15px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom) + 25px);
  background: #3E9920 !important;
  color: #fff;
  position: relative;
  line-height: 20px;
  font-size: 12px;
}

::ng-deep .quiosque {
  .carrinho {
    padding: 20px;
    font-size: 16px;

    .font-13 {
      font-size: 16px !important;
    }
  }
}

.deslocado{
  top: -60px !important;
}

.flex-fixed-width-item {
  flex: 0 0 55px;
}

.qtde_itens {
  top: 0px;
  left: 5px;
  border: solid 1px #fff;
  border-radius: 3px;
  padding: 0px 2px;
  font-size: 11px !important;
}

.fe-shopping-cart {
  position: relative;
  margin-left: 3px;
  top: 2px;
}

::ng-deep .tema-personalizado {
  .carrinho {
    background: var(--cor-fundo-site, #3E9920) !important;
    color: var(--cor-texto-primaria) !important;
    border: solid 1px var(--cor-borda, #3E9920) !important;
  }

  .qtde_itens {
    border: solid 1px var(--cor-borda, #3E9920) !important;
  }
}

::ng-deep .desktop .tema-personalizado {
  .carrinho {
    background: var(--cor-fundo-elementos, #3E9920) !important;
    color: var(--cor-texto-botao) !important;
  }

  .qtde_itens {
    border: solid 1px var(--cor-borda, #3E9920) !important;
  }
}
