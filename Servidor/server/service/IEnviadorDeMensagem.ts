import {SituacaoDeMensagem} from "./SituacaoDeMensagem";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {Empresa} from "../domain/Empresa";

export interface IEnviadorDeMensagem {
  requerAtivacaoDoTelefone(): boolean;

  envieSMS(telefone: string, mensagem: string): Promise<SituacaoDeMensagem>;

  envieMensagem(mensagemEnviada: MensagemEnviada, telefone: string, mensagem: string): Promise<SituacaoDeMensagem>;

  notifiqueAssinantes(mensagemEnviada: MensagemEnviada): any;

  notifiqueAssinantesEmpresa(empresa: Empresa): any;

  acompanheMensagem(idMensagem: string): Promise<SituacaoDeMensagem>;

  obtenhaMeioDeEnvio(): EnumMeioDeEnvio;
}
