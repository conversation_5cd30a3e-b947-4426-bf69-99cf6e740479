
import {Ambiente} from "./Ambiente";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Empresa} from "../domain/Empresa";
import {Usuario} from "../domain/Usuario";


export class VariaveisDeRequest {
  obtenhaIdEmpresaLogada() {
    return require('domain').active.contexto.idEmpresa // getNamespace('request').get(Sessao.EMPRESA_LOGADA)
  }

  possuiEmpresaLogada() {
    return this.obtenhaIdEmpresaLogada() != null;
  }

  obtenhaEmpresaLogada(): Promise<any> {
    return new Promise(resolve => {
      let contexto = require('domain').active.contexto
      let empresaDoContexto = contexto.empresa

      if(empresaDoContexto) return resolve(empresaDoContexto);

      const idEmpresaLogada = this.obtenhaIdEmpresaLogada()
      if ( !idEmpresaLogada ) { return resolve(null); }

      new MapeadorDeEmpresa().selecioneSync({id: idEmpresaLogada}).then( (empresa: Empresa) => {
        if(empresa) contexto.empresa = empresa;
        resolve(empresa);
      });
    });
  }

  obtenhaUrlRaiz(empresa: Empresa) {
    return this._obtenhaUrlSite(empresa, Ambiente.Instance.config.urlSite);
  }

  public obtenhaUrlCardapio(empresa: Empresa) {
    if(empresa && empresa.urlDaEmpresa) return "https://" + empresa.urlDaEmpresa.hostname

    return this._obtenhaUrlSite(empresa, Ambiente.Instance.config.urlCardapio)
  }

  private _obtenhaUrlSite(empresa: Empresa, urlSite: string) {
    const protocolo: string = Ambiente.Instance.config.protocolo;
    const dominio: string = empresa ? empresa.dominio : 'promokit';

    if ( urlSite.indexOf(':3000' ) >= 0 || urlSite.indexOf(':8443') >= 0 || urlSite.indexOf('ngrok-free.app') >= 0 )
      return protocolo + '://' + urlSite;

    return protocolo + '://' + dominio + '.' + urlSite;
  }

  obtenhaUrlRaizSemEmpresa() {
    const urlSite: string = Ambiente.Instance.config.urlSite;
    const protocolo: string = Ambiente.Instance.config.protocolo;
    let prefixo = '';

    if ( !(urlSite.indexOf(':3000' ) >= 0) && !(urlSite.indexOf(':8443') >= 0 ) )
      prefixo = 'www.';

    return protocolo + '://' + prefixo + urlSite;
  }

  obtenhaUsuarioLogado() {
    return new Promise(resolve => {
      let contexto = require('domain').active.contexto
      let usuarioLogado = contexto.usuario;

      if (usuarioLogado) return resolve(usuarioLogado);
      else resolve(null);
    });
  }

  obtenhaOperadorRequest() {
    return new Promise(resolve => {
      let contexto = require('domain').active.contexto
      let usuarioLogado = contexto.operador;

      if (usuarioLogado) return resolve(usuarioLogado);
      else resolve(null);
    });
  }

  salveOperador(operador: Usuario) {
    return new Promise(resolve => {
      let contexto = require('domain').active.contexto
      contexto.operador = operador;

      return resolve({
        sucesso: true
      });
    });
  }
}
