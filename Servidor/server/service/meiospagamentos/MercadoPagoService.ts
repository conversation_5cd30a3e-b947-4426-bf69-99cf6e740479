// SDK de Mercado Pago
import {Fatura} from "../../domain/faturamento/Fatura";
import {Empresa} from "../../domain/Empresa";
import {DTOBoletoMercadoPago} from "../../lib/mercadopago/DTOBoletoMercadoPago";
import {EnumFormaPagamento} from "../../lib/emun/EnumFormaPagamento";
import {EnumStatusPagamento, StatusPagamentoDeParaMercadoPago} from "../../lib/emun/EnumStatusPagamento";
import {DateUtils} from "../../lib/DateUtils";
import {Ambiente} from "../Ambiente";
import {Pagamento} from "../../domain/faturamento/Pagamento";
import {Usuario} from "../../domain/Usuario";
import {PedidoGenerico} from "../../domain/delivery/PedidoGenerico";
import {IMeioPagamentoService} from "./IMeioPagamentoService";
import {Pedido} from "../../domain/delivery/Pedido";
import {PagamentoPedido} from "../../domain/delivery/PagamentoPedido";
import {PedidoService} from "../PedidoService";
import {NotificacaoMeioPagamentoService} from "../NotificacaoMeioPagamentoService";

const mercadopago = require ('mercadopago');

let tabelaErros: any = {
  cc_rejected_bad_filled_card_number:	'Revise o número do cartão.',
  cc_rejected_bad_filled_date: 	'Revise a data de vencimento.',
  cc_rejected_bad_filled_other:	'Revise os dados.',
  cc_rejected_bad_filled_security_code:	'Revise o código de segurança do cartão.',
  cc_rejected_blacklist:	'Não pudemos processar seu pagamento.',
  cc_rejected_call_for_authorize:	'Você deve autorizar  o pagamento do valor ao Mercado Pago.',
  cc_rejected_card_disabled: 	'Ligue para o emissor para ativar seu cartão. O telefone está no verso do seu cartão.',
  cc_rejected_card_error:	'Não conseguimos processar seu pagamento.',
  cc_rejected_duplicated_payment:	'Você já efetuou um pagamento com esse valor. Caso precise pagar novamente, utilize outro cartão ou outra forma de pagamento.',
  cc_rejected_high_risk:	'Seu pagamento foi recusado. Escolha outra forma de pagamento.',
  cc_rejected_insufficient_amount:	'Saldo insuficiente',
  cc_rejected_invalid_installments:	'Cartão não aceita parcelamento',
  cc_rejected_max_attempts:	'Você atingiu o limite de tentativas permitido. Escolha outro cartão ou outra forma de pagamento',
  cc_rejected_other_reason:	'Seu cartão/bandeira não processa esse tipo de pagamento.'

}

export class MercadoPagoService implements IMeioPagamentoService{
  constructor(private token: string) {
    console.log('configurando token: ' + this.token)
    mercadopago.configurations.setAccessToken(this.token);
  }

  async obtenhaMeiosPagamentos() {
    let payment_methods = mercadopago.get("/v1/payment_methods?access_token=" + this.token);

    return payment_methods;
  }

  valideNovoBoletoFatura(fatura: Fatura): string{
    if(fatura.diasPraVencer() > 30)
      return 'Fatura com vencimento acima do prazo miminio, boletos vencem em no máximo 15 dias';

    if(fatura.obtenhaPagamentoPendente())
      return 'Fatura tem um pagamento pendente'

    return null;
  }

  async gereBoleto(fatura: Fatura) {
    let empresa: Empresa = fatura.contrato.empresa;

    return new Promise<any>((resolve, reject) => {

      let erro: string = this.valideNovoBoletoFatura(fatura)
      if(erro) return reject(erro)

      let dadosBoleto = new DTOBoletoMercadoPago(empresa, fatura);
      console.log(dadosBoleto)
      erro = dadosBoleto.valideDados();

      if(erro) return reject(erro)

      mercadopago.payment.create(dadosBoleto).then(  async (response: any) => {
        console.log(response);

        let payment = response.body;

        let dadosPagamento = {
          codigo: payment.id,
          valor: payment.transaction_amount,
          tipo: EnumFormaPagamento.Boleto,
          codigoDeBarras: payment.barcode.content,
          status: EnumStatusPagamento.Gerado,
          dataVencimento: DateUtils.strToDate(payment.date_of_expiration),
          url: payment.transaction_details.external_resource_url
        };

        let pagamento =  await fatura.gerePagamento(dadosPagamento);

        resolve(pagamento);
      }).catch(  (erro2: any) => {
        console.log(erro2);
        reject(erro2.message);
      });
    })
  }

  obtenhaPagamento(id: any){
    return new Promise<any>((resolve, reject) => {
        mercadopago.payment.get(id).then(  (response: any) => {
        resolve(response.body)
      }).catch(  (erro: any) => {
        console.log(erro);
        reject(erro.message)
      });
    })
  }

  busquePagamentos(){
    return new Promise<any>((resolve, reject) => {
      mercadopago.payment.search({
        qs: {
          status: 'approved',
          sort: 'date_created',
          criteria: 'desc'
        }
      }).then(  (response: any) => {
        resolve(response.body.results)
      }).catch(  (erro: any) => {
        console.log(erro);
        resolve(erro.message)
      });
    })
  }

  estornePagamento(idPagamento: string, valor: number = null){
    return new Promise<any>( async (resolve, reject) => {
      let dados: any = {};

      let payment = await this.obtenhaPagamento(idPagamento);

      if(payment && payment.status ===  "refunded" )
         return   resolve(payment.status);

      console.log('estornar payment: ' + idPagamento)
     // if(valor){
      //  dados.amount = valor;
      //  console.log(dados)
     // }

      mercadopago.payment.refund(idPagamento, dados).then(async (response: any, err: any) => {
        if(response && response.status === 200){
          resolve(response.body.status);
        } else {
          let erro = 'Não foi possível estornar transação no MercadoPago';
          console.log(response.status)
          console.log(response.body)

          if(response.message)   erro = String(`${erro}: ${response.message}`)
          reject(erro)
        }
      }).catch(  (erro: any) => {
        console.log(erro.message);
        reject(erro.message)
      });
    })
  }

  async sincronizePagamento(pedido: Pedido, pagamentoOnline: PagamentoPedido, empresa: any){
    let erro: string;
    if(pagamentoOnline.codigoTransacao) {
      console.log('sincronizar pagamento mercadopago: ' + pagamentoOnline.codigoTransacao);
      let payment = await this.obtenhaPagamento(pagamentoOnline.codigoTransacao).catch((err) => {
        erro = err
      })

      if(payment){
        let novoStatus = StatusPagamentoDeParaMercadoPago.get(payment.status);

        if(novoStatus.toString() !== pagamentoOnline.status.toString()){
          await pagamentoOnline.atualizeMercadoPago(payment)
          pedido.empresa = empresa;
          await new PedidoService().mudouStatusPagamento(pedido, pagamentoOnline, novoStatus);
          pagamentoOnline.pedido = pedido;
          await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoOnline, empresa)
        }
      }
    }

    return erro;
  }

  cancelePagamento(pagamento: Pagamento, operador: Usuario){
    return new Promise<void>( (resolve, reject) => {
     if(pagamento.status  >  1 ) return  reject('Pagamento não pode ser cancelado')

     mercadopago.payment.cancel(pagamento.codigo).then(  async (response: any) => {
       console.log(response)
        if(response.status === 200){
          await pagamento.cancele('Boleto cancelado por operador', operador);
          resolve();
        } else {
          let erro = 'Não foi possível realizar o cancamento da transação no MercadoPago';
          reject(erro)
        }
      }).catch(  (erro: any) => {
        console.log(erro);
        reject(erro.message)
      });

    })
  }


  criePagamentoQrCodePix(email: any, pedido: PedidoGenerico): Promise<any>{
    return  new Promise( (resolve, reject) => {
      const nome = pedido.contato.nome;
      let nomes =  nome.split(" ");
      let primeiroNome = nomes[0].trim();
      let ultimoNome =  nome.replace(primeiroNome, "").trim();

      if(!email) return reject('Nenhum email informado');

      const payment_data: any = {
        transaction_amount: Number(pedido.obtenhaTotalPagar()),
        description: 'Pagamento ' + pedido.empresa.nome + ' #' + pedido.codigo,
        payment_method_id: 'pix',
        payer: {
          email: email,
          first_name: primeiroNome,
          last_name: ultimoNome,
        }
      };

      if(Ambiente.Instance.producao)
        payment_data.notification_url = String(`https://${pedido.empresa.dominio}.promokit.com.br/hooks/mercadopago/pagamentos`)


      console.log('url notificacao: ', payment_data.notification_url);

      mercadopago.payment.create(payment_data).then( (respostaPix: any) => {
        let dadosResposta = respostaPix.response;
        console.log(dadosResposta);

        let transacao: any = {
          codigo: dadosResposta.id,
          status: dadosResposta.status,
          codigoQrCode: dadosResposta.point_of_interaction.transaction_data.qr_code
        };

        resolve(transacao);
      }).catch(function (error: Error) {
        console.error(error)
        reject(error.message);
      });
    });
  }

  criePagamentoCartao(dadosCartao: any, pedido: PedidoGenerico): Promise<any>{
   return  new Promise( (resolve, reject) => {
     if(!dadosCartao) return reject('Nennhum cartão foi informado');
     if(!dadosCartao.bandeira) return reject('Bandeira do cartão não informada');

     const payment_data: any = {
       transaction_amount: Number(pedido.obtenhaTotalPagar()),
       token: dadosCartao.token,
       description: String(`Seu pedido #${pedido.codigo} - ${pedido.empresa.nome}`),
       installments: 1,
       payment_method_id: dadosCartao.bandeira,
       external_reference: pedido.codigo.toString(),
       issuer_id: dadosCartao.issuer,
       payer: {
         email:  dadosCartao.email,
         identification: {
           type: 'CPF',
           number: dadosCartao.cpf.replace(/\D/g, '')
         }
       }
     };

     if(Ambiente.Instance.producao){
       payment_data.notification_url = String(`https://${pedido.empresa.dominio}.promokit.com.br/hooks/mercadopago/pagamentos`)
       if(pedido.empresa.rede === 'chinainbox')
         payment_data.application_fee = Number( ( payment_data.transaction_amount * 0.034).toFixed(2) )
     }

     console.log(payment_data)

     mercadopago.payment.save(payment_data)
       .then( (response: any) => {
         let transacao = response.body;
         console.log(transacao)

         if(transacao.status === 'rejected'){
           transacao.motivoReprovacao = 'Pagamento foi negado'
           if(tabelaErros[transacao.status_detail])
             transacao.motivoReprovacao = tabelaErros[transacao.status_detail]
         }
         resolve(transacao)
       })
       .catch( (error: any) => {
         console.log(error);
         reject(error.message);
       });

    })
  }
}
