
import async = require('async');
import {TipoDePontuacao} from "../domain/TipoDePontuacao";
import {MapeadorDeTipoDePontuacao} from "../mapeadores/MapeadorDeTipoDePontuacao";

export class EmpresaService {
  insira(tipoDePontuacao: TipoDePontuacao): Promise<boolean> {
    return new Promise( (resolve, reject) => {
      const mapeador = new MapeadorDeTipoDePontuacao();

      mapeador.transacao(async  (conexao: any, commit: Function) => {
        async.series([
          (cb: any) => {
            mapeador.insiraSync(tipoDePontuacao).then((inseriu: any) => { cb(); });
          },
        ], (erro: any) => {
          if ( !erro ) {
            commit( () => {
              resolve();
            });
          } else {
            conexao.rollback(() => {
              reject( erro);
            });
          }
        });
      });
    });
  }

  atualize(tipoDePontuacao: TipoDePontuacao): Promise<boolean> {
    return new Promise( (resolve, reject) => {
      const mapeador = new MapeadorDeTipoDePontuacao();

      mapeador.transacao(async  (conexao: any, commit: Function) => {
        async.series([
          (cb: any) => {
            mapeador.atualizeSync(tipoDePontuacao).then((inseriu: any) => { cb(); });
          },
        ], (erro: any) => {
          if ( !erro ) {
            commit( () => {
              resolve();
            });
          } else {
            conexao.rollback(() => {
              reject( erro);
            });
          }
        });
      });
    });
  }
}
