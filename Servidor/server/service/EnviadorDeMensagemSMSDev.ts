import {IEnviadorDeMensagem} from "./IEnviadorDeMensagem";
import {SituacaoDeMensagem} from "./SituacaoDeMensagem";
import * as request from "request-promise-native";
import {StatusDeMensagem} from "./StatusDeMensagem";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {Empresa} from "../domain/Empresa";


export class EnviadorDeMensagemSMSDev implements IEnviadorDeMensagem {
  private chave = '9K3822P36DEO0SDB2UJ66A24';

  obtenhaStatusDaMensagem(resposta: any): SituacaoDeMensagem {
    const situacao = new SituacaoDeMensagem();
    console.log('Mensagem enviada. Resposta: ');
    situacao.id = resposta.id;
    situacao.idSMSExterno = resposta.id;

    switch (resposta.descricao) {
      case "MENSAGEM NA FILA":
        situacao.status = StatusDeMensagem.Enviando;
        break;
      case "FILA":
        situacao.status = StatusDeMensagem.EmProcessamento;
        break;
      case "ENVIADA":
      case "RECEBIDA":
      case "VISUALIZADA":
        situacao.status = StatusDeMensagem.Enviada;
        break;
      default:
        situacao.status = StatusDeMensagem.NaoEnviada;
    }

    situacao.mensagem = resposta.descricao;
    console.log(situacao);
    return situacao;
  }

  acompanheMensagem(idMensagem: string): Promise<SituacaoDeMensagem> {
    return new Promise(resolve => {
      const options = {
        method: 'POST',
        uri: "http://api.smsdev.com.br/get" ,
        form: {
          key: this.chave,
          action: "status",
          id: idMensagem
        },
        headers: {
          /* 'content-type': 'application/x-www-form-urlencoded' */ // Is set automatically
        }
      };

      request(options)
        .then( (body) => {
          const resposta = JSON.parse(body);

          console.log(resposta);

          resolve(this.obtenhaStatusDaMensagem(resposta));

        })
        .catch(function (err) {
          const situacaoErro = new SituacaoDeMensagem();

          situacaoErro.status = StatusDeMensagem.NaoEnviada;
          situacaoErro.mensagem = err;
          resolve(situacaoErro);
        });
    });
  }

  envieSMS(telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    const dados = {
      key: this.chave,
      type: "9",
      number: telefone,
      msg: mensagem
    };

    return new Promise(resolve => {
      const options = {
        method: 'POST',
        uri: 'http://api.smsdev.com.br/send' ,
        form: dados,
        headers: {
          /* 'content-type': 'application/x-www-form-urlencoded' */ // Is set automatically
        }
      };
      // @ts-ignore
      request(options)
        .then( (body) => {
          console.log(body);
          const resposta = JSON.parse(body);

          resolve(this.obtenhaStatusDaMensagem(resposta));
        })
        .catch(function (err) {
          console.log(err);
          const situacaoErro = new SituacaoDeMensagem();

          situacaoErro.sucesso = false;
          situacaoErro.status = StatusDeMensagem.NaoEnviada;
          situacaoErro.mensagem = err;
          resolve(situacaoErro);
        });
    });
  }

  envieMensagem(mensagemEnviada: MensagemEnviada, telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    if( mensagem.length < 145 ) {
      mensagem = mensagemEnviada.empresa.nome.substr(0, 15) + ": " + mensagem;
    }

    console.log('Antes de enviar sms');
    const dados = {
      key: this.chave,
      type: "9",
      number: telefone,
      msg: mensagem
    };

    console.log(dados);

    return new Promise(resolve => {
      const options = {
        method: 'POST',
        uri: 'http://api.smsdev.com.br/send' ,
        form: dados,
        headers: {
          /* 'content-type': 'application/x-www-form-urlencoded' */ // Is set automatically
        }
      };

      request(options)
        .then( (body) => {
          console.log(body);
          const resposta = JSON.parse(body);

          resolve(this.obtenhaStatusDaMensagem(resposta));
        })
        .catch(function (err) {
          console.log(err);
          const situacaoErro = new SituacaoDeMensagem();

          situacaoErro.sucesso = false;
          situacaoErro.status = StatusDeMensagem.NaoEnviada;
          situacaoErro.mensagem = err;
          resolve(situacaoErro);
        });
    });
  }

  requerAtivacaoDoTelefone(): boolean {
    return false;
  }

  obtenhaMeioDeEnvio(): EnumMeioDeEnvio {
    return EnumMeioDeEnvio.SMS;
  }

  notifiqueAssinantes(mensagemEnviada: MensagemEnviada) {
  }

  notifiqueAssinantesEmpresa(empresa: Empresa) {
  }
}
