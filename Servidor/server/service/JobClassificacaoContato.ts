import { <PERSON>ron<PERSON>ob } from 'cron';
import { MapeadorDeClassificacaoContato } from '../mapeadores/MapeadorDeClassificacaoContato';
import { MapeadorDeEmpresa } from '../mapeadores/MapeadorDeEmpresa';
import { ExecutorAsync } from '../utils/ExecutorAsync';
import moment = require('moment');

export class JobClassificacaoContato {
    private job: CronJob;
    private static instance: JobClassificacaoContato;

    private constructor() {
        // Construtor vazio, pois não precisamos inicializar mapeadores aqui
    }

    public static get Instance(): JobClassificacaoContato {
        if (!JobClassificacaoContato.instance) {
            JobClassificacaoContato.instance = new JobClassificacaoContato();
        }
        return JobClassificacaoContato.instance;
    }

    public inicialize(): void {
        // Executa todos os dias às 3h da manhã
        this.job = new CronJob('0 3 * * *', async () => {
            await this.executar();
        });
        this.iniciar();
    }

    public iniciar(): void {
        this.job.start();
        console.log('Job de classificação de contatos iniciado');
    }

    public parar(): void {
        this.job.stop();
        console.log('Job de classificação de contatos parado');
    }

    private executar(): void {
        ExecutorAsync.execute(async (cb: Function) => {
            try {
                console.log('Iniciando classificação de contatos...');
                require('domain').active.contexto.idEmpresa = -1;

                const mapeadorDeEmpresa = new MapeadorDeEmpresa();
                const empresas = await mapeadorDeEmpresa.listeAsync({removida: false});
                console.log(`Carregadas ${empresas.length} empresas para processamento`);

                for (const empresa of empresas) {
                    try {
                        require('domain').active.contexto.idEmpresa = empresa.id;
                        require('domain').active.contexto.empresa = empresa;

                        console.log(`Iniciando classificação para empresa ${empresa.id} - ${empresa.nome}`);
                        const mapeadorDeClassificacao = new MapeadorDeClassificacaoContato();

                        const dataInicial = moment().subtract(30, 'days').startOf('day');
                        const dataFim = moment().endOf('day');

                        const parametros = {
                            dataInicio: dataInicial.toDate(),
                            dataFim: dataFim.toDate(),
                            qtdeVendasRecorrente: empresa.qtdeVendasRecorrente,
                            qtdeDiasEmRisco: empresa.qtdeDiasEmRisco,
                            qtdeDiasPerdido: empresa.qtdeDiasPerdido,
                            qtdeVendasVIP: empresa.qtdeVendasVIP,
                            ticketMedioVIP: empresa.ticketMedioVIP,
                            qtdeDiasPeriodo: empresa.qtdeDiasPeriodo
                        };

                        await mapeadorDeClassificacao.atualizarClassificacoes(empresa.id, parametros);
                        console.log(`Classificações atualizadas para empresa ${empresa.id}`);
                    } catch (erro) {
                        console.error(`Erro ao atualizar classificações da empresa ${empresa.id}:`, erro);
                    }
                }

                console.log('Classificação de contatos finalizada');
                cb();
            } catch (erro) {
                console.error('Erro ao executar job de classificação:', erro);
                cb(erro);
            }
        }, (erro: Error) => {
            if (erro) {
                console.error('Erro na execução do ExecutorAsync:', erro);
            }
        }, 0);
    }
}
