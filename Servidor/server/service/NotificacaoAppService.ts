import {Campanha} from "../domain/Campanha";
import {NotificacaoApp} from "../domain/app/NotificacaoApp";
import {MapeadorDeNotificacaoApp} from "../mapeadores/MapeadorDeNotificacaoApp";

export class NotificacaoAppService {
  insira(notificacao: NotificacaoApp): Promise<any> {
    return new Promise<any>( (resolve, reject) => {
      const mapeador = new MapeadorDeNotificacaoApp();

      mapeador.transacao(async (conexao: any, commit: Function) => {
        const resp = await mapeador.insiraGraph(notificacao).catch( (erro: any) => {});

        if( resp ) {
          commit(() => {
            resolve(null);
          });
        } else {
          conexao.rollback( () => {
            resolve({
              msg: 'Não foi possível salvar'
            });
          })
        }
      });
    });
  }

  atualize(notificacao: NotificacaoApp): Promise<any> {
    return new Promise<any>( (resolve, reject) => {
      const mapeador = new MapeadorDeNotificacaoApp();

      mapeador.transacao(async (conexao: any, commit: Function) => {
        const resp = await mapeador.atualizeSync(notificacao).catch( (erro: any) => {});

        if( resp ) {
          commit(() => {
            resolve(null);
          });
        } else {
          conexao.rollback( () => {
            resolve({
              msg: 'Não foi possível salvar'
            });
          })
        }
      });
    });
  }
}
