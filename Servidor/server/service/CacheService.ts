// @ts-ignore
import redis = require('redis');
import {IntegracaoIfood} from "../domain/integracoes/IntegracaoIfood";
import * as moment from "moment";

const options: redis.ClientOpts = {
  host: 'localhost',
  port: 6379,
};

let client: redis.RedisClient = redis.createClient(options);
const IFOODLOJA = 'ifoodloja:';
export class CacheService {
  static insiraJson(chave: string, obj: any, tempo = 300){
    const json = JSON.stringify(obj)

    client.set(chave, json, 'EX',  tempo)

  }

  static pingMerchant(empresa: any){
    if(!empresa.integracoesIfood.length || !empresa.estaRecebendoPedidos) return;

    empresa.integracoesIfood.forEach((integracaoIfood: IntegracaoIfood) => {
      if(!integracaoIfood.distribuida()){
        let  merchant: string =   integracaoIfood.idLoja

        let chaveLoja = IFOODLOJA + merchant;

        client.set(chaveL<PERSON>, merchant, 'EX', 60);
      }
    })
  }

  static getListaLojasPingando(){
    return new Promise<any>(resolve => {
      client.keys(IFOODLOJA + "*", (err, replay) => {
        try{
          if(err) console.error(err)
          let lojas: any = [];

          if(replay)
            lojas = replay.map((item: string) => item.replace(IFOODLOJA, ''));

          resolve(lojas)
        } catch (e) {
          console.error(e)
          resolve([])
        }
      })
    })
  }

  static getJson(chave: string): Promise<any>{
    return new Promise<void>(resolve => {
      client.get(chave , (err, jsonObjeto) => {
           if(err) console.error(err)
           try{
             resolve(JSON.parse(jsonObjeto))
           } catch (e) {
             console.error(e)
             resolve()
           }
      })
    })
  }

  static removaChave(chave: string) {
    client.del(chave);
  }

  static removaCachePrefixo(key: string) {
    return new Promise( (resolve, reject) => {
        client.del(key);

        resolve(true);
    });
  }

  static insiraNoMapJson(nomeMap: string, chave: string, obj: any) {
    const json = JSON.stringify(obj)

    client.hmset(nomeMap, chave, json);
    client.expire(nomeMap, 60 * 60 * 8);
  }

  static obtenhaValorMap(nomeMap: string, chave: string) {
    let incio = new Date().getTime();
    return new Promise( (resolve, reject) => {
      client.hmget(nomeMap, chave, (err, jsonObjeto) => {
        let fim = new Date().getTime(),
          tempo = fim - incio;

        if(tempo > 1000)
          console.log(String(`tempo pegar hash cache ${nomeMap}: ${ tempo}`))

        if(err) console.error(err)
        try{
          if( jsonObjeto[0] ) {
            return resolve(JSON.parse(jsonObjeto[0]));
          }

          return resolve(null);
        } catch (e) {
          console.error(e)
          resolve(null);
        }
      });
    });
  }

  static obtenhaCodigoValidacao(contato: any){
      if(contato && contato) return CacheService.getJson('codigoValidacao' + contato.id)
  }

  static insiraCodigoValidacao(contato: any, codigo: string, menagemEnviada: any = null){
    const chave = 'codigoValidacao' + contato.id;
    if(codigo){
      let dados: any =  { codigo: codigo,
        idContato: contato.id,
        expireIn: moment().add(10, 'm').toDate().getTime(),
        idMensagem: menagemEnviada ? menagemEnviada.id : null
      };

      let tempoCache = 60 * 60 * 4; // 4h

      CacheService.insiraJson(chave, dados, tempoCache)
    } else {
      CacheService.removaChave(chave)
    }
  }
}
