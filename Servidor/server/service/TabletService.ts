
import MapeadorDeTablet from "../mapeadores/MapeadorDeTablet";
import {MapeadorDeMesa} from "../mapeadores/MapeadorDeMesa";

import {RegistroDeOperacaoService} from "./RegistroDeOperacaoService";
import {Ambiente} from "./Ambiente";

export class TabletService {
  private registroDeOperacaoService: RegistroDeOperacaoService;
  constructor() {
  }

  async associeMesaAoTablet(numeroTablet: any, idMesa: any , empresa: any): Promise<any> {

    return new Promise( async ( resolve: any, reject: any) => {
      const mapeadorTablet = new MapeadorDeTablet();
      const mapeadorMesa = new MapeadorDeMesa();

      // Buscar mesa
      const mesa = await mapeadorMesa.selecioneSync({ id: idMesa });
      if (!mesa) return reject(`Mesa não encontrada: ${idMesa}`)


      // Buscar ou criar tablet
      let tablet = await mapeadorTablet.obtenhaTabletPorNumero(numeroTablet);

      if (!tablet)
        return  reject(`Tablet não encontrada: ${numeroTablet}`)

      // Verificar se a mesa já está associada a outro tablet
      const mesasAssociadas = await mapeadorTablet.listeAsync( { idMesa: mesa.id});

      if (mesasAssociadas && mesasAssociadas.length > 0 &&  mesasAssociadas[0].numero !== numeroTablet)
        return  reject(`Mesa "${mesa.nome}" já está associada ao tablet ${mesasAssociadas[0].numero}`)

      let mesaAnterior: any = tablet.mesa;

      tablet.mesa = mesa;
      tablet.empresa  =  empresa;
      await mapeadorTablet.atualizeMesaAssociada(tablet);

      await this.obtenhaRegistroDeOperacaoService().associouTabletAMesa(tablet, mesaAnterior);

      resolve(tablet)
    })

  }


  private obtenhaRegistroDeOperacaoService(): RegistroDeOperacaoService {
    if(!this.registroDeOperacaoService) this.registroDeOperacaoService = new RegistroDeOperacaoService(
      Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip()
    )

    return this.registroDeOperacaoService
  }

}
