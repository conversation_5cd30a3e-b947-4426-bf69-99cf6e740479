import { TaxaDeEntregaCalculada } from '../domain/delivery/TaxaDeEntregaCalculada';
import {Empresa} from "../domain/Empresa";
import {ObjetoPedidoBot} from "../utils/bot/ObjetoPedidoBot";
import {FormaDeEntregaBot} from "../utils/bot/FormaDeEntregaBot";
import {MapeadorDeFormaDePagamento} from "../mapeadores/MapeadorDeFormaDePagamento";
import {FormaDePagamento} from "../domain/delivery/FormaDePagamento";
import {ZonaDeEntrega} from "../domain/delivery/ZonaDeEntrega";
import {EnderecoBot} from "../utils/bot/EnderecoBot";
import {Endereco} from "../domain/delivery/Endereco";
import {FormaDeEntregaEmpresa} from "../domain/delivery/FormaDeEntregaEmpresa";
import {Resposta} from "../utils/Resposta";
import {FormaDePagamentoBot} from "../utils/bot/FormaDePagamentoBot";
import {Pedido} from "../domain/delivery/Pedido";
import {PedidoUtils} from "../utils/PedidoUtils";
import {PedidoService} from "./PedidoService";
import {ComunicadorDialogFlow} from "./ComunicadorDialogFlow";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {Produto} from "../domain/Produto";
import {ItemProdutoBot} from "../utils/bot/ItemProdutoBot";
import {AdicionalDeProduto} from "../domain/delivery/AdicionalDeProduto";
import {MapeadorDeConfigMensagemDeBot} from "../mapeadores/MapeadorDeConfigMensagemDeBot";
import {TipoDeMensagemBotEnum} from "../domain/chatbot/TipoDeMensagemBotEnum";
import {ConfigMensagemDeBot} from "../domain/chatbot/ConfigMensagemDeBot";
import {Notificacao} from "../domain/Notificacao";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import * as async from "async";
import {MapeadorDeNotificacao} from "../mapeadores/MapeadorDeNotificacao";

const _  = require('underscore');
let redis = require("redis");
let clientRedis = redis.createClient();

export class BotsService {
  public static MSG_RUA = `Informe *apenas* o nome da rua, avenida do local de entrega`;
  public static MSG_RUA_OU_CEP = `Informe *apenas* o nome da rua, avenida do local de entrega ou seu *CEP* ou
sua localização 📍`

  public static Intents = {
    INFORMAR_ZONA_DE_ENTREGA: 'Informar Zona de Entrega',
    FORMA_DE_ENTREGA: 'Forma de Entrega',
    Endereco: 'Endereco',
    EnderecoConfirmarSim: 'Endereco - Confirmar Sim',
    PagamentoOutrasOpcoes: 'Pagamento Outras Opcoes'
  };

  public static Contextos = {
    ZONA_ENTREGA: 'ZonaDeEntrega',
    EscolhiReceberEmCasa: 'EscolhiReceberEmCasa',
    PagamentoDinheiroFollowup: 'PagamentoDinheiro-followup',
    ReceberEmCasaOuRetirar: 'ReceberEmCasaOuRetirar',
    FormaDePagamento: 'FormaDePagamento',
    InformeCidade: 'InformeCidade'
  };

  public static Mensagens = {
    MSG_RUA_OU_CEP: `Informe *apenas* o nome da rua, avenida do local de entrega ou seu *CEP* ou
sua localização 📍?`,
    BAIRRO_INVALIDO: 'Bairro inválido. Escolha um dos bairros.'
  }

  obtenhaDados(conv: any): any {
    return conv.data;
  }

  obtenhaPedido(conv: any): any {
    return ObjetoPedidoBot.recrie(conv.data.pedido)
  }

  obtenhaEmpresa(conv: any): Empresa {
    return conv.reqExpress.empresa;
  }

  obtenhaUser(conv: any): any {
    return conv.reqExpress.user;
  }

  obtenhaContato(conv: any): any {
    return conv.body.originalDetectIntentRequest.payload;
  }

  salvePedido(conv: any, pedido: ObjetoPedidoBot, salvarNoRedis: boolean = true) {
    conv.data.pedido = pedido;

    for( let i = 0; i < pedido.itens.length; i++ ) {
      const item = pedido.itens[i];

      if( !item.adicionais ) {
        console.log('aqui');
      }
    }

    const contato = this.obtenhaContato(conv);
    const empresa = this.obtenhaEmpresa(conv);

    if( salvarNoRedis ) {
      ComunicadorDialogFlow.Instance.salvePedido(empresa, contato, pedido);
    }
  }

  async obtenhaPedidoServer(conv: any, pedido: ObjetoPedidoBot): Promise<Pedido> {
    const empresa: Empresa = this.obtenhaEmpresa(conv);
    const usuario: any = this.obtenhaUser(conv);

    const objetoDados: any = pedido;
    objetoDados.empresa = {
      id: empresa.id,
      nome: empresa.nome,
      formasDeEntrega: empresa.formasDeEntrega
    };
    objetoDados.contato = this.obtenhaContato(conv);

    objetoDados.formaDeEntrega = objetoDados.entrega.obtenhaForma();

    const dados = await new PedidoService().obtenhaPedidoPelosDados(empresa, objetoDados);

    return dados;
  }

  private valide(configMensagem: ConfigMensagemDeBot): Promise<string> {
    return new Promise<string>( (resolve) => {
      if ( !configMensagem ) {
        return resolve('Nenhuma configuração informada.');
      }

      if (!configMensagem.mensagem) { return resolve('Configuração de Mensagem deve ter uma mensagem'); }

      let qtdeMaximaCaracteres = 1000;

      if ( configMensagem.mensagem.length > qtdeMaximaCaracteres ) {
        return resolve('Mensagem muito longa.');
      }

      resolve(null);
    });
  }

  atualize(configMensagem: ConfigMensagemDeBot): Promise<boolean> {
    return new Promise<boolean>( (resolve, reject) => {
      async.series([
        (cb: Function) => {
          console.log('validando');
          // @ts-ignore
          this.valide(configMensagem).then( cb );
        },
        (cb: Function) => {
          console.log('inserindo');
          new MapeadorDeConfigMensagemDeBot().atualizeSync(configMensagem).then((inseriu: any) => { cb(); });
        }
      ], (erro: any)   => {
        if ( erro ) { return reject(erro); }

        resolve(null);
      });
    });
  }

  obtenhaMensagemMenuPrincipal(empresa: Empresa, contato: any) {
    return new Promise<string>(resolve => {
      const mapeadorDeMensagemBot = new MapeadorDeConfigMensagemDeBot()

      mapeadorDeMensagemBot.selecioneSync({tipoDeMensagem: TipoDeMensagemBotEnum.MensagemMenuPrincipal, idEmpresa: empresa.id}).then(
        (mensagemDeBot: ConfigMensagemDeBot) => {
          if(!mensagemDeBot) {
            let novaMensagem = ConfigMensagemDeBot.Nova(ConfigMensagemDeBot.MensagemMenuPrincipal, empresa,
              TipoDeMensagemBotEnum.MensagemMenuPrincipal, true )

            mapeadorDeMensagemBot.insiraGraph(novaMensagem).then(() => {
              this.processeMensagem(novaMensagem, empresa, contato, resolve)
            })

          }
          else
            this.processeMensagem(mensagemDeBot, empresa, contato, resolve);
      })
    })
  }


  private processeMensagem(mensagemDeBot: ConfigMensagemDeBot, empresa: Empresa, contato: any, resolve: (value?: unknown) => void) {
    mensagemDeBot.processe(empresa, contato).then((mensagemProcessada) => {
      resolve(mensagemProcessada.mensagemFinal)
    });
  }

  async confirmarPedido(conv: any, pedido: ObjetoPedidoBot) {
    conv.contexts.set('ConfirmarPedido', 1);

    const pedidoServer: Pedido = await this.obtenhaPedidoServer(conv, pedido);

    const contato = this.obtenhaContato(conv);
    let msgConfirmacao = PedidoUtils.obtenhaMensagemConfirmacao(contato, pedidoServer);

    conv.contexts.set('ConfirmarPedido', 1);
    conv.ask(msgConfirmacao + `.
*1* - Sim
*2* - Não`);

    this.salvePedido(conv, pedido);
  }

  processeFallback(conv: any, botEntendeu: boolean): boolean {
    const dados = this.obtenhaDados(conv);

    let qtdeFallbackSeguidos = dados.fallback;

    if( !qtdeFallbackSeguidos ) {
      qtdeFallbackSeguidos = 0;
    }

    if( botEntendeu ) {
      qtdeFallbackSeguidos = 0;
    } else {
      qtdeFallbackSeguidos ++;
    }

    if( qtdeFallbackSeguidos > 2 ) {
      const empresa: Empresa = this.obtenhaEmpresa(conv);

      conv.ask(`Percebemos que você está tendo dificuldades para montar o seu pedido comigo.
Por isso a conversa foi encerrada. Você também pode fazer o pedido pelo nosso cardápio online.

${empresa.obtenhaLinkLoja(true)}
`);

      conv.contexts.set('ConversaEncerrada', 1);
      return false;
    }

    dados.fallback = qtdeFallbackSeguidos;

    return true;
  }

  processeIntente(cb: Function) {
    return (conv: any, dadosExtras: any) => {
      const botEntendeu = conv.intent.indexOf('Fallback') === -1;

      const deveContinuar = this.processeFallback(conv, botEntendeu)

      if( !deveContinuar ) { //encerrar conversa
        return;
      }

      return new Promise<void>( (resolve, reject) => {
        let resposta: any = null;

        try {
          resposta = cb(conv, dadosExtras);
        } catch( erro ) {
          console.log(erro);
        }

        Promise.resolve(resposta).then((objResposta) => {
          const obj = resposta;
          resolve();
        }).catch( (erro) => {
          const empresa = this.obtenhaEmpresa(conv);
          conv.ask(`${erro}
Por isso a conversa foi encerrada. Você também pode fazer o pedido pelo nosso cardápio online.

${empresa.obtenhaLinkLoja(true)}`);
          conv.contexts.set('ConversaEncerrada', 1);

          resolve();
        });
      });
    }
  };

  FormaDeEntrega() {
    return this.processeIntente((conv: any, {opcaoEscolhida}: any) => {
      return this.CalculeFormaDeEntrega(conv, opcaoEscolhida, '');
    });
  }

  CalculeFormaDeEntrega(conv: any, opcaoEscolhida: any, mensagem: string = '') {
    return new Promise<void>( (resolve, reject) => {
      const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);
      const empresa: Empresa = this.obtenhaEmpresa(conv);

      if (opcaoEscolhida === '1') {
        const formaDeEntrega = empresa.obtenhaFormaReceberEmCasa();

        pedido.entrega.formaDeEntrega = FormaDeEntregaBot.RECEBER_EM_CASA;

        conv.contexts.set(BotsService.Contextos.EscolhiReceberEmCasa, 1);
        conv.ask(`${mensagem}${BotsService.Mensagens.MSG_RUA_OU_CEP}`);
        this.salvePedido(conv, pedido);

        resolve();
      } else if( opcaoEscolhida === '2' ) {
        pedido.entrega.formaDeEntrega = FormaDeEntregaBot.RETIRAR;
        pedido.endereco = null;

        if( this.PedidoEstahSendoAlterado(conv) && pedido.pagamento.formaDePagamento ) {
          this.confirmarPedido(conv, pedido).then( () => {
            resolve();
          }).catch( (erro) => {
            reject(erro);
          });

          return;
        }

        const textoFormasDePagamento = this.obtenhaMensagemPedirFormasDePagamento(pedido, conv);

        conv.ask(`${mensagem}Anotado. Qual vai ser a forma de pagamento?
${textoFormasDePagamento}`);

        conv.contexts.set('FormaDePagamento', 1);
        this.salvePedido(conv, pedido);
        resolve();
      } else {
        this.CalculeMensagemPerguntarFormaDeEntrega(empresa, conv, pedido, '').then( () => {
          resolve();
        });
      }
    });
  }

  PedidoEstahSendoAlterado(conv: any) {
    return conv.contexts.get('alterandopedido') != null;
  }

  InformarZonaDeEntrega() {
    return this.processeIntente((conv: any, {zonaEscolhida}: any) => {
      return new Promise<void>( (resolve, reject) => {
        const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);
        const empresa: Empresa = this.obtenhaEmpresa(conv);
        const zona: number = +zonaEscolhida;
        const formaDeEntrega = empresa.obtenhaFormaReceberEmCasa();

        const qtdeDeZonas = formaDeEntrega.zonasDeEntrega.length;

        if( zona <= 0 || zona > qtdeDeZonas ) {
          conv.contexts.set(BotsService.Contextos.ZONA_ENTREGA, 1);
          conv.ask(BotsService.Mensagens.BAIRRO_INVALIDO);

          resolve();
          return;
        }

        if( !pedido.endereco ) {
          pedido.endereco = EnderecoBot.novo();
        }

        const zonasOrdenadasPorNome = formaDeEntrega.zonasDeEntrega.sort( (zona1: ZonaDeEntrega, zona2: ZonaDeEntrega) => {
          return zona1.nome.localeCompare(zona2.nome);
        });

        const objZona = zonasOrdenadasPorNome[zona - 1];

        pedido.endereco.bairro = objZona.nome + '';
        pedido.endereco.zona = objZona;

        if( !pedido.endereco.cidade ) {
          conv.ask(`Bairro escolhido *${objZona.nome}*.
E qual sua cidade?`);
          conv.contexts.set('InformeCidade', 1);

          this.salvePedido(conv, pedido);

          resolve();
          return;
        }

        conv.ask(`Bairro escolhido *${objZona.nome}*.
*${pedido.endereco.obtenhaEnderecoCompleto()}*.
Confirma seu endereço?
*1* - Sim
*2* - Não`);
        conv.contexts.set('ConfirmarEndereco', 1);

        this.salvePedido(conv, pedido);

        resolve();
      });
    });
  }

  EnderecoConfirmarSim() {
    return this.processeIntente(async (conv: any) => {
      try {
        const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);
        const empresa: Empresa = this.obtenhaEmpresa(conv);

        let forma: any = _.find(empresa.formasDeEntrega, (formaCorrente: any) =>
          pedido.entrega.formaDeEntrega === (formaCorrente.nome)
        );

        const endereco = new Endereco(null, null, pedido.endereco.cidade,
          pedido.endereco.cep, pedido.endereco.logradouro, pedido.endereco.complemento, pedido.endereco.bairro, pedido.endereco.numero,
          pedido.endereco.descricao, '');

        const formaDeEntrega = FormaDeEntregaEmpresa.novaDaCache(forma)


        const respFrete: TaxaDeEntregaCalculada = await formaDeEntrega.calcule(empresa, endereco, pedido.total, pedido.endereco.zona);

        if( !respFrete.sucesso ) {
          conv.ask(respFrete.erro);
          return;
        }

        pedido.taxaEntrega = respFrete.valor;

        pedido.calculeTotal();

        const formasDePagamento = this.obtenhaMensagemPedirFormasDePagamento(pedido, conv);

        if( formasDePagamento ) {
          conv.ask(`Endereço confirmado. O valor da taxa de entrega é ${this.exibaPreco(pedido.taxaEntrega)}
Sub-Total: *${this.exibaPreco(pedido.subtotal)}*
Total: *${this.exibaPreco(pedido.total)}*
Informe qual vai ser a forma de pagamento?
${formasDePagamento}`);

          this.salvePedido(conv, pedido);
        }
      } catch( erro ) {
        conv.ask(`Não consegui confirmar seu endereço. Um atendente vai te ajudar para completar.`);
        console.log('erro');
        console.log(erro);
      }
    });
  }

  exibaPreco(valor: number) {
    return `R$ ${valor.toFixed(2).replace('.', ',')}`;
  }

  PagamentoOutrasOpcoes() {
    return this.processeIntente((conv: any, {opcaoEscolhida}: any) => {
      return new Promise<void>( async (resolve, reject) => {
        let opcao: number = +(opcaoEscolhida + '');
        const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);

        const mapeadorDePagamento = MapeadorDeFormaDePagamento.Novo();

        mapeadorDePagamento.listeAsync({naEntrega: true}).then( async (formasDePagamento: Array<FormaDePagamento>) => {
          if( opcao > formasDePagamento.length ) {
            conv.ask(`Opção inválido.`);
            resolve();
            return;
          }

          const forma: FormaDePagamento = formasDePagamento[opcao - 1];

          pedido.pagamento.formaDePagamento = new FormaDePagamentoBot(forma.id, forma.nome,
            forma.descricao);

          if( forma.nome === 'dinheiro' ) {
            conv.ask(`E você vai precisar de troco?
*1* - Sim
*2* - Não`);
            conv.contexts.set(BotsService.Contextos.PagamentoDinheiroFollowup, 1);

            this.salvePedido(conv, pedido);
            resolve();

            return;
          }

          this.confirmarPedido(conv, pedido).then( () => {
            resolve();
          }).catch( (erro) => {
            reject(erro);
          });
        });
      });
    })
  }

  NovoPedidoEscolheuTodosProdutos() {
    return this.processeIntente((conv: any) => {
      const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);
      const empresa: Empresa = this.obtenhaEmpresa(conv);

      if( pedido.itens.length === 0 ) {
        conv.ask(`Seu carrinho ainda não tem produtos. Informe um código de produto válido. *Encerrar* - Para encerrar.`);
        conv.contexts.set('NovoPedido-EscolhaProduto', 1);

        return;
      }

      return this.CalculeMensagemPerguntarFormaDeEntrega(empresa, conv, pedido,
        `Ótimo. Seu carrinho agora está assim:

${pedido.obtenhaItensDescricao()}

`);
    });
  }

  ConfirmarPedidoNaoPagamento() {
    return this.processeIntente((conv: any, {opcaoEscolhida}: any) => {
      return new Promise<void>( (resolve, reject) => {
        const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);

        const formasDePagamento = this.obtenhaMensagemPedirFormasDePagamento(pedido, conv);

        if( formasDePagamento ) {
          conv.ask(`OK. Informe qual vai ser a forma de pagamento?
${formasDePagamento}`);
          conv.contexts.set('FormaDePagamento', 1);
          resolve();
        }
      });
    });
  }

  Endereco() {
    return this.processeIntente((conv: any) => {
      return this.pergunteEndereco(conv, '');
    });
  }

  pergunteEndereco(conv: any, mensagem: string) {
    const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);
    const endereco: any = conv.parameters.endereco;

    const logradouro = endereco;

    if( !logradouro ) {
      conv.ask(mensagem + BotsService.MSG_RUA_OU_CEP);
      conv.contexts.set('EscolhiReceberEmCasa', 1);
      return;
    }

    if( !pedido.endereco ) {
      pedido.endereco = EnderecoBot.novo();
    }

    pedido.endereco.logradouro = logradouro;

    conv.ask(mensagem + 'Informe o número do local de entrega. 0 - Se não tiver número:');
    conv.contexts.set('EnderecoNumero', 1);

    this.salvePedido(conv, pedido);
  }

  CalculeMensagemPerguntarFormaDeEntrega(empresa: any, conv: any, pedido: ObjetoPedidoBot, mensagem: string) {
    const formas: Array<any> = [];
    const formaRetirada = empresa.obtenhaFormaEntregaRetirada();
    const formaReceberEmCasa = empresa.obtenhaFormaReceberEmCasa();

    empresa.formasDeEntrega.forEach( (forma: FormaDeEntregaEmpresa, indice: number) => {
      if( forma.ativa )
        formas.push(`*${formas.length + 1}*. ${forma.formaDeEntrega.nome}`);
    });

    if( formas.length === 2 ) {
      conv.contexts.set(BotsService.Contextos.ReceberEmCasaOuRetirar, 1);

      conv.ask(`${mensagem}Você prefere receber em casa ou retirar aqui?
*1*. Receber em casa
*2*. Retirar`);

      this.salvePedido(conv, pedido);

      return;
    }

    if( formaReceberEmCasa.ativa ) {
      return this.CalculeFormaDeEntrega(conv, '1', mensagem);
    }

    return this.CalculeFormaDeEntrega(conv, '2', mensagem);
  }

  obtenhaMensagemPedirFormasDePagamento(pedido: ObjetoPedidoBot, conv: any) {
    const empresa: any = this.obtenhaEmpresa(conv);

    const formas = [];

    let indice = 0;
    for( let i = 0; i < empresa.formasDePagamento.length; i++ ) {
      const forma: FormaDePagamento = empresa.formasDePagamento[i];

      if( !forma.exibirCardapio ) continue;

      indice ++;
      formas.push(`*${indice}*. ${forma.descricao}`);
    }

    return formas.join('\n');
  }

  private encerrarConversa(conv: any, empresa: Empresa) {
    conv.ask(`Houve um erro por isso a conversa foi encerrada. Você também pode fazer o pedido pelo nosso cardápio online.

${empresa.obtenhaLinkLoja(true)}
`);

    conv.contexts.set('ConversaEncerrada', 1);
  }

  obtenhaMensagemDespedida(conv: any) {
    const empresa = this.obtenhaEmpresa(conv);

    return `Ok! Você pode fazer o pedido pelo nosso cardápio online.

${empresa.obtenhaLinkLoja(true)}.`
  }

  exibaProduto(objetoPedidoBot: ObjetoPedidoBot, itemPedido: ItemProdutoBot, produto: Produto,
                        exibirDescricao: boolean) {
    const pedacos = [];

    try {
      let preposicao = 'de';
      const valorMinimo = produto.obtenhaValorMinimo();

      if( produto.preco !== valorMinimo ) {
        preposicao = 'A Partir de'
      }

      pedacos.push(`📦 *${produto.nome.trim()} ${preposicao} ${this.exibaPreco(valorMinimo)}*`);

      if( exibirDescricao && produto.descricao ) {
        pedacos.push(`_${produto.descricao.trim()}_\n`);
      }

      pedacos.push(itemPedido.obtenhaDescricaoAdicionais());

      pedacos.push(`
*Valor Produto*: ${this.exibaPreco(itemPedido.total)}`)
    } catch( erro ) {
      console.log(erro);
    }

    return pedacos.join('');
  }

  pecaQueUsuarioInformeQtde(conv: any, pedido: ObjetoPedidoBot, itemProdutoBot: ItemProdutoBot, produto: any) {
    conv.ask(`Ótimo, você escolheu
${this.exibaProduto(pedido, itemProdutoBot, produto, true)}

Quantos(as) você deseja?

*v*. Para voltar e remover produto do carrinho`);

    conv.contexts.set('NovoPedido-EscolhaProduto-followup', 1);
  }

  exibaAdicional(conv: any, pedido: ObjetoPedidoBot, itemProduto: ItemProdutoBot, produto: Produto,
                          indiceAdicional: number, msgExtra: string) {
    if( indiceAdicional >= produto.camposAdicionais.length ) {
      this.pecaQueUsuarioInformeQtde(conv, pedido, itemProduto, produto);

      return;
    }

    const pedacos = [];

    pedacos.push(`----------------------
`);

    const adicionalDeProduto: AdicionalDeProduto = produto.camposAdicionais[indiceAdicional];

    pedacos.push(`${adicionalDeProduto.nome} (${adicionalDeProduto.obtenhaDescricao()})❓`);

    let jaAdicionouAlgum = false;
    let indiceDisponiveis = 0;
    const listaAdicionais = [];
    for( let i = 0; i < adicionalDeProduto.opcoesDisponiveis.length; i++ ) {
      const opcao = adicionalDeProduto.opcoesDisponiveis[i];

      if( !opcao.disponivel ) {
        continue;
      }

      const valorCampoAdicional: any = pedido.obtenhaCampoAdicional(itemProduto, produto, adicionalDeProduto, indiceDisponiveis);

      let pedaco = '';

      if( valorCampoAdicional && valorCampoAdicional.qtde > 0 ) {
        pedaco = ` *(adicionou ${valorCampoAdicional.qtde})*`;
        jaAdicionouAlgum = true;
      }

      listaAdicionais.push(`\n*${ indiceDisponiveis + 1}.* ${opcao.nome} ${this.exibaPreco(opcao.valor)}${pedaco}`);

      indiceDisponiveis ++;
    }

    if( adicionalDeProduto.podeIrParaProximo(indiceAdicional) ) {
      pedacos.push('\n\n*0* - Seguir em frente');
    }

    let pedacoAdicionais = listaAdicionais.join('');

    if( jaAdicionouAlgum ) {
      pedacoAdicionais = '';
    }
    conv.ask(`${msgExtra}
${this.exibaProduto(pedido, itemProduto, produto, jaAdicionouAlgum)}

${pedacos.join('')}
${pedacoAdicionais}

*v*. Para voltar e remover produto do carrinho
`);

    conv.contexts.set('NovoPedido-Adicionais', 1);
  }

  NovoPedidoEscolhaProduto() {
    return (conv: any, {codigoDoProduto, nomeProduto}: any) => {
      if (nomeProduto) {
        const nomeProdutoSemCase = nomeProduto.toString().toLocaleLowerCase();

        if (nomeProdutoSemCase === 'sair' || nomeProdutoSemCase === 'encerrar') {
          const empresa = this.obtenhaEmpresa(conv);

          conv.ask(this.obtenhaMensagemDespedida(conv));
          conv.contexts.set('ConversaEncerrada', 1);
          return;
        }
      }

      return new Promise<void>((resolve, reject) => {
        const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);
        const mapeadorDeProduto = MapeadorDeProduto.Instancia(this.obtenhaEmpresa(conv).catalogo)

        const dados = this.obtenhaDados(conv);

        const MSG_PRODUTO_NAO_ENCONTRADO = `Produto não encontrado.
  Informe o *código* do produto no cardápio, *você pode buscar pelo nome* ou *0* - Para seguir em frente.
  *Encerrar* - Para cancelar o pedido`;

        if (codigoDoProduto) {
          dados.codigoProduto = codigoDoProduto;

          mapeadorDeProduto.selecioneSync({ordem: codigoDoProduto, temEstoque: true}).then((produto: Produto) => {
            let itemProduto = null;

            if (produto) {
              if (produto.camposAdicionais && produto.camposAdicionais.length > 0) {
                this.obtenhaDados(conv).indiceAdicional = 0;
                const msgEscolheuProduto = `<<${produto.imagens && produto.imagens.length > 0 ?
                  produto.imagens[0].linkImagem : null}>> Ótimo, você escolheu`;

                dados.indiceProduto = pedido.adicione(produto, 1, {});

                itemProduto = pedido.obtenhaPorIndice(dados.indiceProduto);

                this.exibaAdicional(conv, pedido, itemProduto, produto, this.obtenhaDados(conv).indiceAdicional, msgEscolheuProduto);

                this.salvePedido(conv, pedido, false);

                resolve();
                return;
              }

              dados.indiceProduto = pedido.adicione(produto, 1, {});

              itemProduto = pedido.obtenhaPorIndice(dados.indiceProduto);

              this.pecaQueUsuarioInformeQtde(conv, pedido, itemProduto, produto);
            } else {
              const deveContinuar = this.processeFallback(conv, false);

              if (!deveContinuar) {
                resolve();
                return;
              }

              conv.ask(MSG_PRODUTO_NAO_ENCONTRADO);

              conv.contexts.set('NovoPedido-EscolhaProduto', 1);
            }

            this.processeFallback(conv, true);
            this.salvePedido(conv, pedido, false);

            resolve();
          });
        } else {
          mapeadorDeProduto.listeAsync({
            termo: '%' + nomeProduto + '%', temEstoque: true,
            inicio: 0, total: 5
          }).then((produtos: Array<Produto>) => {
            if (!produtos || produtos.length === 0) {
              const deveContinuar = this.processeFallback(conv, false);

              if (!deveContinuar) {
                resolve();
                return;
              }

              conv.ask(MSG_PRODUTO_NAO_ENCONTRADO);
              conv.contexts.set('NovoPedido-EscolhaProduto', 1);

              resolve();
              return;
            }

            const pedacos = [`Segue os produtos encontrados com *${nomeProduto}*. Se for o produto que deseja, informe o código:
`];

            for (let i = 0; i < produtos.length; i++) {
              const produto = produtos[i];

              pedacos.push(`*${produto.ordem}*. ${produto.nome} por ${this.exibaPreco(produto.preco)}`)
            }

            conv.ask(pedacos.join('\n') + `

Caso não seja, você pode buscar novamente ou digitar o código de algum produto do cardápio.`);
            conv.contexts.set('NovoPedido-EscolhaProduto', 1);

            resolve();
          });
        }
      });
    };
  }

  NovoPedidoCancelarEscolhaProduto() {
    return this.processeIntente((conv: any, {opcaoEscolhida}: any) => {
      return new Promise<void>( (resolve, reject) => {
        const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);
        const dados = this.obtenhaDados(conv);

        pedido.removaPorIndice(dados.indiceProduto);

        conv.ask(`Produto removido do carrinho.
Agora, escolha o *código do produto* ou você pode *buscar pelo nome*.`);
        conv.contexts.set('NovoPedido-EscolhaProduto', 1);

        this.salvePedido(conv, pedido);

        resolve();
      });
    });
  }

  PagamentoDinheiroTrocoSim() {
    return this.processeIntente((conv: any) => {
      return new Promise<void>( (resolve, reject) => {
        const pedido: ObjetoPedidoBot = this.obtenhaPedido(conv);
        const dados = this.obtenhaDados(conv);

        conv.ask(`Troco pra quanto? (Valor do Pedido: ${this.exibaPreco(pedido.obtenhaSubTotal())})`);
        conv.contexts.set('NovoPedido-EscolhaProduto', 1);

        this.salvePedido(conv, pedido);

        resolve();
      });
    });
  }
}
