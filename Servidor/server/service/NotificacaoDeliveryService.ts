import {NotificacaoDelivery} from "../domain/integracoes/NotificacaoDelivery";
import {EnumStatusEntregaLogistic} from "../lib/integracao/opendelivery/EnumStatusEntregaLogistic";
import {Pedido} from "../domain/delivery/Pedido";
import {EnumStatusPedido} from "../lib/emun/EnumStatusPedido";
import {PedidoService} from "./PedidoService";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeDeliveryPedido} from "../mapeadores/MapeadorDeDeliveryPedido";
import {DeliveryPedido} from "../domain/integracoes/DeliveryPedido";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {EnumStatusEntregaUber} from "../lib/integracao/opendelivery/EnumStatusEntregaUber";

import {NotificacaoIfoodDelivery} from "../domain/integracoes/NotificacaoIfoodDelivery";
import {OrderEvent} from "../domain/opendelivery/OrderEvent";
import {EventType} from "../lib/integracao/opendelivery/EventType";
import {DeParaEventsStatusDeliveryPedido, EnumStatusEntregaIfood} from "../lib/integracao/ifood/EnumStatusEntregaIfood";
import {PedidoAlteracaoEndereco} from "../domain/integracoes/PedidoAlteracaoEndereco";
import {Endereco} from "../domain/delivery/Endereco";
import {DeliveryPedidoIfood} from "../domain/integracoes/DeliveryPedidoIfood";
import {EnumStatusPedidoFoodyDelivery} from "./EnumStatusPedidosIntegrados";

export const MapStatusPedidoDelivery  = new Map<any, EnumStatusPedido>([
  [EnumStatusEntregaLogistic.PedidoColetado,  EnumStatusPedido.SaiuParaEntrega],
  [EnumStatusEntregaLogistic.PedidoEntregue,  EnumStatusPedido.Entregue],
  [EnumStatusEntregaLogistic.EntregaFinalizada,  EnumStatusPedido.Entregue],

  [EnumStatusEntregaUber.PedidoColetado, EnumStatusPedido.SaiuParaEntrega],
  [EnumStatusEntregaUber.EntregaAcaminho, EnumStatusPedido.SaiuParaEntrega],
  [EnumStatusEntregaUber.PedidoEntregue, EnumStatusPedido.Entregue],

  [EnumStatusPedidoFoodyDelivery.SaiuParaEntrega, EnumStatusPedido.SaiuParaEntrega],
  [EnumStatusPedidoFoodyDelivery.Entregue, EnumStatusPedido.Entregue]
])

const errosIgnorar: any = [];

export class NotificacaoDeliveryService{
  constructor() {
  }

  static executeNotificacao(notificacao: NotificacaoDelivery, pedido: Pedido){
    return new Promise<void>( async(resolve, reject) => {
      try{
        let tipoEvento = notificacao.tipo;
        let contexto = require('domain').active.contexto;
        contexto.empresa = pedido.empresa;
        contexto.idEmpresa = contexto.empresa.id;

        const vinculadoPedido = pedido.deliveryPedido && pedido.deliveryPedido.deliveryId === notificacao.deliveryId;

        let pedidoEntrega: DeliveryPedido = vinculadoPedido ?  pedido.deliveryPedido : null

        if(!pedidoEntrega)
          pedidoEntrega = await new MapeadorDeDeliveryPedido().selecioneSync({ deliveryId: notificacao.deliveryId})

        if(!pedidoEntrega)  throw Error('Delivery id nao encontrado: ' + notificacao.deliveryId)

        await pedidoEntrega.atualizeRetorno(tipoEvento, notificacao.getDados())

        let novoStatusPedido = MapStatusPedidoDelivery.get(tipoEvento)

        if (novoStatusPedido) {
          if(vinculadoPedido){
            contexto.empresa = await new MapeadorDeEmpresa().selecioneSync(pedido.empresa.id);
            contexto.idEmpresa = contexto.empresa.id;

            let erroAlterar: any =
              await new PedidoService().altereStatus(pedido, contexto.empresa, novoStatusPedido,
                true, true, null, null, null);

            if (erroAlterar) throw Error(erroAlterar)
          }
        } else {
          if(vinculadoPedido){
            pedido.horarioAtualizacao = new Date();
            await new MapeadorDePedido().atualizeHorario(pedido)
          }
        }

        await notificacao.foiExecutada( );
      } catch (e) {
        console.error(e)
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }

      resolve();

    });
  }

  static async executeIfoodDelivery(notificacao: NotificacaoIfoodDelivery,  ifoodService: any){
    if(!ifoodService.integracaoIfood.shippingApi) {
      console.log('ignorar notificação, shippingApi não foi ativada no promokit')
      return;
    }

    if(!notificacao.pedidoId)  throw Error ('Nenhum pedido associado a notificaçao');

    let pedido: any = await new MapeadorDePedido().selecioneSync(notificacao.pedidoId);

    if(!pedido) throw Error ('Nenhum pedido com esse id: ' + notificacao.pedidoId);

    if(pedido.deliveryPedido ){
      if(notificacao.notificaoCancelamentoDelivery() ) {
        let orderEvent: OrderEvent  =
          await OrderEvent.get({orderId: pedido.guid, tipo: EventType.DELIVERY_CANCELLATION_REQUESTED});

        let dados: any = notificacao.getDados();

        if(notificacao.cancelamentoDeliveryAceito()){
          await  pedido.deliveryPedido.atualizeRetorno(EnumStatusEntregaIfood.Cancelado, dados
            , '')
        } else if(notificacao.cancelamentoDeliveryNegado()) {
          let motivoCanacelamento =  dados.reasonDeny || 'Ifood recusou cancelamento';
          if(orderEvent){ //pdv aguardando resposta ifood
            orderEvent.reasonDeny = motivoCanacelamento;
            await orderEvent.mapeador().atualizeRetorno(orderEvent);
          }
        }
      } else if(notificacao.notificaoAlteracaoEnderecoDelivery()) {
        if(notificacao.enderecoEntregaConfirmado()){
          await pedido.deliveryPedido.clienteConfirmouEndereco();
        } else {
          let dados = notificacao.getDados();

          if(notificacao.alteracaoEnderecoSolicitada()){
            let deliveryAlteracaoEndereco: any =
              new PedidoAlteracaoEndereco(notificacao.eventId, pedido, notificacao.deliveryId, dados);
            await deliveryAlteracaoEndereco.insiraGraph();

          } else {
            if(!pedido.alteracaoEndereco || pedido.alteracaoEndereco.jaRespondeu()){
              console.log('Alteraçao endereço soliticada confirmada ou negada')
            } else {
              if(notificacao.alteracaoEnderecoAceita()){
                let novoEndereco = Endereco.novoIfood(pedido.contato, pedido.alteracaoEndereco.getEndereco())

                await new PedidoService().altereEnderecoEntrega(pedido, novoEndereco, null);
              } else if(notificacao.alteracaoEnderecoNegada()){
                await new PedidoService().registreAlteracaoPedidoNegada(pedido, null)
              }
            }
          }
        }
      } else if( notificacao.codigoConfirmacaoDelivery() ){
        let dados = notificacao.getDados();
        await pedido.deliveryPedido.atualizeRetorno(pedido.deliveryPedido.status, dados)
      } else {
        let dados = notificacao.getDados();

        let novoStatus = DeParaEventsStatusDeliveryPedido.get(notificacao.tipo);

        if(novoStatus){
          await pedido.deliveryPedido.atualizeRetorno(novoStatus, dados);
        } else {
          notificacao.ignorar = true;
          throw Error ('Status Ifood Delivery ignorado: ' + notificacao.tipo);
        }
      }
    } else {
      if(notificacao.entregadorAlocado() && pedido.doIfood()){
        let order: any  = {id: pedido.referenciaExterna};
        let novoDevivery = new DeliveryPedidoIfood(pedido, null, order);
        novoDevivery.setRetorno(EnumStatusEntregaIfood.EntregadorAlocado, notificacao.getDados());
        await novoDevivery.salve();
      } else {
        throw Error ('Nenhum delivery associado ao pedido: ' + pedido.id);
      }
    }

    await notificacao.foiExecutada();
  }


}
