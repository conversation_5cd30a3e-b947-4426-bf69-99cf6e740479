
let randomstring = require("randomstring");

export class CookieRememberMe {
  static encodeBase64(texto: string): string {
    return Buffer.from(texto).toString('base64')
  }

  static decodeBase64(texto: string): string {
    return Buffer.from(texto, 'base64').toString('ascii')
  }
  static crieCookie(idUsuario : string): CookieRememberMe {
    let cookie = new CookieRememberMe();
    cookie.lookup = CookieRememberMe.encodeBase64(idUsuario);
    cookie.validator= CookieRememberMe.encodeBase64(randomstring.generate(18));
    return cookie
  }

  lookup: string;
  validator: string;

  toCookieString() {
    return this.lookup + ':' + this.validator;
  }

  static fromCookieString(cookieString: string) {
    if(!cookieString) return null;
    let partesCookie = cookieString.split(':');

    if ( partesCookie.length != 2 ) return null;

    let cookie = new CookieRememberMe();

    cookie.lookup = partesCookie[0];
    cookie.validator = partesCookie[1];

    return cookie;
  }
}
