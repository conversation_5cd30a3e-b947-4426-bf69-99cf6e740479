import {IEnviadorDeMensagem} from "./IEnviadorDeMensagem";
import {SituacaoDeMensagem} from "./SituacaoDeMensagem";
import {StatusDeMensagem} from "./StatusDeMensagem";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {Empresa} from "../domain/Empresa";

export class EnviadorDeMensagemMock implements IEnviadorDeMensagem {
  static idMensagem = 1;

  acompanheMensagem(idMensagem: string): Promise<SituacaoDeMensagem> {
      const situacao = new SituacaoDeMensagem();

      situacao.id = idMensagem
      situacao.mensagem = 'Mensagem enviada com sucesso';
      situacao.status = StatusDeMensagem.Enviada;

    return new Promise(resolve => {
      resolve(situacao);
    });
  }

  envieSMS(telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    return new Promise<SituacaoDeMensagem>((resolve) => {
      const situacaoDeMensagem = new SituacaoDeMensagem();

      situacaoDeMensagem.status = StatusDeMensagem.Nova;
      situacaoDeMensagem.sucesso = true;

      resolve(situacaoDeMensagem);
    });
  }

  envieMensagem(mensagemEnviada: MensagemEnviada, telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    return new Promise(resolve => {
      const situacao = new SituacaoDeMensagem()

      situacao.id = (EnviadorDeMensagemMock.idMensagem++).toString();
      situacao.mensagem = 'Mensagem enviada com sucesso';
      situacao.status = StatusDeMensagem.Enviada

      console.log( 'Enviando mensagem ' + mensagem + ' para o telefone ' + telefone);

      resolve(situacao);
    });
  }

  envieMensagemAtivacaoContato(mensagemEnviada: MensagemEnviada, telefoneContato: string, telefoneEmpresa: string): Promise<void> {
    return Promise.resolve()
  }

  requerAtivacaoDoTelefone(): boolean {
    return false; //true;
  }

  obtenhaMeioDeEnvio(): EnumMeioDeEnvio {
    return EnumMeioDeEnvio.Mock;
  }

  notifiqueAssinantes(mensagemEnviada: MensagemEnviada) {
  }

  notifiqueAssinantesEmpresa(empresa: Empresa) {
  }
}
