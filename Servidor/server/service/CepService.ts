import {Resposta} from "../utils/Resposta";
const axios = require('axios');
import { CepCustomizadoService } from './CepCustomizadoService';

export class CepService {
  async busque(cep: string, timeout: number = 5000): Promise<Resposta<any>> {
    // Buscar primeiro no banco de ceps customizados
    const customizadoService = new CepCustomizadoService();
    const customizado = await customizadoService.busquePorCep(cep);

    if (customizado) {
      //console.log(`[CepService] CEP ${cep} encontrado na base customizada:`, customizado);
      return Resposta.sucesso({
        cep: customizado.cep,
        logradouro: customizado.logradouro,
        bairro: customizado.bairro,
        cidade: customizado.cidade,
        localidade: customizado.cidade + "/" + customizado.estado,
        estado: customizado.estado,
        complemento: customizado.complemento,
        numero: customizado.numero
      });
    }

    console.log(`[CepService] CEP ${cep} não encontrado na base customizada, buscando na API externa...`);
    const resposta = await this.busqueViaCep(cep, timeout);

    if( resposta.sucesso ) {
      if( resposta.data.estado === 'PR' && resposta.data.cidade === 'Foz do Iguaçu' ) {
        resposta.data.bairro = resposta.data.bairro.replace('Itaipu ', 'Vila ');
      }
    }

    return resposta;
  }

  async busqueViaCep(cep: string, timeout: number): Promise<Resposta<any>> {
    return new Promise( async (resolve, reject) => {
      let url =  "https://viacep.com.br/ws/" + cep + "/json";

      try {
        const response = await axios.get(url, { timeout: timeout})
        console.log(response.status);
        if(response && response.status === 200){
          let resposta = response.data

          if(resposta.erro){
            resolve(Resposta.erro("Nenhum endereço encontrado."));
          } else {
            let endereco: any = { logradouro: resposta.logradouro, bairro: resposta.bairro,
              localidade: resposta.localidade + "/" + resposta.uf, cep: resposta.cep}
            endereco.cidade = resposta.localidade;
            endereco.estado = resposta.uf;
            endereco.estados = [
              {
                id: 1,
                nome: 'Goiás',
                sigla: 'GO'
              }
            ];
          //  if(resposta.complemento && endereco.logradouro.length <= 5)
           //   endereco.logradouro = String(`${endereco.logradouro} ${resposta.complemento}`)

            resolve(Resposta.sucesso( endereco))
          }
        } else {
          resolve(Resposta.erro( "Serviço de consulta de CEP indisponível no momento"))
        }
      } catch (error) {
        console.log("Nenhum endereço encontrado.")
        console.log(error)
        resolve(Resposta.erro( "Serviço de consulta de CEP indisponível no momento"))
      }

    });
  }

  async busqueViaBrasilAPI(cep: string, timeout: number): Promise<Resposta<any>> {
    return new Promise( async (resolve, reject) => {
      let url =  "https://brasilapi.com.br/api/cep/v2/" + cep;

      try {
        const response = await axios.get(url, { timeout: timeout})
        console.log(response.status);
        if(response && response.status === 200){
          let resposta = response.data

          if(resposta.erro){
            resolve(Resposta.erro("Nenhum endereço encontrado."));
          } else {
            let endereco: any = { logradouro: resposta.street, bairro: resposta.neighborhood,
              localidade: resposta.city + "/" + resposta.state, cep: resposta.cep}
            endereco.cidade = resposta.city;
            endereco.estado = resposta.state;
            endereco.estados = [
              {
                id: 1,
                nome: 'Goiás',
                sigla: 'GO'
              }
            ];
            resolve(Resposta.sucesso( endereco))
          }
        } else {
          resolve(Resposta.erro( "Serviço de consulta de CEP indisponível no momento"))
        }
      } catch (error) {
        console.log("Nenhum endereço encontrado.")
        console.log(error)
        resolve(Resposta.erro( "Serviço de consulta de CEP indisponível no momento"))
      }

    });
  }
}
