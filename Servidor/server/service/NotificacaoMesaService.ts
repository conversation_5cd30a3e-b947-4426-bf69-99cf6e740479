import {NotificacaoMesa} from "../domain/integracoes/NotificacaoMesa";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Comanda} from "../domain/comandas/Comanda";
import {ComandaService} from "./ComandaService";
import {NotificacaoMesaTransferida} from "../domain/integracoes/NotificacaoMesaTransferida";
import {MapeadorDeMesa} from "../mapeadores/MapeadorDeMesa";
import {Mesa} from "../domain/Mesa";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeComanda} from "../mapeadores/MapeadorDeComanda";
import {NotificacaoMesaCancelada} from "../domain/integracoes/NotificacaoMesaCancelada";
import {NotificacaoMesaItemRemovido} from "../domain/integracoes/NotificacaoMesaItemRemovido";

const errosIgnorar: any = [];

export class NotificacaoMesaService{
  static executeRemovaItens(notificacao: NotificacaoMesaItemRemovido){
    return new Promise<void>( async(resolve, reject) => {
      await notificacao.registreErro('Remoção item da mesa ainda não implementada', true);

      resolve();
    })
  }


  static executeContaCancelada(notificacao: NotificacaoMesaCancelada){
    return new Promise<void>( async(resolve, reject) => {
      try{
        let empresa  = await new MapeadorDeEmpresa().selecioneSync({id: notificacao.empresa.id})

        let contexto = require('domain').active.contexto;
        contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( empresa.id);
        contexto.idEmpresa =  empresa.id;

        let comanda: Comanda = notificacao.comanda;

        if(comanda){

          await  new ComandaService().canceleComanda(comanda, empresa, notificacao.getMotivo())
          await notificacao.foiExecutada();
        }
      } catch (e){
        console.error(e)
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }
      resolve();
    })
  }

  static executeTrocaDaMesa(notificacao: NotificacaoMesaTransferida){
    return new Promise<void>( async(resolve, reject) => {
       try{
         let empresa  = await new MapeadorDeEmpresa().selecioneSync({id: notificacao.empresa.id})

         let contexto = require('domain').active.contexto;
         contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( empresa.id);
         contexto.idEmpresa =  empresa.id;

         let comanda: Comanda = notificacao.comanda;
         const mapeadorDeMesa = new MapeadorDeMesa();
         if(comanda){
           let mesa: any = await mapeadorDeMesa.selecioneSync({codigoPdv: notificacao.getMesaDestino()});

           if(mesa){
             await new MapeadorDeComanda().troqueMesa(comanda, mesa);
             await notificacao.foiExecutada();
           } else {
             //cancelar a comanda para liberera mesa: ate entao nao usam mesa no promokit objeto e so liberar mesa
             await new ComandaService().canceleComanda(comanda, empresa,
               `Troca de mesa para comanda ${notificacao.getMesaDestino()} não foi processada`  )
           }
         }
       } catch (e){
        console.error(e)
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }
      resolve();
    });
  }
  static executeFecharConta(notificacao: NotificacaoMesa){

    return new Promise<void>( async(resolve, reject) => {
      try{
        let empresa  = await new MapeadorDeEmpresa().selecioneSync({id: notificacao.empresa.id})

        let contexto = require('domain').active.contexto;
        contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( empresa.id);
        contexto.idEmpresa =  empresa.id;

        let comanda: Comanda = notificacao.comanda;

        if(comanda){
          let formasPagamentoParceiro  = notificacao.getDados().formasDePagamento;

          let pagamento: any  = notificacao.getDados().pagamento;

          let pagmentos: any = [];

          if(pagamento.dinheiro > 0){
             let formaDinheiro =  empresa.formasDePagamento.find((item: any) => item.ehPagamentoEmDinheiro());

             if(!formaDinheiro) throw Error('Nenhuma forma de pagamento encontrada "dinheiro" configurada');


             pagmentos.push({formaDePagamento: formaDinheiro, valor: pagamento.dinheiro, troco: pagamento.troco})
          }

          if(formasPagamentoParceiro)  {
            formasPagamentoParceiro.forEach((formaPagamentoEcletica: any) => {
              let tipo = formaPagamentoEcletica.flag_de_pagamento,
                codigo = formaPagamentoEcletica.codigo_bandeira,
                nomeBandeira = formaPagamentoEcletica.nome_bandeira,
                valor = formaPagamentoEcletica.valor;

              let formaPagamentoPromokit: any;

              empresa.formasDePagamento.forEach((formaPagamento: any) => {
                if(formaPagamento.bandeirasCartaoIntegrada){
                  formaPagamento.bandeirasCartaoIntegrada.forEach((bandeira: any) => {
                    if(bandeira.tipo.toUpperCase() === tipo.toUpperCase()  && bandeira.codigoPdv === codigo.toString() )
                      formaPagamentoPromokit = formaPagamento
                  })
                }
              })

              if(!formaPagamentoPromokit)
                throw Error( String(`Nenhuma forma de pagamento encontrada para bandeira ${nomeBandeira}: ${tipo}/${codigo}`))

              pagmentos.push({
                formaDePagamento: formaPagamentoPromokit, valor: valor
              })

            })
          }

          if(!pagmentos.length)
            throw Error( String(`Nenhum pagamento foi identificado`))

          let resposta: any =
            await new ComandaService().fechePeloParceiro(comanda, empresa, pagmentos, pagamento.desconto, pagamento.repique).catch(
            async (err) => {
            await notificacao.registreErro(err, errosIgnorar.indexOf(err) >= 0);
          });

          if(resposta) await notificacao.foiExecutada();
          //
        }
      } catch (e){
        console.error(e)
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }
      resolve();
    })
  }
}
