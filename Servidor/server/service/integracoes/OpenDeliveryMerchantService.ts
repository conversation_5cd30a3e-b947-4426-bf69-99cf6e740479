import {EnumTipoDeOrigem} from "../../lib/emun/EnumTipoDeOrigem";
import {EnumBandeirasOpenDelivery} from "../../lib/integracao/opendelivery/EnumBandeirasOpenDelivery";

import axios from "axios";
import {IFonteProdutosExternos} from "../../domain/integracoes/IFonteProdutosExternos";
import {Produto} from "../../domain/Produto";
import {EnumDisponibilidadeProduto} from "../../lib/emun/EnumDisponibilidadeProduto";
import {Categoria} from "../../domain/delivery/Categoria";

import {AdicionalDeProdutoMultiplaEscolha} from "../../domain/delivery/AdicionalDeProdutoMultiplaEscolha";
import {AdicionalDeProdutoEscolhaSimples} from "../../domain/delivery/AdicionalDeProdutoEscolhaSimples";
import {OpcaoDeAdicionalDeProduto} from "../../domain/delivery/OpcaoDeAdicionalDeProduto";
import * as moment from "moment";
import {Empresa} from "../../domain/Empresa";
import {PedidoGenerico} from "../../domain/delivery/PedidoGenerico";
import {Disponibilidade} from "../../domain/Disponibilidade";
import {DisponibilidadePeriodo} from "../../domain/DisponibilidadePeriodo";
import {DisponibilidadePeriodoDia} from "../../domain/DisponibilidadePeriodoDia";
import {OrderEvent} from "../../domain/opendelivery/OrderEvent";
import {EventType} from "../../lib/integracao/opendelivery/EventType";
import {ExecutorAsync} from "../../utils/ExecutorAsync";

export class OpenDeliveryMerchantService implements IFonteProdutosExternos{
  merchantInstance: any;

  constructor(public merchantCredencial: any = null) {
    this.merchantInstance = axios.create({
      baseURL: this.merchantCredencial.baseUrl,
      headers:  {
        'Content-Type': 'application/json',
        'X-API-KEY': this.merchantCredencial.apiKey
      }
    });
  }

  novoPedidoAsync(pedido: PedidoGenerico, empresa: Empresa){
    let orderEvent: OrderEvent = new OrderEvent(pedido.guid, EventType.CREATED, empresa);

    ExecutorAsync.execute(  async (cbAsync: any) => {
      console.log(String(`Criar Evento Create Order pedido "${pedido.codigo}" empresa ${empresa.id}`))
      let contexto: any =  require('domain').active.contexto;

      contexto.empresa = empresa;
      contexto.idEmpresa = empresa.id;
      await orderEvent.salve(true);
      cbAsync();
    },  (err: any) => {
      console.log(err)
    }, 0)
  }

  private getHorarioMenos3(hora: string){
    return moment(hora, 'HH:mm:ss.SSS[Z]').add(-3, 'h').format('HH:mm:ss');
  }

  listeProdutosConvertidos(ultimaSincronizacao: any): Promise<Array<Produto>> {
    return new Promise( async (resolve, reject) => {
        let merchant: any = await this.obtenhaMerchant().catch((erro) => reject(erro));
        let produtos: any = [];

        if(merchant){
          merchant.services.forEach((service: any) => {
            if(service.serviceType === 'DELIVERY'){
              let menuDelivery =  merchant.menus.find((menu: any) => menu.id === service.menuId);

              menuDelivery.categoryId.forEach((categoriaId: string) => {
                let category: any = merchant.categories.find((cat: any) => cat.id === categoriaId)
                if(category){
                  let categoria: Categoria = new Categoria(null, category.name, null, null, category.externalCode,
                                category.index + 1);

                  category.itemOfferId.forEach((itemOfferId: any) => {
                      let itemOffer: any = merchant.itemOffers.find((_itemOffer: any) => _itemOffer.id === itemOfferId)
                      if(itemOffer){
                        let item: any = merchant.items.find((_item: any) => _item.id === itemOffer.itemId);

                        let produto: any = new Produto(null, item.name, itemOffer.price.value, item.description);

                        if(itemOffer.price.originalValue !== itemOffer.price.value){
                           produto.preco =  itemOffer.price.originalValue;
                           produto.setPrecoPromocional( itemOffer.price.value);
                        }

                        produto.codigoPdv = item.externalCode;
                        produto.categoria = categoria;
                        produto.ordem = itemOffer.index + 1;
                        produto.disponibilidade =  EnumDisponibilidadeProduto.SempreDisponivel;

                        if(item.status === 'UNAVAILABLE' || itemOffer.status === 'UNAVAILABLE')
                          produto.disponibilidade =   EnumDisponibilidadeProduto.NaoDisponivel


                        if(itemOffer.availabilityId && itemOffer.availabilityId.length){
                          itemOffer.availabilityId.forEach((availabilityId: string) => {
                            let availabiliti =
                              merchant.availabilities.find((_availabiliti: any) => _availabiliti.id === availabilityId)
                            if(availabiliti){
                              let dataInicio = availabiliti.startDate ? moment(availabiliti.startDate, 'MM/DD/YYYY').toDate() : null,
                                  dataFim = availabiliti.endDate ? moment(availabiliti.endDate, 'MM/DD/YYYY').toDate() : null;


                              let diasSemana: any = [ "SUNDAY", "MONDAY",  "TUESDAY",  "WEDNESDAY", "THURSDAY",  "FRIDAY", "SATURDAY"]

                              let disponiblidade = new Disponibilidade(null, dataInicio, dataFim, [], availabiliti.id)

                              if(availabiliti.hours && availabiliti.hours.length){
                                availabiliti.hours.forEach((hour: any) => {
                                  let dias: any = hour.dayOfWeek.map((day: any) => new DisponibilidadePeriodoDia(diasSemana.indexOf(day) )),
                                     horaInicioMenos3: any = this.getHorarioMenos3(hour.timePeriods.startTime),
                                    horaFimMenos3: any =  this.getHorarioMenos3(hour.timePeriods.endTime);

                                  let periodo = new DisponibilidadePeriodo(dias, horaInicioMenos3, horaFimMenos3);

                                  disponiblidade.periodos.push(periodo)

                                })
                              }

                              produto.disponibilidade =  EnumDisponibilidadeProduto.DiaDaSemana;
                              produto.disponibilidades.push(disponiblidade);
                            }
                          })
                        }

                        if(item.image && item.image.URL){
                          produto.urlImagemExterna = item.image.URL
                          produto.linkImagem =  produto.urlImagemExterna.split('/').pop()
                        }

                        if(itemOffer.optionGroupsId && itemOffer.optionGroupsId.length){
                          itemOffer.optionGroupsId.forEach((groupId: string) => {
                            let optionGroup: any = merchant.optionGroups.find((group: any) => group.id === groupId),
                                obrigatorio = optionGroup.minPermitted >= 1,
                                grupoDisponivel = optionGroup.status === 'AVAILABLE' ? true  : false,
                                adicionalProduto: any;


                            if(optionGroup.maxPermitted > 1){
                               adicionalProduto = new AdicionalDeProdutoMultiplaEscolha(optionGroup.name,  obrigatorio, [],
                                optionGroup.minPermitted,  optionGroup.maxPermitted , true )
                            } else {
                               adicionalProduto = new AdicionalDeProdutoEscolhaSimples(optionGroup.name, obrigatorio, [])
                            }

                            adicionalProduto.codigoPdv = optionGroup.externalCode;
                            adicionalProduto.ordem = optionGroup.index + 1;

                            optionGroup.options.forEach((option: any) => {
                              let preco = option.price.value,
                                maxPermitido = option.maxPermitted;

                              let itemOpcao = merchant.items.find((_item: any) => _item.id === option.itemId);

                              let opcaoDeAdicional = new OpcaoDeAdicionalDeProduto(itemOpcao.name, preco, true, itemOpcao.descricao)

                              opcaoDeAdicional.codigoPdv = itemOpcao.externalCode;

                              if(itemOpcao.status === 'UNAVAILABLE') opcaoDeAdicional.disponivel = false;
                              if(maxPermitido) opcaoDeAdicional.qtdeMaxima  = maxPermitido

                              adicionalProduto.opcoesDisponiveis.push(opcaoDeAdicional)
                            })

                            produto.camposAdicionais.push(adicionalProduto)
                          })
                        }

                        produtos.push(produto);
                      } else {
                        console.log('item oferecido nao encontrado: ' + itemOfferId)
                      }
                  })
                }
              })
            }
          })
        }

        resolve(produtos)
    })
  }

  obtenhaMerchant(){
    return new Promise((resolve, reject) => {
      this.merchantInstance.get('/merchant', {}).then((resp: any) => {
        resolve(resp.data)
      }).catch((response: any) => {
         reject(this.retorneErro(response, 'consultar estabelecimento'))
      })
    })
  }

  listeBandeiras(tipo: string): Promise < Array < any >> {
    let banderiasCartao: any = [];


    if (tipo === 'T') {
      banderiasCartao = [EnumBandeirasOpenDelivery.VR_BENEFICIOS, EnumBandeirasOpenDelivery.SODEXO,
        EnumBandeirasOpenDelivery.TICKET, EnumBandeirasOpenDelivery.GOOD_CARD, EnumBandeirasOpenDelivery.BANESCARD,
        EnumBandeirasOpenDelivery.SOROCARD, EnumBandeirasOpenDelivery.POLICARD, EnumBandeirasOpenDelivery.VALECARD,
        EnumBandeirasOpenDelivery.AGICARD, EnumBandeirasOpenDelivery.JCB, EnumBandeirasOpenDelivery.CREDSYSTEM,
        EnumBandeirasOpenDelivery.CABAL, EnumBandeirasOpenDelivery.GREEN_CARD, EnumBandeirasOpenDelivery.VEROCHEQUE,
        EnumBandeirasOpenDelivery.OTHER]
    } else if (tipo === 'D' ||  tipo === 'C' ){
      banderiasCartao = [
        EnumBandeirasOpenDelivery.VISA, EnumBandeirasOpenDelivery.MASTERCARD, EnumBandeirasOpenDelivery.DISCOVER,
        EnumBandeirasOpenDelivery.AMEX, EnumBandeirasOpenDelivery.HIPERCARD, EnumBandeirasOpenDelivery.ELO,
        EnumBandeirasOpenDelivery.AURA]
    }

    return Promise.resolve(banderiasCartao.map((bandeira: string) => ({ id: bandeira, nome: bandeira }) ));
  }

  obtenhaTipoDeOrigem(): EnumTipoDeOrigem {
    return EnumTipoDeOrigem.ImportadoOpenDelivery;
  }

  private retorneErro(data: any, operacao: string){
    let msgErro = 'Falha ao ' + operacao;

    if( data.response) {
      if (data.response.status === 403)
        msgErro = String(`${msgErro}: Permissão negada (HTTP 403)`)

      if (data.response.status === 401)
        msgErro = String(`${msgErro}: Não autorizado (HTTP 401)`)

      if (data.response.status === 404)
        msgErro = String(`${msgErro}: Url não encontrada (HTTP 404)`)

      if (data.response.data) {
        // tratar erro especifico
      }
    } else {
      if(typeof data === 'string'){
        msgErro = String(`${msgErro}: ${data}`)
      } else {
        if(data.message)
          msgErro = String(`${msgErro}: ${data.message}`)
      }
    }


    return msgErro;
  }
}
