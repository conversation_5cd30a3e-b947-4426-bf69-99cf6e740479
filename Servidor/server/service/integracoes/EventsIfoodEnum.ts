import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";

export enum EventsIfoodEnum{
  PedidoNovo = 'PLC',
  PedidoConfirmado = 'CFM',
  PedidoProntoRetirada = 'RTP',
  PedidoDespachado = 'DSP',
  PedidoConcluido = 'CON',
  PedidoCancelado= 'CAN',

  CancelamentoNegado = 'CARF',
  CancelamentoAceito= 'CAR' ,  //CANCELLATION_REQUESTED

  RequestClienteCancelamento = 'CCR', //CONSUMER_CANCELLATION_REQUESTED
  RequestClienteCancelamentoNegada = 'CCD', //CONSUMER_CANCELLATION_DENIED
  RequestClienteCancelamentoAceita = 'CCA',   //CONSUMER_CANCELLATION_ACCEPTED

  DisputaAberta = 'HSD',    //HANDSHAKE_DISPUTE
  DisputaAcordo= 'HSS',  //HANDSHAKE_SETTLEMENT


}

export enum EventsIfoodDeliveryEnum{
  EntregadaorSolicitado = 'RDR', //REQUEST_DRIVER
  EntregadaorAceito = 'RDS',  //REQUEST_DRIVER_SUCCESS
  EntregadaorNegado = 'RDF',  //REQUEST_DRIVER_FAILED


  EntregadorAlocado = 'ADR', //Um entregador foi alocado para realizar a entrega
  EntregadorAcaminhoLoja = 'GTO', //Entregador está a caminho da origem para retirar o pedido
  EntregadorChegouLoja= 'AAO', //Entregador chegou na origem para retirar o pedido
  EntregadorColetouPedido = 'CLT', //Entregador coletou o pedido
  EntregadorChegouDestino = 'AAD', //Entregador chegou no endereço de destino
  EntregadorRetornandoLoja = 'DRGO', //Entregador está retornando ao local de origem(coleta) do pedido
  EntregadorRetornouLoja = 'DRDO', //Entregador já retornou ao local de origem(coleta) do pedido

  //`salesChannel:"POS" incluirão o campo `code` no Metadata
  RequestCodigoConfirmacaoDelivery = 'DDCR', //Informa o código de confirmação de entrega metadata.CODE
  RequestCodigoConfirmacaoDeliveryValidado = 'DDCS', //O código de confirmação foi validado com sucesso

  RequestDevolucaoDecisaoCodigo = 'DRCR', //Informa a decisão e o código de confirmação da devolução //nesse pega Código de Devolução
  RequestDeliveryCancelamento = 'DCR',
  RequestDeliveryCancelamentoAceita = 'DCRA',
  RequestDeliveryCancelamentoNegada = 'DCRR',


  EnderecoEntregaConfirmado = 'DAU', //Cliente confirmou o endereço de entrega
  RequestMudouEnderecoEntrega = 'DAR', //Cliente solicitou alteração do endereço de entrega do pedido
  RequestEnderecoEntregaAleracaoAceita = 'DAA',
  RequestEnderecoEntregaAleracaoNegada = 'DAD',


  //DELIVERY_RETURNING_TO_ORIGIN

}


export const DeParaEventsStatusPedido = new Map<string, EnumStatusPedido>([
  [EventsIfoodEnum.PedidoCancelado, EnumStatusPedido.Cancelado],
  [EventsIfoodEnum.PedidoNovo,  EnumStatusPedido.Novo],
  [EventsIfoodEnum.PedidoConfirmado,  EnumStatusPedido.Novo],
  [EventsIfoodEnum.PedidoProntoRetirada,  EnumStatusPedido.Pronto],
  [EventsIfoodEnum.PedidoDespachado,  EnumStatusPedido.SaiuParaEntrega],
  [EventsIfoodEnum.PedidoConcluido,  EnumStatusPedido.Entregue],
]);
