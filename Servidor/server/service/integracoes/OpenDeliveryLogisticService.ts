import axios from "axios";
import * as https from 'https';
import {IntegracaoOpendeliveryLogistica} from "../../domain/integracoes/IntegracaoOpendeliveryLogistica";
import * as moment from "moment";
import {Pedido} from "../../domain/delivery/Pedido";
import {DTOPedidoOpenDeliveryLogistica} from "../../lib/integracao/opendelivery/DTOPedidoOpenDeliveryLogistica";
import {MapeadorDePedido} from "../../mapeadores/MapeadorDePedido";
import {ExecutorAsync} from "../../utils/ExecutorAsync";

import {Endereco} from "../../domain/delivery/Endereco";
import {DTOSimulacaoEntregaOpenDelivery} from "../../lib/integracao/opendelivery/DTOSimulacaoEntregaOpenDelivery";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {DeliveryPedidoOpendelivery} from "../../domain/integracoes/DeliveryPedidoOpendelivery";
import * as querystring from "querystring";

declare const global: any;
export class OpenDeliveryLogisticService{
  instance: any;
  constructor(public integracao: IntegracaoOpendeliveryLogistica) {
    this.setInstance();
  }

  setInstance(){
    if(global.desenvolvimento){ //dev nao chamar ingeração real
      this.integracao.baseUrl = 'https://promokit.meucardapio.ai/opendelivery/lg/v1'
      this.integracao.token  = 'xx+xxxx+xx+x00+0+00'
      this.integracao.tokenDataExpiracao  =  moment().add(1, 'days').toDate();
    }

    this.instance = axios.create({
      baseURL: this.integracao.baseUrl,
      headers:  {
        'Content-Type': 'application/json',
        'Authorization':  'Bearer ' + this.integracao.token
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });


  }

  obtenhaToken(){
    return new Promise((resolve, reject) => {
      let config = {
        headers: {    'Content-Type': 'application/x-www-form-urlencoded', },
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      }

      let dados = {
        client_id: this.integracao.clientId,
        client_secret: this.integracao.clientSecret,
        grant_type: 'client_credentials'
      }

      let url = String(`${this.integracao.authUrl}/oauth/token`)

      const formattedData = querystring.stringify(dados);

      axios.post(url, formattedData, config ).then((resp: any) => {
        if(resp.data && resp.data.access_token){
          let token = resp.data.access_token,
            dataExpiracao: any = resp.data.expires_in ? moment().add(resp.data.expires_in, 'seconds').toDate() : null;

          this.integracao.setToken(token, dataExpiracao)

          resolve(resp.data)

        } else {
          reject(this.retorneErro(resp.data, 'Não foi possivel obter token', ''))
        }
      }).catch((response: any) => {
        reject(this.retorneErro(response, 'url ou token inválido: ' + url, ''))
      })
    })
  }

  canceleEntrega(pedido: any, empresa: any, razao: string, acao: string, motivo: string){

    return new Promise( async (resolve, reject) => {
      if(!razao) return reject('Informe a razão cancelamento')
      if(!acao) return reject('Informe ação')

      let dados  = {
        reason: razao  ,
        action: acao,
        message: motivo || ''
      }

      await this.facaLogin(empresa);
      console.log(dados)
      this.instance.post('/logistics/cancel/' + pedido.guid, dados).then( async (resposta: any) => {
        resolve(resposta.data)
      }).catch( (response: any) => {
        reject(this.retorneErro(response, 'solicitar cancelamento'));
      })
    })

  }

 async notifiqueNovaEntrega(pedido: any, empresa: any, operador: any){
   let entrega: any = await this.crieEntregaDelivery(pedido, empresa)
     .catch( async (erro) => {
       pedido.erroExternoDelivery = erro;
       await new MapeadorDePedido().atualizeErroExternoDelivery(pedido)
     });

   if(entrega && entrega.deliveryId){
     let novoDelivery = new DeliveryPedidoOpendelivery(pedido, operador, entrega);
     await novoDelivery.salve();
     return novoDelivery;
   }


  }

  alterouStatusPedidoAsync(pedido: any, empresa: any){
    //if(pedido.entrouEmPreparacao())
    // this.notifiqueNovaEntregaAsync(pedido, empresa)

    if(pedido.deliveryPedido){
      if(pedido.saiuParaEntrega() && pedido.deliveryPedido.notificarPedidoRetirado())
        this.notifiqueSaiuParaEntregaAsync(pedido, empresa)

      if(pedido.foiEntregue() && pedido.deliveryPedido.notificarEntregaConcluida())
        this.notifiqueEntregaConluidaAsync(pedido, empresa)

      if(pedido.ficouPronto() && pedido.deliveryPedido.notificarFicouPronto())
        this.notifiqueFicouProntoAsync(pedido, empresa)
    }

  }

  notifiqueEntregaConluida(pedido: any, empresa: any){
    return new Promise(async (resolve, reject) => {
      let payload: any = {
        "finishDate": moment(pedido.horarioAtualizacao).toDate().toISOString(),
        "observation": ""
      }

      console.log(payload)
      await this.facaLogin(empresa);
      this.instance.post('/logistics/finishDelivery/' + pedido.guid, payload).then( async (resposta: any) => {
        resolve(resposta.data);
      }).catch( (response: any) => {
        reject(this.retorneErro(response, 'notificar conclusao da entrega'));
      })
    })
  }

  notifiqueSaiuParaEntrega(pedido: any, empresa: any){
    return new Promise(async (resolve, reject) => {
      let payload = {
        "pickupDate": moment(pedido.horarioAtualizacao).toDate().toISOString(),
        "volumePicked": 0,
        "observation": ""
      }

      console.log(payload)
      await this.facaLogin(empresa);
      this.instance.post('/logistics/orderPicked/' + pedido.guid, payload).then( async (resposta: any) => {
        resolve(resposta.data);
      }).catch( (response: any) => {
        reject(this.retorneErro(response, 'notificar saiu para entrega'));
      })
    })
  }

  notifiqueFicouPronto(pedido: any, empresa: any){
    return new Promise(async (resolve, reject) => {
      let payload = { }
      console.log(payload)
      await this.facaLogin(empresa);
      this.instance.post('/logistics/readyForPickup/' + pedido.guid, payload).then( async (resposta: any) => {
        resolve(resposta.data);
      }).catch( (response: any) => {
        reject(this.retorneErro(response, 'notificar ficou pronto'));
      })
    });

  }

  notifiqueFicouProntoAsync(pedido: any, empresa: any){
    ExecutorAsync.execute(async (cbAsync: any) => {
      let contexto = require('domain').active.contexto;

      contexto.empresa =  empresa;
      contexto.idEmpresa =  empresa.id;

      await this.notifiqueFicouPronto(pedido, empresa)

      cbAsync();
    }, (err: any) => {
      console.error('Falha tentar notificar pedido ficou pornto')
      console.error(err)
    })
  }
  notifiqueEntregaConluidaAsync(pedido: any, empresa: any){
    ExecutorAsync.execute(async (cbAsync: any) => {
      let contexto = require('domain').active.contexto;

      contexto.empresa =  empresa;
      contexto.idEmpresa =  empresa.id;

      await this.notifiqueEntregaConluida(pedido, empresa)

      cbAsync();
    }, (err: any) => {
      console.error('Falha tentar notificar pedido entregue')
      console.error(err)
    })
  }

  notifiqueSaiuParaEntregaAsync(pedido: any, empresa: any){
    ExecutorAsync.execute(async (cbAsync: any) => {
      let contexto = require('domain').active.contexto;

      contexto.empresa =  empresa;
      contexto.idEmpresa =  empresa.id;

      await this.notifiqueSaiuParaEntrega(pedido, empresa)

      cbAsync();
    }, (err: any) => {
      console.error('Falha tentar notificar saiu entrega async')
      console.error(err)
    })
  }


  notifiqueNovaEntregaAsync(pedido: any, empresa: any, operador: any){
    if( pedido.referenciaExternaDelivery || !pedido.ehDelivery()) return;

    ExecutorAsync.execute(async (cbAsync: any) => {
      let contexto = require('domain').active.contexto;

      contexto.empresa =  empresa;
      contexto.idEmpresa =  empresa.id;

      await this.notifiqueNovaEntrega(pedido, empresa, operador)

      cbAsync();
    }, (err: any) => {
      console.error('Falha tentar notificar nova entrega async')
      console.error(err)
    })

  }

  async facaLogin(empresa: any){
    if(!this.integracao.tokenValido()){
      await this.obtenhaToken();

      await this.integracao.atualizeToken();
      this.setInstance();
      await new MapeadorDeEmpresa().removaDasCaches(empresa)
    } else {
      console.log('token valido até: ' + this.integracao.tokenDataExpiracao)
    }
  }

  obtenhaEntrega(orderId: string, empresa: any){
    return new Promise( async (resolve, reject) => {
      await this.facaLogin(empresa);
      this.instance.get(String(`/logistics/delivery/${orderId}`)).then( async (resposta: any) => {
        if(resposta.data && resposta.data.deliveryId){
          resolve(resposta.data);
        } else {
          console.log(resposta.data)
          reject(this.retorneErro(resposta, 'obter entrega'));
        }
      }).catch( (response: any) => {
        reject(this.retorneErro(response, 'obter entrega'));
      })
    });
  }

  simuleEntrega(valor: number, enderecoEntrega: Endereco , empresa: any){
    return new Promise( async (resolve, reject) => {
      let dtoSimulacao: any;

      try{
        dtoSimulacao = new DTOSimulacaoEntregaOpenDelivery(valor, enderecoEntrega, empresa);
      } catch (e){
        console.error(e)
        return reject(this.retorneErro(e.message, 'Não foi possível verificar o valor da entrega', ''))
      }

      if(dtoSimulacao){
         console.log(JSON.stringify(dtoSimulacao))
         await this.facaLogin(empresa);
         this.instance.post('/logistics/availability', dtoSimulacao).then( async (resposta: any) => {
          if(resposta.data && resposta.data.deliveryPrice){
            resolve(resposta.data.deliveryPrice);
          } else {
            console.log(resposta.data)
            reject("Ainda não entregamos no seu endereço");
          }
        }).catch( (response: any) => {
          reject(this.retorneErro(response, 'falha ao  calcular o valor da entrega', ''));
        })
      }

    })
  }



  private crieEntregaDelivery(pedido: Pedido, empresa: any){
    return new Promise( async (resolve, reject) => {
      let dtoEntrega: any;
      try{
        //todo: virar cadastro na integração esse campo???
        let sourceAppId: string = pedido.doIfood() && this.integracao.baseUrl.indexOf('mottu') >= 0 ?
           "a9e3449e-9302-44ea-8016-68c3195b47a1" : null;

        dtoEntrega = new DTOPedidoOpenDeliveryLogistica(pedido, empresa, sourceAppId)
      } catch (e){
        console.error(e)
        return reject(this.retorneErro(e.message, 'Não foi possível solicitar entregador', ''))
      }

      if(dtoEntrega){
        console.log(JSON.stringify(dtoEntrega))
        await this.facaLogin(empresa);
        this.instance.post('/logistics/delivery', dtoEntrega).then( async (resposta: any) => {
          if(resposta.data && resposta.data.deliveryId && resposta.data.event){
            console.log(resposta.data)
            resolve(resposta.data);
          } else {
            console.log('retorno inesperado: ')
            console.log(resposta.data)
            reject(this.retorneErro(resposta, 'solicitar nova entrega'));
          }
        }).catch( (response: any) => {
          reject(this.retorneErro(response, 'solicitar nova entrega'));
        })
      }
    })
  }

  private retorneErro(data: any, operacao: string, prefixo = 'Falha ao '){
    let msgErro =  prefixo + operacao + '(Delivery Parceiro)';

    if( data.response) {
      if (data.response.status === 403)
        msgErro = String(`${msgErro}: Permissão negada (HTTP 403)`)

      if (data.response.status === 401)
        msgErro = String(`${msgErro}: Não autorizado (HTTP 401)`)

      if (data.response.status === 404)
        msgErro = String(`${msgErro}: Url não encontrada (HTTP 404)`)

      let dadosErros = data.response.data;

      if (dadosErros){
        console.log('Http status erro response: ' + data.response.status)
        console.log('Dados erros response')
        console.log(dadosErros)
        //{ statusCode: 500, message: 'Internal server error' }
        if(dadosErros.message || dadosErros.error)
          dadosErros = dadosErros.message || dadosErros.error

        msgErro = String(`${msgErro}: ${dadosErros} (HTTP ${ data.response.status})`)
      } else {
        let erro =  String(`${ data.response.statusText} (HTTP ${ data.response.status})`);
        msgErro = String(`${msgErro}: ${erro}`)
      }

    } else {
      if(typeof data === 'string'){
        msgErro = String(`${msgErro}: ${data}`)
      } else {

        if(data.data && data.data.StatusDescription)
          msgErro = String(`${msgErro}: ${data.data.StatusDescription}`)
        else  if(data.message)
          msgErro = String(`${msgErro}: ${data.message}`)
        else {
          console.log('Dados erros ')
          console.log(data)
          msgErro = String(`${msgErro}: ${data}`)
        }
      }
    }

    console.log(msgErro)

    return msgErro;
  }
}
