import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";

export class CacheUtils{
   static async limpeCacheEmpresa(empresa: any){
     await new MapeadorDeEmpresa().removaDasCaches(empresa, true);
     await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();
     await new MapeadorDeEmpresa().removaListaDeCategoriasDaCache(empresa)
   }
}
