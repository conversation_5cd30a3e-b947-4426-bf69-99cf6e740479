import {DTOPedidoEcletica} from "../lib/integracao/ecletica/DTOPedidoEcletica";
import {Pedido} from "../domain/delivery/Pedido";
import {DTOPedidoAlteradoEcletica} from "../lib/integracao/ecletica/DTOPedidoAlteradoEcletica";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import * as _ from "underscore";
import {PedidoService} from "./PedidoService";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {EnumStatusPedido} from "../lib/emun/EnumStatusPedido";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {EcleticaProdutoUtils} from "../lib/integracao/ecletica/EcleticaProdutoUtils";
import {DTOProdutoSincronizar} from "../lib/integracao/ecletica/DTOProdutoSincronizar";
import {ProdutoService} from "./ProdutoService";
import * as moment from "moment";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {EnumTipoDeOrigem} from "../lib/emun/EnumTipoDeOrigem";
import {Comanda} from "../domain/comandas/Comanda";
import {Catalogo} from "../domain/catalogo/Catalogo";
import {ERPService} from "./integracoes/ERPService";
import {IntegracaoFoodyDelivery} from "../domain/integracoes/IntegracaoFoodyDelivery";
import {DTOItemPedidoMesaEcletica, DTOPedidoMesaEcletica} from "../lib/integracao/ecletica/DTOPedidoMesaEcletica";
import {Ambiente} from "./Ambiente";
import {IItemProdutoEcletica} from "../lib/integracao/ecletica/DTOItemPedidoEcletica";
import {DTOPedidoInLocoEcletica} from "../lib/integracao/ecletica/DTOPedidoInLocoEcletica";
import {RequestParceiro} from "../domain/integracoes/RequestParceiro";

const axios = require('axios');
let redis = require("redis");
let client = redis.createClient();
declare const global: any;
const nomeIntegrador = "MeuCardapio.ai";
const modelo_impressao = "homolog_html_v2_cupom";
const versaoApiDwms = '2.3.0';
const numero_pocket = '1';

enum TipoPedidoEcletica  {
    Delivery = 'D',
    Encomenda = 'E',
    Agendado = 'A',
    EmLoco = 'L'
}

export class EcleticaService implements ERPService{
  instanceCadastro: any;
  instanceIntegracao: any;
  instanceDmws: any;
  comDMWS: boolean;
  constructor(credenciais: any) {
    this.rede  = credenciais.rede.toString();
    this.loja  = credenciais.loja.toString();
    this.comDMWS = credenciais.comDMWS;
    this.identificacao  = 'MCI'
    this.token = 'e30d01cd61abfd9ec8cc31f8b1410bfe49fc54f4';
    let urlApiCadastro = 'https://www.pedidoviaweb.com.br/ws/cadastro/index.php',
       urlApiIntegracao = 'https://www.pedidoviaweb.com.br/ws/integracao',
       urlApiDmws = 'https://www.pedidoviaweb.com.br/ws/dmws/index.php',
       lojaHomologacao = (this.rede === '989' && this.loja === '9969');

     if(global.desenvolvimento || lojaHomologacao){ // garantir que em dev nao invoque ecletica de produção
       urlApiCadastro = 'https://www.pedidoviaweb.com.br/teste/ws/cadastro/index.php'
       urlApiIntegracao = 'https://www.pedidoviaweb.com.br/teste/ws/integracao'
       urlApiDmws = 'https://www.pedidoviaweb.com.br/teste/ws/dmws/index.php'
        this.token = '961eb85020487eb4427c70c7a0b13e16c4a80a47'
        if(global.desenvolvimento){
          this.rede = '999'
          this.loja = '9999'
        }
    }

    this.instanceCadastro = axios.create({
      baseURL: urlApiCadastro,
      headers:  {
        'Content-Type': 'application/json',
        'loginToken':  this.token
      }
    });

    this.instanceIntegracao = axios.create({
      baseURL: urlApiIntegracao,
      headers:  {
        'Content-Type': 'application/json',
        'loginToken':  this.token
      }
    });

    this.instanceDmws =  axios.create({
      baseURL: urlApiDmws,
      headers:  {
        'Content-Type': 'application/json',
        'loginToken':  this.token
      }
    });
  }

  private rede: string;
  private loja: string;
  private identificacao: string
  private token: string

  static async monitorePedidosPendentes(contexto: any): Promise<any> {
    return new Promise( async (resolve) => {
      console.log('buscando pedidos monitorar...')
      let mapeador =  new MapeadorDePedido();
      mapeador.desativeMultiCliente();
      let pedidos: any = await mapeador.listeAbertosIntegrado('ecletica');
      console.log('total pedidos listados:' + pedidos.length)
      let mapNovosStatus = await EcleticaService.obtenhaAlteracoesPedidosEcletica(pedidos);
      let pedidosAgrupados = _.groupBy(pedidos, (pedido: any) => pedido.empresa.id );
      let idEmpresas = Object.keys(pedidosAgrupados);

      for(let i = 0; i < idEmpresas.length; i++){
        let pedidosDaEmpresa: any = pedidosAgrupados[idEmpresas[i]];

        for(let j = 0; j < pedidosDaEmpresa.length; j++){
          let pedido = pedidosDaEmpresa[j];
          let novoStatus = mapNovosStatus[pedido.id]
          if(novoStatus){
            let empresa =  pedido.empresa;
            contexto.idEmpresa = empresa.id;
            contexto.empresa  = empresa;
            console.log('Ajustou empresa no contexto: ' + empresa.id);
            await  new PedidoService().alterouStatusNoSistemaIntegrado(pedido, empresa, novoStatus);
          }
        }
      }
      resolve(null);
    })
    //
  }

  static async  monitoreProdutosIndisponiveis(contexto: any, catalogoContexto: Catalogo = null){
    let produtosAtualizados: any =  {}
    let produtoService = new ProdutoService();
    return new Promise( async (resolve) => {
      console.log('buscando produtos monitorar disponibilidade...')
      let mapeador =  new MapeadorDeProduto(catalogoContexto);

      if(!catalogoContexto)
        mapeador.buscarTodosCatalogos();

      let produtos: any = await mapeador.listeProdutosIndisponiveisEcletica();
      console.log('total produtos listados:' + produtos.length)

      let produtosAgrupados = _.groupBy(produtos, (produto: any) => produto.catalogo.id );

      let idCatalogos = Object.keys(produtosAgrupados);

      for(let i =  0; i < idCatalogos.length; i++) {
        try{
          let  produtosDoCatalogo: any = produtosAgrupados[idCatalogos[i]];
          let catalogo = produtosDoCatalogo[0].catalogo;

          let empresas  = await new MapeadorDeEmpresa().listeAsync({idCatalogo: catalogo.id, ecletica: true});
          for(let w = 0; w < empresas.length; w++) {
            let empresa = empresas[w]
            let serviceInstance: EcleticaService = empresa.integracaoDelivery.obtenhaService();

            console.log('buscar produtos indisponiveis do catalogo ' + catalogo.id);
            let produtosAtualizar: Array<DTOProdutoSincronizar> =
              await serviceInstance.listeProdutosDisponiblidadeAtualizada(produtosDoCatalogo).catch( (err: any) => {
                console.log(err)
              })
            if(produtosAtualizar && produtosAtualizar.length){
              console.log('total produtos atualizar da empresa ' + catalogo.id + "->" + produtosAtualizar.length);
              //ajustando contexto
              contexto.idEmpresa = empresa.id;
              contexto.empresa  = empresa;
              // produtosAtualizar.map( (prod: any) => ())
              produtosAtualizados[catalogo.id] = { total: produtosAtualizar.length, produtos: [], opcoes: [] }
              let templatesOpcoes: any = [];
              for(let j = 0;  j < produtosAtualizar.length;  j++){
                let produtoSincronizar: DTOProdutoSincronizar = produtosAtualizar[ j]

                let erroSincronizar: string

                await produtoService.sincronizeDisponibilidade(empresa, produtoSincronizar, templatesOpcoes).catch((erro) => {
                  erroSincronizar = erro
                })

                if(!erroSincronizar){
                  if(!produtoSincronizar.sincronizarSoAdicionais)
                    produtosAtualizados[catalogo.id].produtos.push({ codigoPdv: produtoSincronizar.codigoPdv,
                      nome:  produtoSincronizar.nome})

                  produtoSincronizar.opcoesAtualizadas.forEach((opcao: any) => {
                    produtosAtualizados[catalogo.id].opcoes.push({codigoPdv: opcao.codigoPdv, nome: opcao.nome})
                  })
                } else {
                  console.log('Erro ao sincronizar disponiblidade produto: ' + erroSincronizar)
                }
              }
            }
          }
        }catch (error){
          console.log(error)
        }
      }
      resolve(produtosAtualizados);
    })
  }

  static async  obtenhaAlteracoesPedidosEcletica(pedidos: any){
    let listaPedidosAlterados: any = [];

    let promisses = [], ultimoPingMysql = new Date();

    for(let i = 0; i < pedidos.length; i++){
      promisses.push( new Promise( async (resolve, reject) => {
        let pedido = pedidos[i];
        let empresa = pedido.empresa;
        empresa.setHorariosFuncionamento();

        if(empresa.estaAberta) {
          let serviceInstance: EcleticaService = empresa.integracaoDelivery.obtenhaService();

          let pedidoEcletica: any = await serviceInstance.obtenhaPedido(pedido, 20).catch((erroConsultar) => {
            console.log('#erro consulta pedido:' + pedido.id)
            console.log(erroConsultar)
          });

          if(moment().diff(ultimoPingMysql, 's') >= 30){
            console.log('#Fazendo ping no mysql para conexão nao morrer')
            await new MapeadorDePedido().facaPing();
            ultimoPingMysql = new Date();
          }

          if(pedidoEcletica) {
            let novoStatus: EnumStatusPedido;
            console.log(pedidoEcletica.status_pedido)

            const finalizouPedido: boolean = pedidoEcletica.status_pedido.indexOf('finalizado') >= 0 ;
            const entrouEmProducao: boolean = pedidoEcletica.status_pedido.indexOf('produ') >= 0;
            const saiuParaEntrega: boolean = pedidoEcletica.status_pedido.indexOf('andamento') >= 0;

            if( finalizouPedido ){
              novoStatus = EnumStatusPedido.Entregue;
            } else if(pedidoEcletica.status_pedido.indexOf('cancelado') >= 0){
              novoStatus = EnumStatusPedido.Cancelado
            } else if(entrouEmProducao) {
              novoStatus = EnumStatusPedido.EmPreparacao
            } else if(saiuParaEntrega) {
              novoStatus = EnumStatusPedido.SaiuParaEntrega
            }

            if(novoStatus && novoStatus !== pedido.status){
              console.log('novo status veio do Ecletica: ' + novoStatus)
              listaPedidosAlterados[pedido.id] = novoStatus;
            } else {
              console.log('monitorar depois, não realizar baixa agora.....')
            }
          }
        }
        resolve('');
      }))
    }

    await Promise.all(promisses);

    return listaPedidosAlterados;

  }


  async veririqueBaixasNosPedidos(pedidos: any){
    let pedidoService: any = new PedidoService();
    let empresa = await new MapeadorDeEmpresa().selecioneSync(pedidos[0].empresa.id);
    let ultimoPingMysql = new Date();

    for(let j = 0; j < pedidos.length; j++){
      let pedido = pedidos[j];

      if(moment().diff(ultimoPingMysql, 's') >= 30){
        console.log('#Fazendo ping no mysql para conexão nao morrer')
         await new MapeadorDePedido().facaPing();
        ultimoPingMysql = new Date();
      }

      let pedidoEcletica: any = await this.obtenhaPedido(pedido).catch( (erroConsultar) => {
        console.log('#erro consulta pedido:')
        console.log(erroConsultar)
      });
      if(pedidoEcletica){
        let novoStatus: EnumStatusPedido;
        console.log(pedidoEcletica.status_pedido)

        const finalizouPedido: boolean = pedidoEcletica.status_pedido.indexOf('finalizado') >= 0 ;
        const entrouEmProducao: boolean = pedidoEcletica.status_pedido.indexOf('produ') >= 0;
        const saiuParaEntrega: boolean = pedidoEcletica.status_pedido.indexOf('andamento') >= 0;

        if( finalizouPedido ){
           novoStatus = EnumStatusPedido.Entregue;
        } else if(pedidoEcletica.status_pedido.indexOf('cancelado') >= 0){
           novoStatus = EnumStatusPedido.Cancelado
        } else if(entrouEmProducao) {
           novoStatus = EnumStatusPedido.EmPreparacao
        } else if(saiuParaEntrega) {
          novoStatus = EnumStatusPedido.SaiuParaEntrega
        }

        if(novoStatus){
          console.log('novo status veio do Ecletica: ' + novoStatus)
          await  pedidoService.alterouStatusNoSistemaIntegrado(pedido, empresa, novoStatus);
        } else {
          console.log('monitorar depois, não realizar baixa agora.....')
        }
      } else {
        console.log('Pular pedido: ' + pedido.referenciaExterna)
      }
    }
  }

  fecheComanda(comanda: Comanda, empresa: any){
    return new Promise(async (resolve, reject) => {
      if(!this.comDMWS) return   resolve({});

      let formaDePagamento = [], numeroComanda = comanda.codigoPdv;

      for(let i = 0; i < comanda.pagamentos.length; i++){
        let pagamento: any = comanda.pagamentos[i];

        if(!pagamento.formaDePagamento.referenciaExterna)
          return reject(String(`Forma de pagamento "${pagamento.formaDePagamento.nome}" não configurada na loja`))

        let formaDePagamentoEclecita = pagamento.formaDePagamento.referenciaExterna;

        let bandeira = pagamento.formaDePagamento.bandeirasCartaoIntegrada[0];

        if(pagamento.codigoTipoPagamento && pagamento.formaDePagamento.bandeirasCartaoIntegrada.length > 1)
          bandeira =  pagamento.formaDePagamento.bandeirasCartaoIntegrada.find( (bandeiraIntegrada: any) =>
            bandeiraIntegrada.codigoPdvOnline.toString() === pagamento.metodoPagamento.toString())

        //   add referencia direita de referenciaExterna ou buscar da empresa a forma_de_pagamento_integrada
        let obrigatorios = ['cartao', 'ticket', 'outras']

        if(!bandeira && obrigatorios.indexOf(formaDePagamentoEclecita) >= 0 )
          return reject(String(`Nenhum bandeira de cartão configurada para o pagamento em: "${pagamento.formaDePagamento.descricao}"`))

        if(bandeira){
          formaDePagamento.push({
            "flag_de_pagamento": bandeira.tipo,
            "nome_bandeira": bandeira.nome,
            "codigo_bandeira": bandeira.codigoPdv ?  bandeira.codigoPdv : bandeira.id,
            "valor":  pagamento.valor
          })
        }
      }

      let valorTotal = comanda.obtenhaTotalPagar();
      let troco =  comanda.obtenhaValorTroco();

      let dados: any = {
        "metodo": "fecharContaJSON",
        "tipo_operacao": numeroComanda ? "comanda" : "mesa",
        "numero": numeroComanda || comanda.mesa.codigoPdv,
        "modulo": nomeIntegrador,
        "numero_pocket": numero_pocket,
        "versao_atual": versaoApiDwms,
        "usa_pinpad": "N",
        "id_transacao": new Date().getTime(),
        "dadosDispositivo": {
          "ip_dispositivo": Ambiente.Instance.ipV4(),
          "sistema_operacional": "android",
          "modelo_pinpad": modelo_impressao,
          "operadora_pinpad": ""
        },
        "dadosOperador": {  },
        "inserirPagamentoJSON": {
          "CpfnaNota": [],
          "FormaDePagamento": formaDePagamento ,
          "Pagamento": {
            "valor_total": valorTotal,
            "dinheiro": !troco ? comanda.obtenhaTotalPagoEm('dinheiro') : comanda.obtenhaTrocoPara(),
            "cheque": comanda.obtenhaTotalPagoEm('cheque',  valorTotal),
            "cartao": comanda.obtenhaTotalPagoEm('cartao', valorTotal),
            "ticket": comanda.obtenhaTotalPagoEm('ticket',  valorTotal),
            "outros": comanda.obtenhaTotalPagoEm('outras',  valorTotal),
            "servico": comanda.taxaServico,
            "desconto": comanda.desconto,
            "vale_ref": 0,
            "contra_vale_emi": 0,
            "contra_vale_rec": 0,
            "troco": troco,
            "numero_de_pessoas": 1,
            "nome_cliente": "",
            "telefone_cliente": "",
            "repique": 0,
            "pendura": 0,
            "cpf": ""
          }
        }
      }

      let payload = [{ JSON: dados}]

      //necessario consulta intens de fechar
      await this.consultaTotalConta(dados.numero, dados.tipo_operacao);

      console.log(JSON.stringify(payload))
      this.instanceDmws.post(this.getPathPost("fecharConta"), payload)
        .then(   (response: any) => {
          console.log('retorno resposta criar fechar conta: ')
          console.log( response.status)
          if(response.data.cod === '000' ){
            console.log(response.data.dados_adicionais) //prev_entrega
            let dadosBase64: any = response.data.dados_adicionais.json;
            resolve( dadosBase64 && dadosBase64.length ? dadosBase64[0].value : {});
          } else {
            reject(this.retornoErro('fechar conta', response.data))
          }
        }).catch( (response: any) => {
        reject(this.retornoErro('fechar conta', response))
      })
    })
  }

  adicioneItemPedidoNaComandaDWMS(pedido: any, numeroMesaComanda: string, operacao: string, transacao: string,
                                  itens: Array<IItemProdutoEcletica>, garcom: any): Promise<string> {
    return new Promise(async (resolve, reject) => {
      try{
        let dados: any  = new DTOItemPedidoMesaEcletica(numeroMesaComanda, operacao, transacao, itens, garcom)
        let inicio = new Date()
        let payload = [{ JSON: dados}]
        console.log(JSON.stringify(payload))
        this.instanceDmws.post(this.getPathPost("efetuaVenda"), payload)
          .then(  async (response: any) => {
            console.log('retorno resposta criar pedido venda mesa: ')
            console.log( response.status)
            console.log( response.data)
            console.log(String(`>>>tempo inserir pedido mesa: ${moment().diff(inicio, 's')}s`))
            await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response);
            if(response.data.cod === '000' ){
              console.log(response.data.dados_adicionais) //prev_entrega
              resolve( dados.id_transacao );
            } else {
              reject(this.retornoErro('efetua Venda', response.data))
            }
          }).catch( async (response: any) => {
            let msgErro: any = this.retornoErro('efetua Venda', response);

          await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response, msgErro);
          reject(msgErro)
        })
      } catch (execption){
        console.log('**erro adicionar pedido**')
        console.log(execption)
        reject(this.retornoErro('antes de enviar pedido mesa', execption))
      }
    })
  }

  adicionePedidoDaComandaDWMS(pedido: any, empresa: any): Promise<string> {
    return new Promise(async (resolve, reject) => {
      try{
        if(!pedido.mesa)
          throw Error(  'Pedido não tem nenhuma mesa vinculada');

        if(!pedido.comanda.garcom)
          throw Error(  'Informe o garçom da comanda');

        if(!pedido.garcom)
          throw Error(  'Informe o garçom do pedido');

        if(empresa.integracaoDelivery.integrarComComandas()){
          if(!pedido.comanda.codigoPdv)
            throw Error(  'Informe o código da comanda na mesa ' + pedido.mesa.nome);
        } else {
          if(!pedido.mesa.codigoPdv)
            throw Error(  'Informe o código do PDV na mesa ' + pedido.mesa.nome);
        }

        let dados: any  = this.obtenhaDTOPedido(pedido, empresa)
        let inicio = new Date()
        let payload = [{ JSON: dados}]
        console.log(JSON.stringify(payload))
        this.instanceDmws.post(this.getPathPost("efetuaVenda"), payload)
          .then(  async (response: any) => {
            console.log('retorno resposta criar pedido venda mesa: ')
            console.log( response.status)
            console.log( response.data)
            console.log(String(`>>>tempo inserir pedido mesa: ${moment().diff(inicio, 's')}s`))
            await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response);
            if(response.data.cod === '000' ){
              console.log(response.data.dados_adicionais) //prev_entrega
              resolve( dados.id_transacao );
            } else {
              reject(this.retornoErro('efetua Venda', response.data))
            }
          }).catch( async (response: any) => {

          let msgErro: any = this.retornoErro('efetua Venda', response);
          await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response, msgErro);

          reject(msgErro)
        })
      } catch (execption){
        console.log('**erro adicionar pedido**')
        console.log(execption)
        reject(this.retornoErro('antes enviar pedido mesa', execption))
      }
    })
  }

  removaItensMesaDWMs(pedidoNovo: any, itensRemovidos: Array<any>, justificativa: string ){
    let numeroComanda = pedidoNovo.comanda.codigoPdv ;
    const operacao = numeroComanda ? "comanda" : "mesa";

    if(!numeroComanda) numeroComanda =  pedidoNovo.mesa.codigoPdv

    return new Promise(async (resolve, reject) => {
      let dados: any = {
        "metodo": "cancelarItemPedidoJSON",
        "tipo_operacao": operacao,
        "numero": numeroComanda ,
        "modulo": nomeIntegrador,
        "numero_pocket": numero_pocket,
        "versao_atual": versaoApiDwms,
        "usa_pinpad": "N",
        "id_transacao":  new Date().getTime(),
        "dadosDispositivo": {
          "ip_dispositivo": Ambiente.Instance.ipV4(),
          "sistema_operacional": "android"
        },
        "dadosOperador": { },
        "cancelarItemPedidoJSON": {
          "Itens": []
        }
      }

      let itenscomandas: any = await this.listeItensMesaComanda(numeroComanda, operacao);

      for( let i = 0 ; i < itensRemovidos.length; i++){
        let item: IItemProdutoEcletica = itensRemovidos[i];

        //tirando itens removidos
        itenscomandas = itenscomandas.filter((itemComanda: any) => itemComanda.cod_reg.toString() !== '-1');

        let itemNaComanda: any = itenscomandas.find((itemComanda: any) =>
          itemComanda.cod_item.toString() === item.id_produto.toString() && Number(itemComanda.qtde) === item.quantidade);

        if(!itemNaComanda){
          console.log(itenscomandas)
          return reject(String(`Item na ${operacao} não econtrado: "${item.id_produto} - ${item.nome}", quantidade: ${item.quantidade}`))
        }

        dados.cancelarItemPedidoJSON.Itens.push(
          {
            "cod_item":  item.id_produto,
            "descr_item": item.nome,
            "quantidade": item.quantidade,
            "vlr_unit": item.valor_unitario,
            "valor_total": Number((item.quantidade *  item.valor_unitario).toFixed(2)),
            "cod_reg": itemNaComanda.cod_reg,
            "multi_sabor": "N",
            "transferido_de": 0,
            "cancelamento": {
              "justificativa": justificativa
            }
          }
        )

        //marcar removido assim como api retorna.
        itemNaComanda.cod_reg = '-1';
      }


      let payload = [{ JSON: dados}];
      console.log(JSON.stringify(payload))
      this.instanceDmws.post(this.getPathPost("cancelaItens"), payload)
        .then(  async (response: any) => {
          console.log('retorno resposta criar cancelar item da venda')
          console.log( response.status)
          await new RequestParceiro(pedidoNovo, 'ecletica', payload).saveRetornoHttp(response);
          if(response.data.cod === '000' ){
            console.log(response.data.dados_adicionais) //prev_entrega
            resolve( response.data.dados_adicionais);
          } else {
            reject(this.retornoErro('cancelar item mesa', response.data))
          }
        }).catch( async (response: any) => {
          let msgErro: any = this.retornoErro('cancelar item mesa', response);
        await new RequestParceiro(pedidoNovo, 'ecletica', payload).saveRetornoHttp(response, msgErro);
        reject(msgErro)
      })
    })


  }

  cancelePedidoMesaNoDWMs(pedido: any,  justificativa: string = 'Item removido Meucardapio.ai'){
    return new Promise(async (resolve, reject) => {
      let dadosAlteracao = new DTOPedidoAlteradoEcletica(null, pedido, pedido.empresa);
      let itensRemovidos: any = dadosAlteracao.obtenhaItensRemovidos();
      let erroRemover: any;

      await this.removaItensMesaDWMs(pedido, itensRemovidos, justificativa).catch((erro) => {
        erroRemover = erro;
      })

      if(erroRemover) return reject(erroRemover)

      resolve('Remoção  realizado com sucesso')
    })
  }


  alterePedidoMesaNoDWMs(pedidoNovo: any, dadosAlteracao: DTOPedidoAlteradoEcletica,
                         justificativa: string = 'Item removido Meucardapio.ai'){
    let itensRemovidos: any = dadosAlteracao.obtenhaItensRemovidos(), itensNovos = dadosAlteracao.obtenhaItensNovos();
    let erroRemover: any;
    let erroAdicionar: any;

    return new Promise(async (resolve, reject) => {
      if(itensRemovidos.length){
        await this.removaItensMesaDWMs(pedidoNovo, itensRemovidos, justificativa).catch((erro) => {
          erroRemover = erro;
        })

        if(erroRemover) return reject(erroRemover)
      }

      let transacao =  new Date().getTime().toString()  + new Date().getMilliseconds();

      if(itensNovos.length){
        let numeroMesaComanda = pedidoNovo.comanda.codigoPdv;
        let operacao = numeroMesaComanda ? 'comanda' : 'mesa';
        if(!numeroMesaComanda) numeroMesaComanda = pedidoNovo.mesa.codigoPdv;

        await this.adicioneItemPedidoNaComandaDWMS(pedidoNovo, numeroMesaComanda, operacao, transacao, itensNovos,
          pedidoNovo.comanda.garcom).catch((erro) => {
           erroAdicionar = erro;
        })

        if(erroAdicionar) return reject(erroAdicionar)
      }

      resolve(transacao)
    })
  }


  enviePreFechamentoMesaDWMS(comanda: Comanda, empresa: any){
    let numeroComanda = comanda.codigoPdv ;

    return new Promise(async (resolve, reject) => {
      if(!this.comDMWS) return   resolve({});

      let dados: any  = {
        "metodo": "emitirPreFechamentoJSON",
        "tipo_operacao": numeroComanda ? "comanda" : "mesa",
        "numero": numeroComanda || comanda.mesa.codigoPdv,
        "modulo": nomeIntegrador,
        "numero_pocket": numero_pocket,
        "versao_atual": versaoApiDwms,
        "usa_pinpad": "N",
        "id_transacao":  new Date().getTime(),
        "dadosDispositivo": {
          "ip_dispositivo": Ambiente.Instance.ipV4(),
          "sistema_operacional": "android",
          "modelo_pinpad": modelo_impressao,
          "operadora_pinpad": ""
        },
        "dadosOperador": {  },
        "emitirPreFechamentoJSON": {
          "valor_desconto": comanda.desconto,
          "tipo_desconto": "dinheiro",
          "numero_pessoas": 1
        }
      }
      let comandaEcletica: any =  await this.consultaTotalConta(dados.numero, dados.operacao).catch(( err) => {})

      let payload = [{ JSON: dados}];
      console.log(JSON.stringify(payload))
      this.instanceDmws.post(this.getPathPost("enviaPreFechamento"), payload)
        .then( async (response: any) => {
          console.log('retorno resposta criar pedido venda mesa: ')
          console.log( response.status)
          if(response.data.cod === '000' ){
            console.log(response.data.dados_adicionais) //prev_entrega
            let dadosBase64: any = response.data.dados_adicionais.json;
            resolve( dadosBase64 && dadosBase64.length ? dadosBase64[0].value : {});
          } else {
            //comada zerada, ignorar pre-fechamento...
            if(comandaEcletica && comandaEcletica.total_conta === 0) return resolve({});

            reject(this.retornoErro('pre fechamento', response.data))
          }
        }).catch( (response: any) => {
        reject(this.retornoErro('pre fechamento', response))
      })
    })
  }

  adicionePedidoDaComanda(pedido: any, empresa: any){
    if(this.comDMWS)
      return this.adicionePedidoDaComandaDWMS(pedido, empresa);

    return this.adicionePedidoDaComandaInLoco(pedido, empresa)
  }

  //api descontinuado no exportaWeb suando nova agora DMWS
  adicionePedidoDaComandaInLoco(pedido: any, empresa: any): Promise<string> {
    return new Promise(async (resolve, reject) => {
      try{
        if(!pedido.mesa)
          throw Error(  'Pedido não tem nenhuma mesa vinculada');

        if(!pedido.comanda.garcom)
          throw Error(  'Informe o garçom da comanda');

        if(!pedido.garcom)
          throw Error(  'Informe o garçom do pedido');

        if(empresa.integracaoDelivery.integrarComComandas()){
          if(!pedido.comanda.codigoPdv)
            throw Error(  'Informe o código da comanda na mesa ' + pedido.mesa.nome);
        } else {
          if(!pedido.mesa.codigoPdv)
            throw Error(  'Informe o código do PDV na mesa ' + pedido.mesa.nome);
        }

        let dados: any  = this.obtenhaDTOPedido(pedido, empresa)

        let payload = {pedidoInLocoJSON: dados};
        console.log(JSON.stringify(payload))
        let inicio = new Date()
        this.instanceIntegracao.post(this.getPathPost("pedidoInLoco"), payload)
          .then( async  (response: any) => {
          console.log('retorno resposta criar pedido in loco: ')
          console.log( response.status)
          console.log( response.data)
          console.log(String(`>>>tempo inserir pedido in loco: ${moment().diff(inicio, 's')}s`))
            await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response);
          if(response.data.cod === '000' ){
            console.log(response.data.dados_adicionais) //prev_entrega
            resolve( dados.dadosPedido.id_transacao );
          } else {
            reject(this.retornoErro('criar pedido', response.data))
          }
        }).catch( async (response: any) => {
          let msgErro: any = this.retornoErro('criar pedido', response);
          await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response, msgErro);
          reject(msgErro)
        })
      } catch (execption){
        console.log('**erro adicionar pedido**')
        console.log(execption)
        let msgErro = 'Pedido com erro, corrija antes enviar para Ecletica : ' + execption.message || execption;
        reject(msgErro)
      }
    })
  }

  private obtenhaChaveRedis(pedido: Pedido){
    return  'ecletica#' + pedido.id;
  }

  obtenhaDTOPedido(pedido: any, empresa: any){
    let dados: any;

    let mesaComanda = pedido.mesa && pedido.mesa.id && pedido.comanda && pedido.comanda.id;

    if(!mesaComanda){
      dados = new DTOPedidoEcletica(pedido, empresa);
    }  else {
      dados = this.comDMWS ? new DTOPedidoMesaEcletica(pedido, empresa) : new DTOPedidoInLocoEcletica(pedido)
    }

    if(!dados.id_transacao){
      let identificador = this.obtenhaIdentificador(pedido);

      dados.setDadosLoja(this.rede, this.loja, identificador );
    }


    return dados;
  }


  private getPathPost(nomeMetodo: string, extra: string = null){
    let url = String(`/index.php?metodo=${nomeMetodo}&rede=${this.rede}&loja=${this.loja}`);

    if(extra) url = url  + "&" + extra;

    console.log(url)

    return url;
  }

  private async enviePedido(dados: any, chave: string, pedido: any){
    return new Promise(async (resolve, reject) => {
      let inicio = new Date();
      let payload = { enviaPedidoJSON: dados};

      this.instanceIntegracao.post(this.getPathPost("enviaPedido"), payload).then(  async  (response: any) => {
        console.log(String(`>>>tempo inserir pedido: ${moment().diff(inicio, 's')}s`))
        let erro: any =   this.trateRetorno(response );

        await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response, erro);

        resolve(erro)
      }).catch( async (response: any) => {
        let erro: any = this.retornoErro('criar pedido', response);

        await new RequestParceiro(pedido, 'ecletica', dados).saveRetornoHttp(response, erro);

        reject(erro)
      }).finally( () => {
        client.del(chave);
      })
    })
  }

  adicionePedido(pedido: Pedido, empresa: any): Promise<string>{
    return new Promise(async (resolve, reject) => {
      if(pedido.referenciaExterna){
        console.log('Já foi notificado para o eclética' + pedido.referenciaExterna)
        return  resolve(pedido.referenciaExterna)
      }

      if(pedido.foiCanceladoOuDevolvido())
        return  reject('Pedido foi cancelado, não pode ser enviado.')

      await pedido.carregueDeParaTamanhoSabores();

      const chave = this.obtenhaChaveRedis(pedido);

      client.get(chave, async (err: any, reply: string) => {
        if(reply)
          return reject('Aguarde! Pedido "#' + pedido.codigo + '" já está sendo enviado ao Ecletica ');

        client.set(chave, pedido.codigo, 'EX',  45);
        if(pedido.comanda && pedido.comanda.id){
          this.adicionePedidoDaComanda(pedido, empresa).then( (resp) => {
            resolve(resp);
          }).catch( erro => {
            reject(erro)
            // @ts-ignore
          }).finally( () => {
            client.del(chave);
          })
        } else {
          try{
            let dados: any = this.obtenhaDTOPedido(pedido, empresa);
            console.log(dados);
            let erro = await this.enviePedido(dados, chave, pedido);

            if(!erro){
              this.notifiquePedidoEmPreparacao(pedido, empresa)
              resolve( dados.dadosPedido.id_transacao );
            } else {
              reject(erro);
            }
          } catch (execption){
            console.log('**erro adicionar pedido**')
            console.log(execption)
            reject(this.retornoErro('enviar pedido', execption))
          }
        }
      });
    })
  }



  async alterePedido(pedidoNovo: Pedido, pedidoAntigo: Pedido, empresa: any): Promise<any>{
    try{
      await pedidoNovo.carregueDeParaTamanhoSabores();

      let dados: DTOPedidoAlteradoEcletica = new DTOPedidoAlteradoEcletica(pedidoNovo, pedidoAntigo, empresa);

      await dados.setNovoIdenticador(pedidoAntigo);

      if(pedidoNovo.comanda && pedidoNovo.comanda.id && this.comDMWS)
        return this.alterePedidoMesaNoDWMs(pedidoNovo, dados)

      return new Promise(async (resolve, reject) => {
        /*
         { cod: '006',
            msg: 'Loja sem internet',
            dados_adicionais: { pedido_em_processamento: false } }
         */
        dados.dadosPedido.rede = this.rede;
        dados.dadosPedido.loja = this.loja;

        let payload: any =  {alteraPedidoJSON: dados};

        console.log(dados)
        this.instanceIntegracao.post(   this.getPathPost("alteraPedido"),
          payload).then(   async (response: any) => {

          await new RequestParceiro(pedidoNovo, 'ecletica', payload).saveRetornoHttp(response);

          if(response.data.cod === '000' ){
            console.log(response.data.dados_adicionais) //prev_entrega
            resolve( dados.dadosPedido.id_transacao_nova);
          } else {
            reject(this.retornoErro('alterar pedido', response.data))
          }
        }).catch( async (response: any) => {

          let msgErro: any = this.retornoErro('alterar pedido', response);

          await new RequestParceiro(pedidoNovo, 'ecletica', payload).saveRetornoHttp(response, msgErro);

          reject(msgErro)
        })

      })
    } catch (execption){
      console.log('**erro alterar pedido**')
      console.log(execption)
      return Promise.reject(this.retornoErro('alterar pedido', execption))
    }
  }

  cancelePedido(pedido: Pedido, motivo: string): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      if(pedido.comanda && pedido.comanda.id && this.comDMWS) {
        let erroCancelarMesa: any;
        await this.cancelePedidoMesaNoDWMs(pedido, motivo).catch((erro) => {    erroCancelarMesa = erro; });
        resolve(erroCancelarMesa);
      } else {
        let dados = {
          telefone_cliente: pedido.contato.telefone,
          nome_cliente : pedido.contato.nome.substring(0, 50),
          data : moment().format('DD/MM/YYYY'),
          hora : moment().format('HH:mm:ss'),
          tipo_pedido :  pedido.ehDelivery() ? "D" : "E"
        }

        let payload: any =  { cancelaPedidoJSON: dados};

        console.log(payload)

        this.instanceIntegracao.post(   this.getPathPost("cancelaPedido", String(`id_transacao=${pedido.referenciaExterna}`)),
          payload).then(  async (response: any) => {
          await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response);
          if(response.data.cod === '000' ){
            console.log(response.data)
            resolve('');
          } else {
            resolve(this.retornoErro('cancelar pedido', response.data))
          }
        }).catch( async (response: any) => {
          let msgErro: any = this.retornoErro('cancelar pedido', response);
          await new RequestParceiro(pedido, 'ecletica', payload).saveRetornoHttp(response, msgErro);
          resolve(msgErro)
        })
      }
    })
  }

  private obtenhaTipoEcletica(pedido: Pedido){
    let tipo = 'D';

    if(pedido.comanda && pedido.comanda.id){
      tipo = 'L'
    }  else if(!pedido.ehDelivery()){
      tipo = 'E'
    } else if(pedido.horarioEntregaAgendada) {
      let dataMundanca = '2021-04-07 08:00:00';
      if(moment(pedido.horario).isAfter(moment(dataMundanca)))
        tipo = 'A'
      else
        tipo = 'E'
    }


    return tipo;
  }

  obtenhaPedido(pedido: Pedido, timeoutSegundos = 15){
    return new Promise(async (resolve, reject) => {
     try {
       let contato = pedido.contato;
       let id_transacao = pedido.referenciaExterna;


       let dadosCliente = {
         telefone_cliente: contato.telefone,
         nome_cliente: contato.nome.trim(),
         tipo_pedido: this.obtenhaTipoEcletica(pedido),
         data: moment().format('DD/MM/YYYY'),
         hora: moment().format('HH:mm:ss')
       }

       console.log(dadosCliente)
       let inicio = new Date();
       console.log('#consultar pedido ecletica: ' + id_transacao);
       this.instanceIntegracao.post(this.getPathPost("consultaPedido", String(`id_transacao=${id_transacao}`)),
         { consultaPedidoJSON: dadosCliente}, {timeout: timeoutSegundos * 1000}).then(   (response: any) => {
         console.log('#chamou then ');
         console.log(String(`>>>tempo consultar pedido: ${moment().diff(inicio, 's')}s`))
         if(response.data.cod === '000' ){
           resolve(response.data.dados_adicionais);
         } else {
           reject(this.retornoErro('consultar pedido', response.data))

         }
       }).catch( (response: any) => {
         console.log('#chamou catch ');
         console.log('#erro na resposta Ecletica: ' +  moment().diff(inicio, 's') + "s");
         reject(this.retornoErro('consultar pedido', response))
       }).finally( () => {
         console.log('#chamou finally ');
       });
     } catch (err){
       console.log('#erro consulta pedido')
       console.log(err)
       reject(this.retornoErro('consultar pedido', err))
     }

    });
  }

  obtenhaProduto(codigo: string){
    return new Promise(async (resolve, reject) => {
      resolve(null);
    })
  }

  obtenhaProdutoConvertido(){
    return Promise.resolve(null)
  }

  private getParams(nomeMetodo: string, extra: any = null){
    let params: any = { metodo:  nomeMetodo, rede: this.rede, loja: this.loja};
    if(extra) Object.assign(params, extra)
    console.log(params)
    return  { params:  params }
  }

  listeProdutos(): Promise<Array<any>>{
    return new Promise(async (resolve, reject) => {
      this.instanceCadastro.get(null, this.getParams("listaProdutos")).then(   (response: any) => {
        if(response.data.cod === '000' ){
          resolve(response.data.dados_adicionais);
        } else {
          console.log(response.data)
          reject(response.data.msg)
        }
      }).catch( (response: any) => {
        reject(this.retornoErro('listar produtos', response))
      })
    });
  }




  listeProdutosConvertidos(ultimaSincronizacaoProdutos: any = null): Promise<any>{
    return new Promise(async (resolve, reject) => {
      let reposta: any = await this.listeProdutos().catch( (erroIntegracao: any) => {
           reject(erroIntegracao)
      })

      if(reposta){
        let categoriasEcletica = reposta.categorias;
        let produtosEcletica = reposta.produtos;
        let combos = reposta.combos;
        let produtosCatalogoDelivery: Array<any> =
          EcleticaProdutoUtils.convertaParaProdutos(produtosEcletica, categoriasEcletica, combos);

        resolve(produtosCatalogoDelivery)
      }
    });
  }

  listePrecosProdutos(ultimaSincronizacaoPrecos: any): Promise<any>{
    return this.listeProdutosConvertidos();
  }


  listeProdutosIndisponiveis(): Promise<any>{
    return new Promise(async (resolve, reject) => {
      let inicio =  moment();
      this.instanceCadastro.get(null, this.getParams("listaFaltantes")).then(   (response: any) => {
        let tempo =  moment().diff(inicio, 's');
        if(tempo >= 1)
          console.log(String(`->>tempo verificar disponiveis: ${tempo}s`))

        if(response.data.cod === '008'){ // retorna esse erro quando nao produtos faltantes na base
          console.log(response.data)
          return resolve([])
        }

        if(response.data.cod === '000' ){
          resolve( response.data.dados_adicionais.produtos_faltantes)
        } else {
          console.log(response.data)
          reject(this.retornoErro('listar produtos faltantes', response.data))
        }
      }).catch( (error: any) => {
        console.log(String(`->>tempo verificar disponiveis(com erro): ${moment().diff(inicio, 's')}s`))
        reject(this.retornoErro('listar preços produtos', error))
      })

    });
  }

  listeProdutosDisponiblidadeAtualizada(produtosIntegrados: any): Promise<any>{
    return new Promise(async (resolve, reject) => {
      let produtosFaltantes = await this.listeProdutosIndisponiveis().catch( (error: any) => { reject(error)});

      if(produtosFaltantes){
        if(produtosFaltantes.length){
          let produtosSincronizar: any =
            EcleticaProdutoUtils.obtenhaProdutosSincronizarDisponibilidade(produtosIntegrados, produtosFaltantes)

          resolve(produtosSincronizar);
        } else {
          resolve([])
        }
      }
    });
  }

  listeBandeiras(tipo: string): Promise<Array<any>>{
    return new Promise(async (resolve, reject) => {
      this.instanceCadastro.get(null, this.getParams("listaBandeiras", { tipo: tipo })).then(
        (response: any) => {
          if(response.data.cod === '000' ){
            let bandeiras = response.data.dados_adicionais.bandeiras;

            let bandeirasVisiveis = bandeiras.filter((item: any) => item.status === 'N');

            resolve(bandeirasVisiveis);
          } else {
            console.log(response.data)

            if(response.data.dados_adicionais && response.data.dados_adicionais.motivo)
              return reject(response.data.dados_adicionais.motivo)

            reject(String(`Erro Ecletica: ` + response.data.msg))
          }
      }).catch( (response: any) => {
        reject(this.retornoErro('listar produtos', response))
      })

    });
  }


   valideToken(){
    return new Promise(async (resolve, reject) => {
      if(this.comDMWS){
        // Altere o timeout para 15 segundos
        this.instanceDmws.defaults.timeout = 15 * 1000;
        let erroValidarDMWS;
        await this.listeMapaDeMesas().catch((erro) => {
          erroValidarDMWS = erro;
        });

        if(erroValidarDMWS)
          return reject('Para usar nova api mesas/comandas solicite ativação do monitor DMWS')
      }

      let retorno: any = await this.statusLoja({}).catch((err) => reject(err));

      console.log(retorno)
      if(retorno.cod != null) return resolve(true)

      reject(retorno.msg || 'Não foi possível verificar status da loja');
    })
  }

  statusLoja(data = {}){
    let inicio =  new Date();
    return new Promise(async (resolve, reject) => {
      this.instanceIntegracao.get(  null, this.getParams("monitoraLoja"))
        .then(   (response: any) => {
          let tempo  = moment().diff(inicio, 's');
          if(tempo >= 1)
            console.log(String(`->>tempo verificar status loja: ${tempo}s`))
          if(response.data && response.data.cod  ){
            resolve( response.data);
          } else {
            console.log(response.data)
            reject(response.data.msg)
          }
        }).catch( (response: any) => {
        reject(this.retornoErro('monitorar Loja', response))
      })

    });
  }
  veririqueUpdates(data = {}){
    let inicio =  new Date();
    return new Promise(async (resolve, reject) => {
      this.instanceCadastro.get(  null, this.getParams("ultimaAtualizacao"))
        .then(   (response: any) => {
          let tempo  = moment().diff(inicio, 's');
          if(tempo >= 1)
            console.log(String(`->>tempo verificar atualizações: ${tempo}s`))
        if(response.data.cod === '000' ){
          resolve(response.data.dados_adicionais);
        } else {
          console.log(response.data)
          reject(response.data.msg || 'Não foi possivel verificar atualizaçoes da loja')
        }
      }).catch( (response: any) => {
        reject(this.retornoErro('verificar atualizações', response))
      })

    });
  }

  obtenhaConfiguracoesLoja(){
    return new Promise(async (resolve, reject) => {
      this.instanceDmws.get(  null, this.getParams("verificacoesIniciais"))
        .then(   (response: any) => {
          if(response.data.cod === '000' ){
            resolve(response.data.dados_adicionais);
          } else {
            console.log(response.data)
            reject(response.data.msg || 'Não foi possivel consultar metodo: verificacoesIniciais')
          }
        }).catch( (response: any) => {
        reject(this.retornoErro('consultar metodo verificacoesIniciais', response))
      })

    });
  }

  listaBandeirasDMWS(){
    //
    return new Promise(async (resolve, reject) => {
      this.instanceDmws.get(  null, this.getParams("listaDetalheBandeiras"))
        .then(   (response: any) => {
          if(response.data.cod === '000' ){
            resolve(response.data.dados_adicionais.bandeiras);
          } else {
            console.log(response.data)
            reject(response.data.msg || 'Não foi possivel consultar metodo: listaDetalheBandeiras')
          }
        }).catch( (response: any) => {
        reject(this.retornoErro('consultar metodo listaDetalheBandeiras', response))
      })

    });
  }

  listeMapaDeMesas(){
    return new Promise(async (resolve, reject) => {
      this.instanceDmws.get(  null, this.getParams("mapaDeMesas"))
        .then(   (response: any) => {
          if(response.data.cod === '000' ){
            resolve(response.data.dados_adicionais.mesas);
          } else {
            console.log(response.data)
            reject(response.data.msg || 'Não foi possivel consultar metodo: mapaDeMesas')
          }
        }).catch( (response: any) => {
        reject(this.retornoErro('consultar metodo mapaDeMesas', response))
      })

    });
  }

  listeMapaDeComandas(){
    return new Promise(async (resolve, reject) => {
      this.instanceDmws.get(  null, this.getParams("mapaDeComandas"))
        .then(   (response: any) => {
          if(response.data.cod === '000' ){
            resolve(response.data.dados_adicionais.comandas);
          } else {
            console.log(response.data)
            reject(response.data.msg || 'Não foi possivel consultar metodo: mapaDeComandas')
          }
        }).catch( (response: any) => {
        reject(this.retornoErro('consultar metodo mapaDeComandas', response))
      })

    });
  }

  listeItensMesaComanda(mesa_comanda: number, operacao: string){
    return new Promise(async (resolve, reject) => {

      let comanda: any = await this.consultaTotalConta(mesa_comanda, operacao).catch((err) => {
        reject(err)
      });

      if (comanda) return resolve(comanda.produtos)
    })
  }

  consultaTotalConta(mesa_comanda: number, operacao: string){
    return new Promise(async (resolve, reject) => {
      let dados: any =  {
        "metodo": "consultarTotalContaJSON",
        "tipo_operacao": operacao,
        "numero": mesa_comanda.toString(),
        "numero_conta": 0
      }

      let payload = [{ JSON: dados}]
      console.log(payload)
      this.instanceDmws.post( this.getPathPost("consultaTotalConta" ), payload)
        .then(   (response: any) => {
          if(response.data.cod === '000' ){
            resolve(response.data.dados_adicionais );
          } else {
            console.log(response.data)
            reject(response.data.msg || 'Não foi possivel consultar total conta da mesa/comanda: consultaTotalConta')
          }
        }).catch( (response: any) => {
        reject(this.retornoErro('consultar metodo consultaTotalConta', response))
      })

    });
  }

  listeProdutosDMWS(): Promise<Array<any>>{
    return new Promise(async (resolve, reject) => {
      this.instanceDmws.get(null, this.getParams("listaProdutos")).then(   (response: any) => {
        if(response.data.cod === '000' ){
          resolve(response.data.dados_adicionais);
        } else {
          console.log(response.data)
          reject(response.data.msg)
        }
      }).catch( (response: any) => {
        reject(this.retornoErro('listar produtos', response))
      })
    });
  }

  listeOperadoresDMWS(operacao: string): Promise<Array<any>>{
    return new Promise(async (resolve, reject) => {
      if(!this.comDMWS) return resolve([])

      this.instanceDmws.get(null, this.getParams("listaOperadores", {operacao: operacao})).then(   (response: any) => {
        if(response.data.cod === '000' ){
          resolve(response.data.dados_adicionais.operadores);
        } else {
          console.log(response.data)
          reject(response.data.msg)
        }
      }).catch( (response: any) => {
        reject(this.retornoErro('listar produtos', response))
      })
    });
  }

  private retornoErro(operacao: string, dados: any): String {
    console.log(dados)
    let msgErro = String(`Falha ao ${operacao} (Ecletica):`);

    if(dados.cod) msgErro = String(`${msgErro} ${dados.cod} - `)

    if(dados.dados_adicionais && dados.dados_adicionais.motivo){
      msgErro = String(`${msgErro} ${dados.dados_adicionais.motivo}`)

    } else if(dados.msg) {
      msgErro = String(`${msgErro} ${dados.msg}`)
    } else if(dados.message) {
      msgErro = String(`${msgErro} ${dados.message}`)
    } else {
      msgErro = String(`${msgErro} ${dados}`)
    }
    console.log(msgErro)

    return msgErro;
  }

  private obtenhaIdentificador(pedido: Pedido) {
    let prefixo = String(`${pedido.empresa.id}${pedido.codigo}`);

    //considera reenvio, por conta de bug que prende o codigo da transação quando da erro, gerar codigo diferente
    if(moment().diff(moment(pedido.horario), 'm') >= 1){
      let aletorio = Math.floor(Math.random() * 500).toString().padStart(3, '0')

      return String(`${prefixo}T${aletorio}${this.identificacao}`);
    }

    return String(`${prefixo}${this.identificacao}`);
  }

  private notifiquePedidoEmPreparacao(pedido: any, empresa: any) {

    if(pedido.fazParteMultipedido()) return;

    ExecutorAsync.execute( async (cbAsync: any) => {
      let contexto = require('domain').active.contexto;
      contexto.empresa = empresa
      contexto.idEmpresa =  empresa.id;
      await new PedidoService().altereStatus(pedido, empresa, EnumStatusPedido.EmPreparacao, true, false);
      await IntegracaoFoodyDelivery.notifiqueSistemaDelivery(pedido, empresa, null);
      cbAsync();
    }, () => {});
  }

  obtenhaTipoDeOrigem(): EnumTipoDeOrigem {
    return EnumTipoDeOrigem.ImportadoEcletica;
  }

  private   trateRetorno(response: any ) {
    console.log('retorno resposta enviar pedido: ')
    console.log( response.status)
    console.log( response.data)

    if(response.data.cod === '000' ){
      console.log(response.data.dados_adicionais) //prev_entrega
      return null;
    } else {
      let estaEmProcessamento = response.data.dados_adicionais &&
        response.data.dados_adicionais.pedido_em_processamento === true;

      if(response.data.cod  === '006' && estaEmProcessamento){ //Loja sem internet e estaEmProcessamento
        console.log('Esta em processamento, pode aguardar e salvar codigo' )
       return null;
      } else {
       return this.retornoErro('enviar pedido', response.data);
      }
    }
  }
}
