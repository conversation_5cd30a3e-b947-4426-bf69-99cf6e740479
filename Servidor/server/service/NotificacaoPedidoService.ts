import {NotificacaoPedidoFoodyDelivery} from "../domain/integracoes/NotificacaoPedidoFoodyDelivery";
import {PedidoService} from "./PedidoService";
import {EnumStatusPedido} from "../lib/emun/EnumStatusPedido";
import {MapeadorDePedidoIntegrado} from "../mapeadores/MapeadorDePedidoIntegrado";
import {Pedido} from "../domain/delivery/Pedido";
import {MapeadorDeNotificacaoPedido} from "../mapeadores/MapeadorDeNotificacaoPedido";
import {NotificacaoPedido} from "../domain/integracoes/NotificacaoPedido";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {NotificacaoPedidoEcletica} from "../domain/integracoes/NotificacaoPedidoEcletica";
import {NotificacaoPedidoBluesoft} from "../domain/integracoes/NotificacaoPedidoBluesoft";
import {NotificacaoPedidoSaipos} from "../domain/integracoes/NotificacaoPedidoSaipos";
import {NotificacaoPedidoGcom} from "../domain/integracoes/NotificacaoPedidoGcom";
import {MultipedidoService} from "./MultiPedidoService";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {OrderEvent} from "../domain/opendelivery/OrderEvent";
import {EventType, MapStatusOpenDeliveryEventType} from "../lib/integracao/opendelivery/EventType";
import {EnumStatusPedidoOpenDelivery} from "../lib/integracao/opendelivery/EnumStatusPedidoOpenDelivery";
import {NotificacaoIfood} from "../domain/integracoes/NotificacaoIfood";
import {DeParaEventsStatusPedido} from "./integracoes/EventsIfoodEnum";
import {HistoricoPedido} from "../domain/delivery/HistoricoPedido";
import {EnumResultadoDisputa} from "../domain/integracoes/PedidoDisputa";
import {NotificacaoIfoodDelivery} from "../domain/integracoes/NotificacaoIfoodDelivery";
import {NotificacaoDeliveryService} from "./NotificacaoDeliveryService";
import {
  EnumStatusPedidoSaipos, MapStatusPedidoBlueSoft,
  MapStatusPedidoBlueSoftIgnorar,
  MapStatusPedidoEcletica,
  MapStatusPedidoFoodyDelivery,
  MapStatusPedidoFoodyDeliveryIgnorar,
  MapStatusPedidoGCOM,
  MapStatusPedidoOpenDelivery,
  MapStatusPedidoSaipos
} from "./EnumStatusPedidosIntegrados";
import {PagamentoPedido} from "../domain/delivery/PagamentoPedido";
import {Empresa} from "../domain/Empresa";
import {ERedeItauApi} from "../lib/ERedeItauApi";

let errosIgnorar: any = [];


export class NotificacaoPedidoService{
  static async execute(notificacao: NotificacaoPedido){
    if(notificacao.origem === 'foodydelivery' )
      await NotificacaoPedidoService.executeFoodyDelivery(notificacao as NotificacaoPedidoFoodyDelivery)
    else  if(notificacao.origem === 'ecletica' ) {
      await NotificacaoPedidoService.executeEcletica(notificacao as NotificacaoPedidoEcletica);
    } else  if(notificacao.origem === 'gcom' ) {
      await NotificacaoPedidoService.executeGcom(notificacao as NotificacaoPedidoGcom);
    } else  if(notificacao.origem === 'bluesoft' ){
        await NotificacaoPedidoService.executeBlueSoft(notificacao as NotificacaoPedidoBluesoft)
    } else  if(notificacao.origem === 'saipos' ){
      await NotificacaoPedidoService.executeSaipos(notificacao as NotificacaoPedidoSaipos)
    } else {
      throw Error ('Tipo norificação não esperada: ' + notificacao.origem);
    }
  }

  static executeOpenDelivery(notificacao: any){
    return new Promise<void>( async(resolve, reject) => {
      try{
        let pedido: Pedido = await notificacao.obtenhaPedido();

        if(!pedido) throw Error ('Pedido não existe: ' + notificacao.codigo);
        let erroAlterar: any;
        let contexto = require('domain').active.contexto;

        contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( pedido.empresa.id);
        contexto.idEmpresa =  contexto.empresa.id;

        if(notificacao.status === EnumStatusPedidoOpenDelivery.denyCancellation){
          let orderEvent: OrderEvent = await OrderEvent.obtenha({ orderId: pedido.guid, tipo:  EventType.ORDER_CANCELLATION_REQUEST})

          if(orderEvent){
            let dados: any = notificacao.getDados();

            let reasonDeny = String(`Motivo: ${dados.reason}, codigo: ${dados.code}`);

           await orderEvent.rejeitouCancelamento(reasonDeny);

          } else {
            throw Error('Nenhum evento cancelametno foi lançado para pedido ' + pedido.id)
          }

        } else if(notificacao.status ===  EnumStatusPedidoOpenDelivery.requestCancellation){
          let orderEvent = new OrderEvent(pedido.guid,  EventType.CANCELLATION_REQUESTED,  contexto.empresa);
          await orderEvent.salve(true);

          let dadosCancelamento: any = notificacao.getDados();

          new PedidoService().avalieRequisicaoCancelamento(pedido, contexto.empresa, dadosCancelamento)
        } else {
          let novoStatus: any = MapStatusPedidoOpenDelivery.get(notificacao.status.toString())

          if(!novoStatus) throw Error ('Status não esperado: ' + notificacao.status);

          let dados: any = notificacao.getDados()

          if(notificacao.status ===  EnumStatusPedidoOpenDelivery.cancelled){
            let orderEvent: OrderEvent = await OrderEvent.obtenha({ orderId: pedido.guid, tipo:  EventType.ORDER_CANCELLATION_REQUEST})

            if(orderEvent){
              let erroCancelar = await new PedidoService().cancelePedidoAguardando(pedido,   contexto.empresa, null, true,
                orderEvent.reason)

              if(erroCancelar)    throw Error(erroCancelar)
            } else {
              throw Error('Nenhum evento cancelametno foi lançado para pedido ' + pedido.id)
            }
          } else {
            if(dados.orderExternalCode || dados.preparationTime ){
              if(dados.orderExternalCode)
                pedido.referenciaExterna = dados.orderExternalCode;

              if(dados.preparationTime)
                pedido.tempoPreparacao = dados.preparationTime;

              await new MapeadorDePedido().atualizeDadosConfirmacao(pedido)
            }

            erroAlterar =
              await new PedidoService().altereStatus(pedido, contexto.empresa, novoStatus,
                true, true, null, null,  null);

            if(erroAlterar)    throw Error(erroAlterar)
          }
        }

        await notificacao.foiExecutada( );
      } catch (e) {
        console.error(e)
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }

      resolve();
    })
  }

  static executeGcom(notificacao: NotificacaoPedidoGcom){
    return new Promise<void>( async(resolve, reject) => {

      try{
        let pedido: Pedido = await notificacao.obtenhaPedido();

        if(!pedido) throw Error ('Pedido não existe: ' + notificacao.codigo);

        // @ts-ignore
        let novoStatus: any = MapStatusPedidoGCOM.get(notificacao.status.toString())

        if(!novoStatus) throw Error ('Status GCOM não esperado: ' + notificacao.status);

        let contexto = require('domain').active.contexto;

        contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( pedido.empresa.id);
        contexto.idEmpresa =  contexto.empresa.id;

        if(pedido.ehNovo() && novoStatus === EnumStatusPedido.Cancelado){ // nao cancelar pedido para exibir erros integraçao

          let motivo = notificacao.getDados().motivo || 'Verifique no MyOrders (GCOM) a causa do erro e informe no grupo de suporte';

          let erroIntegracao =  String(`Falha ao integrar com GCOM: ${motivo}`)

          pedido.erroExterno = erroIntegracao;
          await new MapeadorDePedido().atualizeErroExterno(pedido);

        } else {

          if(pedido.erroExterno){ //remover erro integração
            pedido.erroExterno = null
            await new MapeadorDePedido().atualizeErroExterno(pedido);
          }

          let erroAlterar: any =
            await new PedidoService().altereStatus(pedido, contexto.empresa, novoStatus,
              true, true, null, null, notificacao.obtenhaCupomFiscal());

          if(erroAlterar)   throw Error(erroAlterar)

        }


        await notificacao.foiExecutada( );

      } catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }
      resolve();
    })
  }

  static async executeNovoPedidoIfood(notificacaoNovoPedido: NotificacaoIfood, ifoodService: any){
    let order: any =
     await  ifoodService.obtenhaPedido(notificacaoNovoPedido.orderId).catch( async (err: any) => {
         await notificacaoNovoPedido.registreFalhaImportarNovoPedido(err, { orderId: notificacaoNovoPedido.orderId})
    })

    if(order){
      try{
        let idLoja = order.merchant.id;
        let empresa: any = await new MapeadorDeEmpresa().selecioneSync({idLojaIfood: idLoja});

        if(!empresa) throw Error('Nenhuma empresa vinculada  a loja do ifood: ' + idLoja)

        let contexto = require('domain').active.contexto;

        contexto.empresa = empresa;
        contexto.idEmpresa = empresa.id;

        let pedido: Pedido, geradoForaIfood = order.salesChannel === 'POS';

        //O pedido de fora do iFood registrado é disponibilizado no polling? Sim
        //O campo salesChannel ="POS" identifica pedidos que foram importados e vieram de outro canal de aquisição.
        if(geradoForaIfood){ //pedido de veio outra plataforma, promokit por exemplo...
          if(!ifoodService.integracaoIfood.shippingApi) {
            console.log('ignorar notificação, shippingApi não foi ativada no promokit')
            return;
          }

          pedido = await new MapeadorDePedido().selecioneSync({ referenciaExterna: order.id})

          if(!pedido) {
            console.log('ignorar notificação, pedido gerado for do ifood por outro canal d evenda')
            //  throw Error('Pedido gerado por outro canal de venda: ' + order.id)
            return;
          }

          await pedido.atualizeRetornoDeliveryIfood(order, idLoja, empresa);
        } else{
          if(!ifoodService.integracaoIfood.orderApi) {
            console.log('ignorar notificação, orderApi não foi ativada no promokit')
            return;
          }
          pedido =  await new PedidoService().salvePedidoIfood(order, empresa);
        }

        if(pedido){
          await    notificacaoNovoPedido.foiExecutada();

          if(empresa.aceitarPedidoAutomatico || geradoForaIfood){
            let confirmado = await ifoodService.confirmePedido(pedido.referenciaExterna).catch((erroConfirmar: any) => {
              console.log('Falha confirmar pedido ifood')
              console.error(erroConfirmar)
            })

            if(confirmado && !geradoForaIfood)
              await new PedidoService().executeOperacoesPedidoAceito(pedido, empresa);
          }
        }
      } catch (error){
        await notificacaoNovoPedido.registreFalhaImportarNovoPedido(error, order)
      }
    }
  }



  static async executeIfood(notificacao: any,  ifoodService: any){
    try{
      console.log('Executar noticação ifood: ' + notificacao.id)

      if( notificacao instanceof NotificacaoIfoodDelivery){
        await NotificacaoDeliveryService.executeIfoodDelivery(notificacao, ifoodService)
      } else if(notificacao.ehNovoDePedido()){
        await NotificacaoPedidoService.executeNovoPedidoIfood(notificacao, ifoodService)
      } else {
        let erro: any;

        let pedido: Pedido = await notificacao.obtenhaPedido();

        if(!pedido)  throw Error('Nenhum pedido associado a order: ' +  notificacao.orderId)

        let contexto = require('domain').active.contexto;

        let empresa: any = await new MapeadorDeEmpresa().selecioneSync(pedido.empresa.id);
        contexto.empresa =  empresa;
        contexto.idEmpresa =  empresa.id;

        if(notificacao.pedidoConfirmado()){
          erro  = await new PedidoService().aceitePedido(pedido, empresa,  null);

        } else if(notificacao.repostaSolicitacaoCancelamento()) {
          let dados: any = notificacao.getDados();
          let motivoCanacelamento =  dados.reasonDeny || 'Pedido não pode ser cancelado no ifood';

          if(notificacao.cancelamentoNegado()){
            let orderEvent: OrderEvent  =
              await OrderEvent.get({orderId: pedido.guid, tipo: EventType.ORDER_CANCELLATION_REQUEST});

            if(orderEvent){ //pdv aguardando resposta ifood
              orderEvent.reasonDeny = motivoCanacelamento;
              await orderEvent.mapeador().atualizeRetorno(orderEvent);
            }

            if(dados.status ===  EnumResultadoDisputa.EXPIROU || dados.TIMEOUT_EVENT)
              await pedido.disputa.atualizeFinalizada(EnumResultadoDisputa.EXPIROU, 'Loja não respondeu a solicitação');
            else {
              if(dados.status === EnumResultadoDisputa.ACEITOU)
                await pedido.disputa.atualizeFinalizada(EnumResultadoDisputa.ACEITOU);

              if(dados.status === EnumResultadoDisputa.REJEITOU)
                await pedido.disputa.atualizeFinalizada(EnumResultadoDisputa.REJEITOU);
            }

            let historico = new HistoricoPedido(pedido, 'Solicitação cancelamento negada ( ifood )',
              null, null)

            await historico.salve(true);
          }

          await notificacao.foiExecutada();
        } else  if(notificacao.disputaComCliente()) {

          if(notificacao.disputaAberta()){
            let dados: any = notificacao.getDados();

            erro =  await new PedidoService().novaDisputaIniciada(pedido, dados, notificacao.disputaAbertaLegado())
          }
        } else if(notificacao.pedidoCancelado()) {
          erro =   await new PedidoService().cancelePedidoDoIfood(pedido, empresa);

        } else {
          // @ts-ignore
          let novoStatus: any = DeParaEventsStatusPedido.get(notificacao.code.toString())

          if(!novoStatus) throw Error ('Status Ifood não esperado: ' + notificacao.code);

          erro =
            await new PedidoService().altereStatus(pedido, contexto.empresa, novoStatus,
              true, true);
        }

        if(!erro){
          await notificacao.foiExecutada();
        } else {
          throw Error(erro)
        }
      }
    } catch (error) {
      console.error(error)
      await notificacao.registreErro(error.message || error)
      console.log('Registrou erro')
    }
  }

  static executeEcletica(notificacao: NotificacaoPedidoEcletica){
    return new Promise<void>( async(resolve, reject) => {
      try{
        let pedido: Pedido = await notificacao.obtenhaPedido();

        if(!pedido) throw Error ('Pedido não existe: ' + notificacao.codigo);

        // @ts-ignore
        let novoStatus: any = MapStatusPedidoEcletica.get(notificacao.status.toString())

        if(!novoStatus) throw Error ('Status Ecletica não esperado: ' + notificacao.status);

        let contexto = require('domain').active.contexto;

        contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( pedido.empresa.id);
        contexto.idEmpresa =  contexto.empresa.id;

        let erroAlterar: any;
        if(!pedido.fazParteMultipedido()){
          erroAlterar =
            await new PedidoService().altereStatus(pedido, contexto.empresa, novoStatus,
              true, true, null, null, notificacao.obtenhaCupomFiscal());

        } else {
            erroAlterar =
            await new MultipedidoService().altereStatusParceiro(pedido, novoStatus,  notificacao.obtenhaCupomFiscal());
        }

        if(erroAlterar)   throw Error(erroAlterar)
        await notificacao.foiExecutada( );

      } catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }

      resolve();
    })
  }

  static executeSaipos(notificacao: NotificacaoPedidoSaipos){
    return new Promise<void>( async(resolve, reject) => {
      try{
        let contexto = require('domain').active.contexto;

        let pedido: Pedido = await new MapeadorDePedidoIntegrado().selecioneSync(
          { idEmpresa: notificacao.empresaId, id: notificacao.pedidoId});

        if(!pedido) throw Error ('Pedido não existe: ' + notificacao.codigo);

        // @ts-ignore
        let novoStatus: any = MapStatusPedidoSaipos.get(notificacao.status)

        if(!novoStatus) throw Error ('Status não esperado: ' + notificacao.status);

        if(novoStatus === EnumStatusPedidoSaipos.Despachado && !pedido.ehDelivery())
           novoStatus = EnumStatusPedido.Pronto

        contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( pedido.empresa.id);
        contexto.idEmpresa =  contexto.empresa.id;

        let erroAlterar: any =
          await new PedidoService().altereStatus(pedido, contexto.empresa, novoStatus, true, true);

        if(erroAlterar)   throw Error(erroAlterar)

        await notificacao.foiExecutada( );

      } catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }
      resolve();
    });
  }

  static executeBlueSoft(notificacao: NotificacaoPedidoBluesoft){
    return new Promise<void>( async(resolve, reject) => {
      try{
        let contexto = require('domain').active.contexto;

        let pedido: Pedido = await new MapeadorDePedidoIntegrado().selecioneSync(
          { idEmpresa: notificacao.empresaId, id: notificacao.pedidoId});

        if(!pedido) throw Error ('Pedido não existe: ' + notificacao.codigo);

        if(MapStatusPedidoBlueSoftIgnorar.get(notificacao.status)){
          console.log(String(`ignorar notificação  pedido delivery: ${notificacao.status}`) )
          await notificacao.foiExecutada( );
          return resolve()
        }

        // @ts-ignore
        let novoStatus: any = MapStatusPedidoBlueSoft.get(notificacao.status)

        if(!novoStatus) throw Error ('Status não esperado: ' + notificacao.status);

        contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( pedido.empresa.id);
        contexto.idEmpresa =  contexto.empresa.id;

        let erroAlterar: any =
          await new PedidoService().altereStatus(pedido, contexto.empresa, novoStatus, true, true);

        if(erroAlterar)   throw Error(erroAlterar)

        await notificacao.foiExecutada( );
      } catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }

      resolve();
    })
  }

  static executeFoodyDelivery(notificacao: NotificacaoPedidoFoodyDelivery){
    return new Promise<void>( async(resolve, reject) => {
      try{
        if(notificacao.novoPedido()){
          console.log(String(`criou um novo pedido: ${notificacao.codigo}`) )
          console.log(notificacao)
          return resolve()
        }
        if(notificacao.altercaoDeStatus())
          throw Error ('Tipo de notificação nao esperado: ' + notificacao.obtenhaTipoDeNotificacao());

        let pedido: Pedido = await new MapeadorDePedidoIntegrado().selecioneSync(
          { codigoExterno: notificacao.codigo});

        if(!pedido) throw Error ('Pedido não existe: ' + notificacao.codigo);

        if(MapStatusPedidoFoodyDeliveryIgnorar.get(notificacao.status)){
          console.log(String(`ignorar notificação pedido delivery: ${notificacao.status}`) )
          await notificacao.foiExecutada( );
          return resolve()
        }

        // @ts-ignore
        let novoStatus: any = MapStatusPedidoFoodyDelivery.get(notificacao.status)

        if(!novoStatus) throw Error ('Status não esperado: ' + notificacao.status);

        let contexto = require('domain').active.contexto;

        contexto.empresa = await new MapeadorDeEmpresa().selecioneSync( pedido.empresa.id);
        contexto.idEmpresa =  contexto.empresa.id;

        let erroAlterar: any =
          await new PedidoService().altereStatus(pedido, contexto.empresa, novoStatus, true, true);

        if(erroAlterar)   throw Error(erroAlterar)

        await notificacao.foiExecutada( );
      } catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0);
      }

      resolve();
    })
  }

  static async executePendentes( ) {
    return new Promise<void>( async(resolve, reject) => {
      let notificacoes: any = await new MapeadorDeNotificacaoPedido().listeAsync({naoExecutadas: true});

      console.log('executar  notificações pedido pendentes: ' + notificacoes.length)
      console.log(new Date())

      let contador = 1;

      for(let notificacao of notificacoes){
        console.log('notificação executar: ' + contador + ' => ' + notificacao.id);

        contador++

        await NotificacaoPedidoService.execute(notificacao)

      }

      console.log('terminou de executar')
      console.log(new Date())

      resolve();
    })
  }

  static async executeEredePostback(empresa: Empresa, pedido: Pedido, pagamento: PagamentoPedido){
    return new Promise<void>( async(resolve, reject) => {
      let formaDePagamento = pagamento.formaDePagamento;

      let contexto = require('domain').active.contexto;

      contexto.empresa = empresa
      contexto.idEmpresa = empresa.id;

      if(formaDePagamento){
        await new ERedeItauApi(formaDePagamento.configMeioDePagamento).sincronizePagamento(pedido, pagamento, empresa );
      } else {
        console.log('Nenhuma forma de pagamento')
      }


    })
  }
}
