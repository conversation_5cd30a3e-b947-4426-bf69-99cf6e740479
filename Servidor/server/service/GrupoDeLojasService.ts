import {MapeadorDeGrupoDeLojas} from "../mapeadores/MapeadorDeGrupoDeLojas";
import {GrupoDeLojas} from "../domain/GrupoDeLojas";

export class GrupoDeLojasService {
  private static _instancia: GrupoDeLojasService = new GrupoDeLojasService();

  static Instancia(): GrupoDeLojasService {
    return GrupoDeLojasService._instancia;
  }

  private constructor() {

  }

  public async carregue() {
  }

  public async inicialize() {
  }

  public async obtenha(dominio: string): Promise<GrupoDeLojas> {
    await this.inicialize();

    const mapeador = new MapeadorDeGrupoDeLojas();

    return await mapeador.obtenha(dominio);
  }

  public async obtenhaPorId(id: number): Promise<GrupoDeLojas> {
    await this.inicialize();

    const mapeador = new MapeadorDeGrupoDeLojas();
    return await mapeador.obtenhaPorId(id);
  }
}
