{"version": "6", "id": "cl5h1o2le602909mktioc9dk0", "name": "My typebot", "events": [{"id": "cl5h1o2la000009mk2nyph7jv", "outgoingEdgeId": "km1tn39ymdp9o1mzcawkx7qr", "graphCoordinates": {"x": -56.67, "y": -18.67}, "type": "start"}], "groups": [{"id": "cl5h1ogbv001k356iqgvwrdbp", "title": "Group #1", "graphCoordinates": {"x": 251.53, "y": -34.08}, "blocks": [{"id": "bgytejnpo4i82qa01ml0mwji", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "Olá! Bem-vindo ao {{nome_empresa}}. Como podemos ajudar? "}]}]}}, {"id": "s7ydgpp6mna8z3u5ahiig214", "type": "choice input", "items": [{"id": "bxxyvo7nyzqsen47kj931rk4", "outgoingEdgeId": "fed55cuajs4ev5k8zyoyitrh", "content": "Fazer Um Pedido"}, {"id": "mr386e33lmf6pnlhpu469r03", "content": "<PERSON><PERSON>"}, {"id": "eyiq30839jh89mcwvglloeld", "content": "Falar com Atendente"}], "options": {"variableId": "vr5i3wnccz8ha9g7trhwcdo3l", "isMultipleChoice": false, "buttonLabel": "Send", "isSearchable": false, "searchInputPlaceholder": "Filter the options..."}}]}, {"id": "egymexwd1teaii9q7slcywie", "title": "Group #2", "graphCoordinates": {"x": 761.86, "y": 26.69}, "blocks": [{"id": "pedycix3ghtrt5ij4pvsgunu", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "Qual forma de entrega?"}]}]}}, {"id": "sps48hzw69jbhudxwcnus6oj", "type": "choice input", "items": [{"id": "lzc048heb5j3a5um7x3ofgtg", "outgoingEdgeId": "d2unhi2fn1yxb4h5nz8zo8nd", "content": "Delivery"}, {"id": "zdu05mhhm5dvqyn5ac5fld4t", "content": "Re<PERSON><PERSON>"}, {"id": "ngxhlomevj4acfy1gq5iafbz", "content": "Comer <PERSON>"}], "options": {"variableId": "vsomop2x5oz794qvmt19ncnkg", "isMultipleChoice": false, "isSearchable": false}}]}, {"id": "s7hlff8xzmvr6x6hqnhisf63", "title": "Group #3", "graphCoordinates": {"x": 1254.77, "y": 12.6}, "blocks": [{"id": "omv3qj8p0jft34w4i87eys39", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "<PERSON><PERSON><PERSON>, você escolheu delivery. Por favor, poderia compartilhar sua localização atual? "}]}]}}, {"id": "ny38ess1xfwtgs3i9qf456m9", "outgoingEdgeId": "wf0pmg7eidi685fp9dyhx0r0", "type": "text input", "options": {"labels": {"placeholder": "localizacao", "button": "Enviar"}, "variableId": "vvo2i6zgu7pe1abomna79epf3", "isLong": false}}]}, {"id": "pwyisq9ewn1hengj1oll6aap", "title": "Group #4", "graphCoordinates": {"x": 2147.71, "y": 318.69}, "blocks": [{"id": "ca8uq9g70f4td3u2h4vl9no3", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "Agora, escolha a forma de pagamento"}]}]}}, {"id": "gy6qyorqp0hef8kfdpi8l1nn", "outgoingEdgeId": "wegm7dm0ccjwrvz3k5v6020m", "type": "choice input", "items": [{"id": "p968b9eqy8hv187umas6swuq", "content": "<PERSON><PERSON><PERSON>"}, {"id": "fojct5hdu9f9rd8esx6sbs7r", "content": "Cartão de Crédito"}, {"id": "fvzhm6limpa2a09wkjjenxhd", "content": "Débito"}, {"id": "a8l3ket957gba5xum0pflau3", "content": "Pix"}, {"id": "llw158karakrwksntxefv4v3", "content": "Pagar Online"}], "options": {"variableId": "vx0u67vxlzvefgbsyhb2iwi6c"}}]}, {"id": "rai4r29zfhniwxgsa4kk9yud", "title": "Group #5", "graphCoordinates": {"x": 1768.99, "y": 45.1}, "blocks": [{"id": "nnva1q39f5wl0cwnro10orum", "outgoingEdgeId": "jlvs7ph6wvyfqevg6ha5l5jc", "type": "Condition", "items": [{"id": "gbut1hyh3387q45dz9uuab6t", "outgoingEdgeId": "r3667i65clr1uqgoi1a8b0u5", "content": {"comparisons": [{"id": "wxmuqwjyouwy5zcd5lgrun58", "variableId": "va3l9k1v79zsaju8iwh1ltqlk"}]}}]}]}, {"id": "zdpczhoob4w73decjbx3hsnh", "title": "Group #6", "graphCoordinates": {"x": 2603.94, "y": -72.9}, "blocks": [{"id": "ipv3ton8awl6r1ogmp35c9z6", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "Quando você deseja <PERSON>?"}]}]}}, {"id": "hj8zy6o4pv7p3beh80vsx80t", "type": "choice input", "items": [{"id": "cg794bhhxo821wq0lv1wtz7v", "outgoingEdgeId": "ztxh1lb50c4p6hc65jd4o1y4", "content": "<PERSON><PERSON><PERSON>"}, {"id": "ferttcn6s87ja49q1wem2bkr", "outgoingEdgeId": "aycbr1iwr2s4ee1hx7cxaxoi", "content": "Agendar"}]}]}, {"id": "ctukwejh7073vqah2qqk6g6n", "title": "Group #8", "graphCoordinates": {"x": 3135.337002360027, "y": 134.7711094156904}, "blocks": [{"id": "uqr11hb6nj50z3w1bwq1gxr0", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "Me informe o dia e horário que deseja receber. "}]}, {"type": "p", "children": [{"text": ""}]}]}}, {"id": "j07yllqukm7dmq17qyze8bgx", "type": "choice input", "items": [{"id": "dl1t5vq1mzyi6grvli93al5n", "outgoingEdgeId": "ugsvqmeftym8933l8mx9f2ps", "content": "<PERSON><PERSON> (22/10)"}, {"id": "sry0w4jmf85hvogw9m2xlyr0", "outgoingEdgeId": "e03pe52hbu8lwfm2g0wriq2o", "content": "<PERSON><PERSON><PERSON><PERSON> (23/10)"}, {"id": "dr68750ztlx2gg1svdb3di5s", "outgoingEdgeId": "j3pap050ya03uwg4kqx3fegl", "content": "Quarta 24/10", "displayCondition": {"isEnabled": false}}, {"id": "gr2432mibzvk2r6f6z2ria18", "content": "Quinta 25/10"}, {"id": "daa77u3evi7gpah9a85o831n", "outgoingEdgeId": "f1n6n6wi34jw953wdyplfpfj", "content": "Outra Data"}]}]}, {"id": "ukf87t0thslqhf6s2i4nkmgu", "title": "Group #8", "graphCoordinates": {"x": 3662.71, "y": 582.88}, "blocks": [{"id": "wwc2awd4p0esumz3gnhkta5g", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "Para qual dia você deseja o seu pedido?"}]}, {"type": "p", "children": [{"text": "Exemplo: 22/10/2024"}]}]}}, {"id": "myzgd940madig8w78ens1ujt", "outgoingEdgeId": "t1ynxw9zl6e11xoh2eigcni1", "type": "text input", "options": {"variableId": "vy6z1anv9rlgdl6cjugmihxqw"}}]}, {"id": "jg8nt56b7nos1yf4btqx9s64", "title": "Group #9", "graphCoordinates": {"x": 3643.337124430339, "y": 255.7710483805341}, "blocks": [{"id": "fs7y74znh48j7p0mpm1cngu4", "outgoingEdgeId": "mwzen1fwud0h7ueqqzzk5wdm", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "Qual horário você deseja? Exemplo: 16:00 (4 da tarde)"}]}, {"type": "p", "children": [{"text": ""}]}]}}]}, {"id": "fhyyim8z8j82u4did6rwysal", "title": "Group #10", "graphCoordinates": {"x": 2569.62, "y": 983.1}, "blocks": [{"id": "d513q1pr67pomg44h3u48uit", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "Como você prefere finalizar seu pedido? "}]}]}}, {"id": "zeyfbydnhhglsxoyybpj2pet", "type": "choice input", "items": [{"id": "lu0tv2yzutftaxx4xczz26nz", "outgoingEdgeId": "sbzx28kbyyop3gu5u3qog3jt", "content": "Me envie o <PERSON>"}, {"id": "vj368m1uohjb5gtt2wa4re5k", "content": "Falar com Atendente"}]}]}, {"id": "w1kjyjjv8a1pnj5xonwnh816", "title": "Group #11", "graphCoordinates": {"x": 3052.622472505117, "y": 1111.104361368815}, "blocks": [{"id": "v6lcrtcbpug7vmnttbzbfa2n", "type": "text", "content": {"richText": [{"type": "p", "children": [{"text": "[link]"}]}]}}]}], "edges": [{"id": "d2unhi2fn1yxb4h5nz8zo8nd", "from": {"blockId": "sps48hzw69jbhudxwcnus6oj", "itemId": "lzc048heb5j3a5um7x3ofgtg"}, "to": {"groupId": "s7hlff8xzmvr6x6hqnhisf63", "blockId": "omv3qj8p0jft34w4i87eys39"}}, {"id": "jlvs7ph6wvyfqevg6ha5l5jc", "from": {"blockId": "nnva1q39f5wl0cwnro10orum"}, "to": {"groupId": "pwyisq9ewn1hengj1oll6aap"}}, {"id": "r3667i65clr1uqgoi1a8b0u5", "from": {"blockId": "nnva1q39f5wl0cwnro10orum", "itemId": "gbut1hyh3387q45dz9uuab6t"}, "to": {"groupId": "zdpczhoob4w73decjbx3hsnh", "blockId": "ipv3ton8awl6r1ogmp35c9z6"}}, {"id": "ztxh1lb50c4p6hc65jd4o1y4", "from": {"blockId": "hj8zy6o4pv7p3beh80vsx80t", "itemId": "cg794bhhxo821wq0lv1wtz7v"}, "to": {"groupId": "pwyisq9ewn1hengj1oll6aap"}}, {"id": "aycbr1iwr2s4ee1hx7cxaxoi", "from": {"blockId": "hj8zy6o4pv7p3beh80vsx80t", "itemId": "ferttcn6s87ja49q1wem2bkr"}, "to": {"groupId": "ctukwejh7073vqah2qqk6g6n", "blockId": "uqr11hb6nj50z3w1bwq1gxr0"}}, {"id": "f1n6n6wi34jw953wdyplfpfj", "from": {"blockId": "j07yllqukm7dmq17qyze8bgx", "itemId": "daa77u3evi7gpah9a85o831n"}, "to": {"groupId": "ukf87t0thslqhf6s2i4nkmgu", "blockId": "wwc2awd4p0esumz3gnhkta5g"}}, {"id": "ugsvqmeftym8933l8mx9f2ps", "from": {"blockId": "j07yllqukm7dmq17qyze8bgx", "itemId": "dl1t5vq1mzyi6grvli93al5n"}, "to": {"groupId": "jg8nt56b7nos1yf4btqx9s64"}}, {"id": "e03pe52hbu8lwfm2g0wriq2o", "from": {"blockId": "j07yllqukm7dmq17qyze8bgx", "itemId": "sry0w4jmf85hvogw9m2xlyr0"}, "to": {"groupId": "jg8nt56b7nos1yf4btqx9s64"}}, {"id": "j3pap050ya03uwg4kqx3fegl", "from": {"blockId": "j07yllqukm7dmq17qyze8bgx", "itemId": "dr68750ztlx2gg1svdb3di5s"}, "to": {"groupId": "jg8nt56b7nos1yf4btqx9s64"}}, {"id": "t1ynxw9zl6e11xoh2eigcni1", "from": {"blockId": "myzgd940madig8w78ens1ujt"}, "to": {"groupId": "jg8nt56b7nos1yf4btqx9s64"}}, {"id": "mwzen1fwud0h7ueqqzzk5wdm", "from": {"blockId": "fs7y74znh48j7p0mpm1cngu4"}, "to": {"groupId": "pwyisq9ewn1hengj1oll6aap", "blockId": "ca8uq9g70f4td3u2h4vl9no3"}}, {"id": "km1tn39ymdp9o1mzcawkx7qr", "from": {"eventId": "cl5h1o2la000009mk2nyph7jv"}, "to": {"groupId": "cl5h1ogbv001k356iqgvwrdbp", "blockId": "bgytejnpo4i82qa01ml0mwji"}}, {"id": "wegm7dm0ccjwrvz3k5v6020m", "from": {"blockId": "gy6qyorqp0hef8kfdpi8l1nn"}, "to": {"groupId": "fhyyim8z8j82u4did6rwysal"}}, {"id": "sbzx28kbyyop3gu5u3qog3jt", "from": {"blockId": "zeyfbydnhhglsxoyybpj2pet", "itemId": "lu0tv2yzutftaxx4xczz26nz"}, "to": {"groupId": "w1kjyjjv8a1pnj5xonwnh816"}}, {"id": "wf0pmg7eidi685fp9dyhx0r0", "from": {"blockId": "ny38ess1xfwtgs3i9qf456m9"}, "to": {"groupId": "rai4r29zfhniwxgsa4kk9yud"}}, {"id": "fed55cuajs4ev5k8zyoyitrh", "from": {"blockId": "s7ydgpp6mna8z3u5ahiig214", "itemId": "bxxyvo7nyzqsen47kj931rk4"}, "to": {"groupId": "egymexwd1teaii9q7slcywie"}}], "variables": [{"id": "vy6z1anv9rlgdl6cjugmihxqw", "name": "data_de_entrega", "isSessionVariable": false}, {"id": "va3l9k1v79zsaju8iwh1ltqlk", "name": "possui_agendamento", "isSessionVariable": true}, {"id": "vx0u67vxlzvefgbsyhb2iwi6c", "name": "forma_pagamento", "isSessionVariable": false}, {"id": "vvo2i6zgu7pe1abomna79epf3", "name": "localizacao", "isSessionVariable": false}, {"id": "vsomop2x5oz794qvmt19ncnkg", "name": "forma_de_entrega", "isSessionVariable": false}, {"id": "vr5i3wnccz8ha9g7trhwcdo3l", "name": "opcao_escolhida", "isSessionVariable": false}, {"id": "vcvdx36d3geewmezfy3sz90je", "name": "nome", "isSessionVariable": true}], "theme": {"general": {"font": "Open Sans", "background": {"type": "Color", "content": "#FFFFFF"}}, "chat": {"hostAvatar": {"isEnabled": true, "url": "https://lh3.googleusercontent.com/a-/AFdZucqTFhBYKpJZtJoZh0Kl-tvro59JOXUC566KHsoZGA=s96-c"}, "hostBubbles": {"backgroundColor": "#F7F8FF", "color": "#303235"}, "guestBubbles": {"backgroundColor": "#FF8E21", "color": "#FFFFFF"}, "buttons": {"backgroundColor": "#0042DA", "color": "#FFFFFF"}, "inputs": {"backgroundColor": "#FFFFFF", "color": "#303235", "placeholderColor": "#9095A0"}}}, "selectedThemeTemplateId": null, "settings": {"general": {"isBrandingEnabled": true, "isInputPrefillEnabled": true, "isHideQueryParamsEnabled": true, "isNewResultOnRefreshEnabled": false}, "typingEmulation": {"enabled": true, "speed": 300, "maxDelay": 1.5}, "metadata": {"description": "Build beautiful conversational forms and embed them directly in your applications without a line of code. Triple your response rate and collect answers that has more value compared to a traditional form."}, "publicShare": {"isEnabled": true}}, "createdAt": "2022-07-11T17:54:45.410Z", "updatedAt": "2024-10-28T18:45:37.529Z", "icon": null, "folderId": null, "customDomain": null, "workspaceId": "cl5h16vts303809kykk2q17ps", "resultsTablePreferences": null, "isArchived": false, "isClosed": false, "whatsAppCredentialsId": null, "riskLevel": null}