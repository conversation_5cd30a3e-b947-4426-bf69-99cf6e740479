import axios from 'axios';
import * as fs from 'fs';
import {Empresa} from "../../domain/Empresa";
import {Contato} from "../../domain/Contato";
import {RespostaChatbotDuvidas} from "./RespostaChatbotDuvidas";
import {ConfiguracoesMia} from "../../domain/chatbot/ConfiguracoesMia";
import * as path from "path";
import {FormaDeEntregaEmpresa} from "../../domain/delivery/FormaDeEntregaEmpresa";
import { ChatBotDuvidasService } from './ChatBotDuvidasService';
import { EstadoChatbot } from '../../domain/chatbot/EstadoChatbot';

export class TypebotService {
  private typebotApiUrl: string;

  constructor() {
    this.typebotApiUrl = 'https://promokit.app.br:8443';
  }

  async inicieFluxo(configuracoesMia: ConfiguracoesMia, idFluxoTypebot: string, empresa: Empresa,
                    contato: Contato, mensagem: string): Promise<RespostaChatbotDuvidas> {
    try {
      let formasDeEntrega: any =  ['Escolha a forma de entrega'];
      empresa.formasDeEntrega.forEach((forma: FormaDeEntregaEmpresa) => {
        if( !forma.ativa ) {
          return;
        }
        formasDeEntrega.push(forma.formaDeEntrega.nome);
      });

      const respostaFormasDePagamento: any = ChatBotDuvidasService.calculeFormasDePagamento(empresa);

      const url = `${this.typebotApiUrl}/api/v1/typebots/${idFluxoTypebot}/startChat`;

      const response = await axios.post(
        url,
        {
          prefilledVariables: {
            url_servidor: 'https://fibo.promokit.com.br/api/v1',
            empresa: empresa.nome,
            nome_empresa: empresa.nome,
            novo_cliente: contato.id ? 'false' : 'true',
            possivel_nome_cliente: contato.nome,
            nome_cliente: contato.nome,
            telefone: contato.telefone,
            formas_de_entrega: formasDeEntrega,
            formas_de_pagamento: respostaFormasDePagamento.entrega
          },
          message: mensagem,
        },
        {
          headers: {
            Authorization: `Bearer ${configuracoesMia.chaveApiTypebot}`,
          },
        }
      );

      return this.processarRespostaTypebot(response.data);
    } catch (error) {
      console.error('Erro ao iniciar fluxo do Typebot:', error);
      throw error;
    }
  }

  async continuarFluxo(configuracoesMia: ConfiguracoesMia, empresa: Empresa, contato: Contato,
                       estado: EstadoChatbot, mensagem: string): Promise<RespostaChatbotDuvidas> {
    try {
      const url =
        `${this.typebotApiUrl}/api/v1/sessions/${estado.idSessaoTypebot}/continueChat`;

      const response = await axios.post(url,
        {
          message: {
            text: mensagem,
            type: 'text'
          }
        },
        {
          headers: {
            Authorization: `Bearer ${configuracoesMia.chaveApiTypebot}`,
          },
        }
      );

      return this.processarRespostaTypebot(response.data);
    } catch (error) {
      if( error.response.status === 404 ) {
        return RespostaChatbotDuvidas.sucesso('FINALIZAR_TYPEBOT', 'Sessão encerrou', []);
      }
      console.error('Erro ao continuar fluxo do Typebot:', error);
      throw error;
    }
  }

  public static converter(resposta: any): string {
    let resultado = '';

    resposta.forEach((item: any) => {
      if (item.type === 'text' && item.content && item.content.richText) {
        const blocos: string[] = [];
        item.content.richText.forEach((block: any) => {
          if (block.children) {
            let textoBloco = '';
            block.children.forEach((child: any) => {
              if (child.text) {
                textoBloco += child.text;
              } else if (child.type === 'inline-variable' && child.children) {
                child.children.forEach((varChild: any) => {
                  if (varChild.text) {
                    textoBloco += `{${varChild.text}}`;
                  } else if (varChild.children) {
                    varChild.children.forEach((child: any) => {
                      if (child.text) {
                        textoBloco += `${child.text}`;
                      }
                    });
                  }
                });
              }
            });
            blocos.push(textoBloco);
          }
        });
        resultado += blocos.join('\n');
      }
    });

    return resultado;
  }

  private processarRespostaTypebot(typebotResponse: any): RespostaChatbotDuvidas {
    const mensagem = TypebotService.converter(typebotResponse.messages);
    const opcoes = typebotResponse.input ? typebotResponse.input.items?.map((item: any) => ({ id: item.id, label: item.content })) : [];
    const labels = typebotResponse?.input?.options?.labels ? typebotResponse.input.options.labels?.placeholder : '';

    let comando = 'CONTINUAR_BOT';
    if (typebotResponse.isCompleted) {
      comando = 'FINALIZAR_TYPEBOT';
    }

    let resposta = RespostaChatbotDuvidas.sucesso(comando, mensagem, opcoes);

    resposta.tipo = 'texto';
    if( typebotResponse.progress === 100 ) {
      resposta.comando = 'FINALIZAR_TYPEBOT';
    }

    //checa se labels tem o valor localizacao
    if (labels && labels.includes('localizacao')) {
      resposta.tipo = 'localizacao';
    }

    //pega a primeira opção como titulo
    if (resposta.opcoes.length > 0) {
      resposta.opcoes.titulo = resposta.opcoes[0].label;

      //remove a primeira opção
      resposta.opcoes.splice(0, 1);
    }

    resposta.idSessao = typebotResponse.sessionId;
    resposta.idResultadoTypebot = typebotResponse.resultId;

    return resposta;
  }

  async crieFluxosTypebotPadrao(empresa: Empresa, configuracoes: ConfiguracoesMia) {
    try {
      const fluxoWhatsapp = await this.crieFluxo('typebot-export-whatsapp.json', 'whatsapp', empresa, configuracoes);
      const fluxoInstagram = await this.crieFluxo('typebot-export-instagram.json', 'instagram', empresa, configuracoes);

      const fluxoWhatsappPublicado = await this.publiqueFluxo(configuracoes, fluxoWhatsapp.id);
      const fluxoInstagramPublicado = await this.publiqueFluxo(configuracoes, fluxoInstagram.id);

      return {
        whatsapp: {
          ...fluxoWhatsapp,
          publicId: fluxoWhatsapp.publicId
        },
        instagram: {
          ...fluxoInstagram,
          publicId: fluxoInstagram.publicId
        }
      };
    } catch (erro) {
      console.error('Erro ao criar fluxos do Typebot:', erro);
      throw erro;
    }
  }

  async obtenhaVariaveis(configuracoesMia: ConfiguracoesMia, estadoSessao: EstadoChatbot) {
    try {
      const urlResultados = `${this.typebotApiUrl}/api/v1/typebots/${estadoSessao.idFluxoTypebot}/results/${estadoSessao.idResultadoTypebot}`;

      const response = await axios.get(urlResultados, {
        headers: {
          Authorization: `Bearer ${configuracoesMia.chaveApiTypebot}`,
      }
    });

      return response.data.result;
    } catch (erro) {
      console.error('Erro ao obter url dos resultados:', erro);
      throw erro;
    }
  }

  async crieFluxo(nomeArquivo: string, tipo: string, empresa: Empresa, configuracoes: ConfiguracoesMia) {
    try {
      const caminho = path.join(__dirname, "../../templates/" + nomeArquivo);

      const arquivo: any = await fs.promises.readFile(caminho, 'utf8');

      const objeto = JSON.parse(arquivo);
      objeto.name = `${empresa.id} - ${empresa.dominio} - ${tipo}`;

      // Generate a publicId using empresa info and timestamp
      const timestamp = new Date().getTime();
      const publicId = `${empresa.id}-${empresa.dominio}-${tipo}-${timestamp}`;
      objeto.publicId = publicId;

      const response = await axios.post(
        `${this.typebotApiUrl}/api/v1/typebots`,
        {
          workspaceId: configuracoes.workspaceId,
          typebot: objeto
        },
        {
          headers: {
            Authorization: `Bearer ${configuracoes.chaveApiTypebot}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        ...response.data.typebot,
        publicId: publicId
      };
    } catch (erro) {
      console.error('Erro ao criar fluxos do Typebot:', erro);
      throw erro;
    }
  }

  public async publiqueFluxo(configuracoesMia: ConfiguracoesMia, idFluxo: string) {
    try {
      const url = `${this.typebotApiUrl}/api/v1/typebots/${idFluxo}/publish?v=` + new Date().getTime();

      console.log(url);
      const response = await axios.post(url, {
        horario: new Date().toISOString()
      }, {
        headers: {
          Authorization: `Bearer ${configuracoesMia.chaveApiTypebot}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(response.data);

      //await this.atualizePublicId(idFluxo);

      return response.data;
    } catch (erro) {
      console.error('Erro ao publicar fluxo do Typebot:', erro);
      throw erro;
    }
  }

  public async atualizePublicId(configuracoesMia: ConfiguracoesMia, idFluxo: string) {
    try {
      console.log('atualizePublicId', idFluxo);
      const url = `${this.typebotApiUrl}/api/v1/typebots/${idFluxo}`;

      const response = await axios.patch(url, {
        typebot: {
          publicId: idFluxo
        }
      }, {
        headers: {
          Authorization: `Bearer ${configuracoesMia.chaveApiTypebot}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (erro) {
      console.error('Erro ao atualizar fluxo do Typebot:', erro);
      throw erro;
    }
  }

  async obtenhaWorkspaces(chaveApi: string): Promise<any[]> {
    try {
      const response = await axios.get(
        `${this.typebotApiUrl}/api/v1/workspaces`,
        {
          headers: {
            Authorization: `Bearer ${chaveApi}`,
          },
        }
      );

      return response.data.workspaces;
    } catch (erro) {
      console.error('Erro ao obter workspaces do Typebot:', erro);
      throw erro;
    }
  }
}
