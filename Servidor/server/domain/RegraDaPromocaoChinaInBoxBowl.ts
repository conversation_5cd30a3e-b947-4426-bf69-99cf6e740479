import {AplicacaoDeRegra, RegraDaPromocao} from "./RegraDaPromocao";
import {Pedido} from "./delivery/Pedido";
import {Promocao} from "./Promocao";

export class RegraDaPromocaoChinaInBoxBowl extends RegraDaPromocao{
  private codigosBowlsColecionaveis = [
    '20160',
    '20162',
    '20164',
    '20166',
    '39200',
    '39202',
    '39204',
    '39206'
  ]
  constructor(promocao: Promocao, public ativa: boolean = true) {
    super(promocao, 'chinainbox-bowl', 0);
  }
  aplique(pedido: Pedido): AplicacaoDeRegra {
    let possuiBowl = false;
    let possuiOutro = false;
    let qtdBowls = 0;

    if(pedido.itens.length <= 1) return new AplicacaoDeRegra(0)

    for(let item of pedido.itens) {
      if(this.codigosBowlsColecionaveis.indexOf(item.produto.codigoPdv) >= 0) {
        possuiBowl = true
        qtdBowls += item.qtde
        continue;
      }

      if(this.codigosBowlsColecionaveis.indexOf(item.produto.codigoPdv) < 0) {
        possuiOutro = true;
        continue;
      }
    }

    let totalDesconto = qtdBowls * 12



    if(possuiBowl && possuiOutro) {
      pedido.desconto += totalDesconto
      return new AplicacaoDeRegra(totalDesconto)
    }




    return new AplicacaoDeRegra(0);
  }

}
