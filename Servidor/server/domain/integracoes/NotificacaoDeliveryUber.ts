import {NotificacaoDelivery} from "./NotificacaoDelivery";
import {EnumStatusEntregaUber} from "../../lib/integracao/opendelivery/EnumStatusEntregaUber";

export class NotificacaoDeliveryUber extends NotificacaoDelivery{
  constructor(pedido: any, payload: any) {
    super('uber', pedido, payload);
    this.deliveryId  = payload ? payload.id : null;
    this.tipo = payload ? payload.status : null;
  }

  setDados(dados: any) {
    if(dados){
       delete dados.pickup;
       delete dados.dropoff;
       delete dados.manifest_items;
       this.dados = dados;
    }
  }

  obtenhaListaStatus(): any {
    return EnumStatusEntregaUber;
  }

}
