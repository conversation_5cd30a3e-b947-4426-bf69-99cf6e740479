
import {TinyService} from "../../service/TinyService";
import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";
import {IServiceIntegracaoExternaERP} from "./IServiceIntegracaoExternaERP";
import {Pedido} from "../delivery/Pedido";
import {IntegracaoDeliveryComJson} from "./IntegracaoDeliveryComJson";

export class IntegracaoTinyERP extends IntegracaoDeliveryComJson{
  constructor(dados: any) {
    if(dados){
      super(dados.id, dados.empresa, dados.token, 'tiny');
      this.configuracoesEspecificas = {
        enviarPedidosMesasFechamentoComanda:  dados.configuracoesEspecificas &&
          dados.configuracoesEspecificas.enviarPedidosMesasFechamentoComanda
      }
    } else {
      super( null, null, null, 'tiny');
    }
  }

  notificarNovo(pedido: Pedido): boolean {
    if(pedido.comanda && pedido.comanda.id) return false;

    return Number(pedido.status) === EnumStatusPedido.Novo;
  }

  obtenhaCredencial(): any {
    return {token: this.token}
  }

  enviarMesasFecharConanda(){
    return this.configuracoesEspecificas.enviarPedidosMesasFechamentoComanda;
  }

  obtenhaService(): IServiceIntegracaoExternaERP {
    return new TinyService(this.obtenhaCredencial().token);
  }
}
