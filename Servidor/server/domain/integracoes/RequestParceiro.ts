import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeRequestParceiro} from "../../mapeadores/MapeadorDeRequestParceiro";

export class RequestParceiro extends ObjetoPersistente{
  public header: string;
  public payload: string;
  public empresa: any;
  public horario: Date;
  public httpstatus: number;
  public retorno: string;
  public erro: string
  constructor(public pedido: any, public sistema: string, dto: any) {
    super();
    this.horario = new Date();
    if(dto)
      this.payload = (typeof dto === 'string' ) ? dto : JSON.stringify(dto);

    if(pedido) this.empresa = pedido.empresa;

  }

  async saveRetornoHttp(retorno: any, erro: string = null){
    let response: any = retorno.response ? retorno.response : retorno;

    try{
      if(response.status){
        this.httpstatus = response.status;
        this.retorno = typeof response.data === 'string' ? response.data : JSON.stringify(response.data) ;
      } else {
        this.httpstatus = 0;
        this.retorno = typeof response === 'string' ? response : JSON.stringify(response)
      }
    } catch (e){
      console.error(e)
      this.httpstatus = -1;
      this.retorno = 'Erro tentar pegar retorno da request'
    }

    if(response.request && response.request._header)
      this.header = response.request._header

    this.erro = erro;

    await this.salve(true)
  }

  setDados(){
    try{
      if( typeof this.payload === 'string')
        this.payload = JSON.parse(this.payload)

      if( typeof this.retorno === 'string')
        this.retorno = JSON.parse(this.retorno)
    } catch (e){
      //console.log(e)
      this.payload = { data: this.payload} as any;
    }
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeRequestParceiro();
  }
}
