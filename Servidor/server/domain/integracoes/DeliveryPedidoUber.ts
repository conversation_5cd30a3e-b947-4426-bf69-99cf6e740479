import {DeliveryPedido} from "./DeliveryPedido";
import {EnumStatusEntregaUber} from "../../lib/integracao/opendelivery/EnumStatusEntregaUber";

const cores: any = {
  'white': 'branco',
  'black': 'preto',
  'red': 'vermelho',
  'gray': 'cinza',
  'green': 'verde',
  'brown': 'marrom',
  'blue': 'azul',
  'yellow': 'amarelo',
  'orange': 'laranja',
  'pink': 'rosa',
  'purple': 'roxo',
  'beige': 'bege',
  'gold': 'dourado',
  'silver': 'prateado',
  'navy': 'azul-marinho',
  'teal': 'azul-petróleo',
  'olive': 'verde-oliva',
  'coral': 'coral',
  'mint': 'menta',
  'peach': 'pêssego',
  'lavender': 'lavanda',
};
export class DeliveryPedidoUber extends DeliveryPedido{
  constructor(pedido: any, operador: any,  dados: any) {
    super('uber', pedido, operador, dados)
    if(dados)
      this.status =  dados.status;
  }

  setDados(dados: any): void {
    if(!this.dados) this.dados = { orderId: dados.orderId};
    else {
      if(typeof this.dados === 'string')
        this.dados = JSON.parse(this.dados)
    }
    this.deliveryId = dados.id;
    this.dados.courier = dados.courier;
    this.dados.courier_imminent = dados.courier_imminent;
    this.dados.dropoff_eta = dados.dropoff_eta;
    this.dados.dropoff_ready = dados.dropoff_ready;
    this.dados.external_id = dados.external_id;
    this.dados.fee = dados.fee;
    this.dados.tip = dados.tip;
    this.dados.pickup_eta = dados.pickup_eta;
    this.dados.pickup_ready = dados.pickup_ready;
    this.dados.tracking_url = dados.tracking_url;
    this.dados.status = dados.status;
    this.dados.undeliverable_action = dados.undeliverable_action;
    this.dados.undeliverable_reason = dados.undeliverable_reason;
    this.dados.cancelation_reason = dados.cancelation_reason;
    this.dados.created = dados.created;
    this.dados.updated = dados.updated;
    this.dados.uuid = dados.uuid;
    this.dados.return = dados.return;

    if(dados.dropoff)
      this.dados.dropoff_verification = dados.dropoff.verification_requirements
  }

  setFoiAceita() {
    this.foiAceita = this.status !== EnumStatusEntregaUber.Pendente &&   this.status !== EnumStatusEntregaUber.Rejeitado &&
      this.status !== EnumStatusEntregaUber.Cancelado
  }

  podeCancelar(){
    return   this.status  === EnumStatusEntregaUber.Pendente   ||  this.status  === EnumStatusEntregaUber.AcaminhoDaLoja
  }

  foiCancelado(){
    return   this.status  === EnumStatusEntregaUber.Cancelado
  }

  listaStatusAntesColeta(){
    return  [EnumStatusEntregaUber.Pendente, EnumStatusEntregaUber.AcaminhoDaLoja]
  }

  estaPendente(){
    return   this.status  === EnumStatusEntregaUber.Pendente
  }

  foiRejeitado(){
    return   this.status  === EnumStatusEntregaUber.Rejeitado
  }

  foiFinalizado(){
    return   this.status  === EnumStatusEntregaUber.PedidoEntregue
  }

  podeSolicitarNovamente(): boolean {
     return  this.foiCanceladoOuRejeitado();
  }


  foiCanceladoOuRejeitado(){
    return this.foiCancelado() ||  this.foiRejeitado();
  }

  encerrouPrazo(): boolean {
    return false;
  }

  getEntregador(): any {
    let entregador: any = this.getDados().courier;

    if(entregador)  entregador.phone = entregador.phone_number

    return entregador;
  }

  getMotivoCancelamento(){
    const tabelaErros: any = {
      CUSTOMER_UNAVAILABLE: "CLIENTE INDISPONÍVEL",
      NO_SECURE_LOCATION_TO_DROPOFF: "SEM LOCAL SEGURO PARA ENTREGA"
    }

    let dados: any = this.getDados();

    let motivoCancelamento: any = '';
    let razaoNaoEntregar: string = '';

    if(dados ){
      if(dados.undeliverable_reason){
        razaoNaoEntregar =  dados.undeliverable_reason;

        if(tabelaErros[razaoNaoEntregar])
          razaoNaoEntregar =  tabelaErros[razaoNaoEntregar]

        razaoNaoEntregar = String(`Pedido não foi entregue: ` + razaoNaoEntregar)
      }

      if(dados.cancelation_reason){
        motivoCancelamento = dados.cancelation_reason;
        let quemCancelou = [], outros = []
        if(typeof motivoCancelamento === 'object') {
          let keys = Object.keys(motivoCancelamento);

          for(let key of keys) {
            if(key === 'primary_reason' || key === 'secondary_reason') {
              quemCancelou.push(motivoCancelamento[key])
            } else {
              outros.push(String(`${key} - ${motivoCancelamento[key]}`) )
            }
          }

          if(quemCancelou.length)
            motivoCancelamento = String('Cancelado por: ' + quemCancelou.join(', '))

        }
      }
    }

    return (razaoNaoEntregar  +  " " + motivoCancelamento).trim() ;
  }


  getUrlRastreamento(): string {
    return this.getDados().tracking_url
  }

  getPincodeEntrega(): string {
    let  dropoff_verification = this.getDados().dropoff_verification;

    return dropoff_verification && dropoff_verification.pincode  ? dropoff_verification.pincode.value : null
  }

  getIdChamado(){
    return this.getUberId()
  }


  getUberId(){
    let guid: string = this.getDados().uuid;
    return  guid ? guid.substring(guid.length - 5) : '';
  }

  getVeiculo(): any {
    let entregador: any = this. getEntregador();

    let veiculo: any = {};

    if(entregador && entregador.vehicle_type){
      veiculo = {
        tipo: entregador.vehicle_type,
        marca: entregador.vehicle_make,
        modelo: entregador.vehicle_model,
        cor: cores[entregador.vehicle_color] || entregador.vehicle_color,
        carro: entregador.vehicle_type === 'car'
      }
      veiculo.descricao = String(`${veiculo.marca} ${veiculo.modelo} - ${veiculo.cor}`)
    }

    return veiculo;
  }

  obtenhaListaStatus(): any {
    return EnumStatusEntregaUber;
  }
}
