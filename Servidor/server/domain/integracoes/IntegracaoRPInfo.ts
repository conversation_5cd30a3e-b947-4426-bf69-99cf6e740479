import {IServiceIntegracaoExternaERP} from "./IServiceIntegracaoExternaERP";
import {RPInfoService} from "../../service/RPInfoService";
import {TokenComVencimento} from "./TokenComVencimento";
import {IntegracaoDeliveryComJson} from "./IntegracaoDeliveryComJson";

export class IntegracaoRPInfo extends IntegracaoDeliveryComJson{
  validadeToken: Date;
  loja: number;
  configuracoesEspecificas: {
    especie: number;
    codigoDocumento: string;
    cnpj: string;
    link: string;
    usuario: string;
    senha: string;
    empresa: { id: number, cnpj: string };
  }

  constructor(dados: any) {
    if(dados) {
      super(dados.id, dados.empresa, dados.token, 'RPInfo');
      this.validadeToken = dados.validadeToken;

     if(dados.loja)
      this.loja = +dados.loja;

      this.configuracoesEspecificas = dados.configuracoesEspecificas
    }
    else{
      super(null, null, null, 'RPInfo')
    }

    if(!this.configuracoesEspecificas)
      this.configuracoesEspecificas = {
        especie: null,
        codigoDocumento: null,
        cnpj: null,
        link: null,
        usuario: null,
        senha: null,
        empresa: null
      };

    this.configuracoesEspecificas.usuario = '100014';
    this.configuracoesEspecificas.senha = '102030';

  }

  static crieIntegracao(empresa: any, usuario: string, senha: string, loja: number, especie: number,
                        codigoDocumento: string, cnpj: string, link: string): Promise<IntegracaoRPInfo> {
    return new Promise((resolve, reject) => {
      RPInfoService.obtenhaToken(usuario, senha, link).then((token: TokenComVencimento) => {
        if(!token) resolve(null)

        resolve(new IntegracaoRPInfo({empresa: {id: empresa.id}, token: token.token,
          validadeToken: token.dataExpiracao, loja: loja, configuracoesEspecificas: {especie: especie,
          codigoDocumento: codigoDocumento, cnpj: cnpj, link: link, empresa: {id: empresa.id,
              cnpj: empresa.cnpj, dominio: empresa.dominio}}}))
      }).catch((reason: any) => {
        reject(reason)
      })
    })
  }

  obtenhaCredencial(): any {
    return {
      token: this.token,
      validadeToken: this.validadeToken,
      loja: this.loja,
      especie: this.configuracoesEspecificas.especie,
      codigoDocumento: this.configuracoesEspecificas.codigoDocumento,
      cnpj: this.configuracoesEspecificas.cnpj,
      link: this.configuracoesEspecificas.link,
      usuario: this.configuracoesEspecificas.usuario,
      senha: this.configuracoesEspecificas.senha,
      id: this.id,
      empresa: this.configuracoesEspecificas.empresa
    }
  }

  obtenhaService(): IServiceIntegracaoExternaERP {
    return new RPInfoService(this.obtenhaCredencial());
  }

  inicializeToken(): Promise<any> {
    return new Promise((resolve) => {
      IntegracaoRPInfo.crieIntegracao(this.configuracoesEspecificas.empresa,
        this.configuracoesEspecificas.usuario, this.configuracoesEspecificas.senha, this.loja, this.configuracoesEspecificas.especie,
        this.configuracoesEspecificas.codigoDocumento, this.configuracoesEspecificas.empresa.cnpj,
        this.configuracoesEspecificas.link ).then((integracao) => {
        this.token = integracao.token
        this.validadeToken = integracao.validadeToken
        this.configuracoesEspecificas.cnpj = this.configuracoesEspecificas.empresa.cnpj
        resolve(null);
      })
    })
  }

}
