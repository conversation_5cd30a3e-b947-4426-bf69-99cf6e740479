import {NotificacaoMesa} from "./NotificacaoMesa";

export class NotificacaoMesaTransferida extends NotificacaoMesa{
  constructor(public empresa: any = null,  numero: string = null,   public operacao: string = null,
              dados: any = null) {
    super(empresa, numero, operacao, dados);
    this.comando  = 'tranferiu';
  }

  async valide(contexto: any = null, catalogo: any = null): Promise<any> {
    if(!this.operacao)
      return Promise.resolve('tipo de operacao não informado')

    if(!this.numero)
      return  Promise.resolve(`numero da ${this.operacao} não informado`);

    let dados =  this.getDados();

    if(!dados.rede || !dados.loja)
      return  Promise.resolve('Rede ou Loja não informado')

    if(!dados.de)
      return  Promise.resolve('Dados transferencia invalido: de')

    if(!dados.de.numero)
      return  Promise.resolve('Dados transferência invalido: de.numero')

    if(!dados.para)
      return  Promise.resolve('Dados transferência invalido: para')

    if(!dados.para.numero)
      return  Promise.resolve('Dados transferência invalido: para.numero')

    if(typeof this.dados !== 'string')
      this.dados = JSON.stringify(this.dados)

    return Promise.resolve();

  }

  getMesaDestino() {
    let dados: any = this.getDados();

    return dados && dados.para ? dados.para.numero : null;
  }
}
