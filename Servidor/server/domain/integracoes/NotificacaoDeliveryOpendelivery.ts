import {NotificacaoDelivery} from "./NotificacaoDelivery";
import {EnumStatusEntregaLogistic} from "../../lib/integracao/opendelivery/EnumStatusEntregaLogistic";

export class NotificacaoDeliveryOpendelivery extends NotificacaoDelivery{
  constructor(pedido: any, dados: any) {
    super('opendelivery', pedido, dados);
    this.deliveryId  = dados ? dados.deliveryId : null;
    this.tipo = dados ? dados.event.type : null;
  }

  setDados(dados: any) {
    if(dados){
      this.dados = {
        orderId: dados.orderId,
        vehicle: dados.vehicle,
        event: dados.event,
        deliveryPrice: dados.deliveryPrice,
        eta: dados.eta,
        deliveryPerson: dados.deliveryPerson,
        geoLocalization: dados.geoLocalization,
        problem: dados.problem,
        externalTrackingURL: dados.externalTrackingURL
      }
    }
  }

  obtenhaListaStatus(): any {
    return EnumStatusEntregaLogistic
  }

}
