import {NotificacaoSistemaExterno} from "./NotificacaoSistemaExterno";
import {MapeadorDeNotificacaoIfood} from "../../mapeadores/MapeadorDeNotificacaoIfood";
import {EventsIfoodEnum} from "../../service/integracoes/EventsIfoodEnum";
import {MapeadorDePedidoIntegrado} from "../../mapeadores/MapeadorDePedidoIntegrado";
import {PedidoFalhaIntegracao} from "./PedidoFalhaIntegracao";

export class NotificacaoIfood extends NotificacaoSistemaExterno{
  public code: string;
  public fullCode: string;
  public orderId: string;
  public horarioCriacao: Date;
  public merchantId: string;
  constructor(event: any) {
    super(null);
    if(event){
      this.id = event.id;
      this.code  = event.code;
      this.fullCode = event.fullCode;
      this.orderId = event.orderId;
      this.horarioCriacao = new Date(event.createdAt);
      this.merchantId = event.merchantId;

      if(event.metadata)
        this.dados = JSON.stringify(event.metadata)
    } else {
      this.horarioCriacao = new Date();
    }
  }

  async obtenhaPedido(){
    let pedido = await  new MapeadorDePedidoIntegrado( ).selecioneSync({ idExterno: this.orderId})

    return pedido;
  }

  ehNovoDePedido(){
    return this.code === EventsIfoodEnum.PedidoNovo
  }

  pedidoConfirmado(){
    return this.code === EventsIfoodEnum.PedidoConfirmado;
  }

  cancelamentoAceito(){
    return this.code === EventsIfoodEnum.CancelamentoAceito || this.code === EventsIfoodEnum.RequestClienteCancelamentoAceita;
  }

  cancelamentoNegado(){
    return this.code === EventsIfoodEnum.CancelamentoNegado || this.code === EventsIfoodEnum.RequestClienteCancelamentoNegada;
  }


  pedidoCancelado(){
    return this.code === EventsIfoodEnum.PedidoCancelado;
  }

  disputaComCliente(){
    return this.disputaAberta() || this.disputaRespondida()
  }

  disputaAberta(){
    return this.code === EventsIfoodEnum.DisputaAberta  || this.disputaAbertaLegado()
  }

  disputaAbertaLegado(){//dizem sera descontinuado
    return  this.code === EventsIfoodEnum.RequestClienteCancelamento;
  }

  disputaRespondida(){
    return this.code === EventsIfoodEnum.DisputaAcordo
  }

 async registreFalhaImportarNovoPedido(error: any, order: any){
    console.log(error)
    await this.registreErro(error.message || error)

    let pedidoFalha = new PedidoFalhaIntegracao(order, 'ifood');

    pedidoFalha.setFalha(error);

    await pedidoFalha.salve(true);
  }

  mapeador(): any {
    return new MapeadorDeNotificacaoIfood();
  }

  repostaSolicitacaoCancelamento() {
     return this.cancelamentoAceito() || this.cancelamentoNegado()
  }


}
