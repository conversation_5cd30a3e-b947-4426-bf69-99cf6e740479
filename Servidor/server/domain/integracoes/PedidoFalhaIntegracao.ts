import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorPedidoFalhaIntegracao} from "../../mapeadores/MapeadorPedidoFalhaIntegracao";

export class PedidoFalhaIntegracao extends ObjetoPersistente{
  public horario: Date;
  public erro: string;
  constructor(public payload: any, public sistema: string) {
    super();
    this.horario =  new Date();
    this.payload = payload ? JSON.stringify(payload) : "{}"
  }


  setFalha(error: any, operacao = 'Importar pedido'){
     let erro: any  = {
       operacao:  'Falha ao ' + operacao
     }

    if(typeof error === 'string')
      erro.descricao = error;

    if(error.message)
      erro.descricao = error.message;

    if(typeof  error === 'object')
       Object.assign(erro, error)

    if(Array.isArray(error))
      erro.erros = error

    this.erro = JSON.stringify(erro)

  }

  toDTO(){
    this.payload = JSON.parse(this.payload)
    this.erro = JSON.parse(this.erro)

    return this;
  }

  mapeador(): any {
    return new MapeadorPedidoFalhaIntegracao();
  }
}
