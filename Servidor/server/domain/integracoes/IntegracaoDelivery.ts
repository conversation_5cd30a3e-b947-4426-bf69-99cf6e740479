import {FormaDePagamentoIntegrada} from "./FormaDePagamentoIntegrada";
import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";
import {IServiceIntegracaoExternaERP} from "./IServiceIntegracaoExternaERP";
import * as moment from "moment";
import {Produto} from "../Produto";
import {EnumDisponibilidadeProduto} from "../../lib/emun/EnumDisponibilidadeProduto";
import {MapeadorDeIntegracaoDelivery} from "../../mapeadores/MapeadorDeIntegracaoDelivery";
import {MapeadorDeProduto} from "../../mapeadores/MapeadorDeProduto";
import {MapeadorDeOpcaoDeAdicionalDeProduto} from "../../mapeadores/MapeadorDeOpcaoDeAdicionalDeProduto";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {RegistroDeOperacaoService} from "../../service/RegistroDeOperacaoService";
import {Ambiente} from "../../service/Ambiente";
import {Pedido} from "../delivery/Pedido";
import {ProdutoTamanho} from "../templates/ProdutoTamanho";
import {ProdutoNaEmpresa} from "../catalogo/ProdutoNaEmpresa";
import {OpcaoNaEmpresa} from "../catalogo/OpcaoNaEmpresa";
import {MapeadorDeProdutoNaEmpresa} from "../../mapeadores/MapeadorDeProdutoNaEmpresa";
import {MapeadorDeOpcaoNaEmpresa} from "../../mapeadores/MapeadorDeOpcaoNaEmpresa";
import {MapeadorDeProdutoTemplate} from "../../mapeadores/MapeadorDeProdutoTemplate";

export abstract class IntegracaoDelivery  {
  data: Date;
  ativa: boolean;
  formasDePagamento: Array<FormaDePagamentoIntegrada> = [];
  tipo: string;
  ultimaSincronizacaoDisponivel: Date;
  configuracoesEspecificas: any;
  ultimaSincronizacaoProdutos: Date;
  ultimaSincronizacaoPrecos: Date;
  ultimaSincronizacaoEstoque: Date;
  constructor(public id: any, public empresa: any, public token: string, public sistema: string) {
    this.data = new Date();
    this.ativa = true;
  }

  notificarNovo(pedido: Pedido): boolean { // padrao é essse, quando muda para em prepração e nao tem mesa
    if(pedido.comanda && pedido.comanda.id) return false;

    return pedido.ehDelivery() && Number(pedido.status) === EnumStatusPedido.EmPreparacao;
  }

  abstract obtenhaCredencial(): any;

  abstract obtenhaService( ): IServiceIntegracaoExternaERP;

  async sincronizeProdutosEstoque(produtosAhVenda: Array<Produto>, empresa: any): Promise<any>{
   return new Promise<void>(async (resolve) => {
     let catalogo: any  = empresa.catalogo

     let disponibilidadePorEmpresa = catalogo.disponibilidadePorEmpresa
     let produtosErpIndisponiveis;

     let service = this.obtenhaService();

     let ultimasAtualizacoes: any =  await service.veririqueUpdates({}).catch( (err: any) => {
       console.log(err)
     })

     let ultimaAtualizacao = ultimasAtualizacoes ? ultimasAtualizacoes.dh_faltante : null;

     if(!ultimaAtualizacao || !this.estoqueEstaSincronizado(ultimaAtualizacao) ){
       produtosErpIndisponiveis  = await service.listeProdutosIndisponiveis( ).catch( (err: any) => {
         console.log(err)
       })

       if(produtosErpIndisponiveis && produtosErpIndisponiveis.length){
         let produtosAtualizar: any = [], opcoesAtualizar: any = [], tamanhosAtualizar: ProdutoTamanho[] = [],
           templatesOpcoesAtualizar: any = [];

         let templatesJaSelecionados: any = {}
         let opcoesCompartilhadas: any = {}
         produtosErpIndisponiveis.forEach( (produtoIndisponivel: any) => {
           produtosAhVenda.forEach( (produtoAVenda: Produto) => {
             if( produtoAVenda.codigoPdv && produtoAVenda.codigoPdv.toString() === produtoIndisponivel.id.toString()){
               if(!disponibilidadePorEmpresa) {
                 produtoAVenda.temEstoque = false;
                 produtoAVenda.disponibilidade = EnumDisponibilidadeProduto.NaoDisponivel
               } else{
                 if(!produtoAVenda.produtoNaEmpresa || !produtoAVenda.produtoNaEmpresa.id)
                   produtoAVenda.produtoNaEmpresa = new ProdutoNaEmpresa(produtoAVenda, empresa,  produtoAVenda.preco)

                 console.log('---Sincronizando o produto por empresa---')
                 produtoAVenda.produtoNaEmpresa.disponibilidade = EnumDisponibilidadeProduto.NaoDisponivel;
                 produtoAVenda.produtoNaEmpresa.empresa = { id: empresa.id}
                 produtoAVenda.produtoNaEmpresa.produto = produtoAVenda
               }

               produtosAtualizar.push(produtoAVenda)
             }

             if(produtoAVenda.tamanhos) {
               produtoAVenda.tamanhos.forEach((tamanho) => {
                 if(tamanho.codigoPdv && tamanho.codigoPdv === produtoIndisponivel.id.toString()
                 && tamanho.disponivel) {
                   tamanho.disponivel = false;
                   tamanhosAtualizar.push(tamanho)
                 }
               })
             }
             if( produtoAVenda.camposAdicionais ) {
               produtoAVenda.camposAdicionais.forEach((campoAdiconal) => {
                 if( campoAdiconal.opcoesDisponiveis ) {
                   campoAdiconal.opcoesDisponiveis.forEach((opcaoDisponivel) => {
                     if (opcaoDisponivel.codigoPdv && opcaoDisponivel.codigoPdv.toString() === produtoIndisponivel.id.toString()) {
                       if(!disponibilidadePorEmpresa) {
                         if(opcaoDisponivel.template && opcaoDisponivel.template.id) {
                           if(!templatesJaSelecionados[opcaoDisponivel.template.id]) {
                             templatesJaSelecionados[opcaoDisponivel.template.id] = opcaoDisponivel.template
                             opcaoDisponivel.template.disponivel = false
                             templatesOpcoesAtualizar.push(opcaoDisponivel.template)
                           }

                         } else if(opcaoDisponivel.disponivel) {
                           opcaoDisponivel.disponivel = false;
                           opcoesAtualizar.push(opcaoDisponivel)
                         }
                       }

                       else {
                         if(!opcaoDisponivel.opcaoNaEmpresa || !opcaoDisponivel.opcaoNaEmpresa.id) {
                           opcaoDisponivel.opcaoNaEmpresa = new OpcaoNaEmpresa(opcaoDisponivel, empresa,
                             opcaoDisponivel.valor, opcaoDisponivel.disponivel)
                         }

                         if(opcaoDisponivel.opcaoNaEmpresa.disponivel) {
                           opcaoDisponivel.opcaoNaEmpresa.disponivel = false;
                           opcaoDisponivel.opcaoNaEmpresa.empresa = { id: empresa.id}
                           opcaoDisponivel.opcaoNaEmpresa.opcao = opcaoDisponivel
                           opcaoDisponivel.opcaoNaEmpresa.novo = true;

                           if(!opcoesCompartilhadas[opcaoDisponivel.id]) {
                              opcoesAtualizar.push(opcaoDisponivel)
                             if(campoAdiconal.compartilhado)
                                opcoesCompartilhadas[opcaoDisponivel.id] = opcaoDisponivel
                           }
                           else
                             opcaoDisponivel.opcaoNaEmpresa = opcoesCompartilhadas[opcaoDisponivel.id].opcaoNaEmpresa
                         }
                       }
                     }
                   })
                 }
               })
             }
           })
         })

         if(produtosAtualizar.length || opcoesAtualizar.length || tamanhosAtualizar.length ||
           templatesOpcoesAtualizar.length){
           let mapeadorProduto = new MapeadorDeProduto(empresa.catalogo)
           mapeadorProduto.transacao( async (conexao: any, commit: Function) => {
             await mapeadorProduto.atualizeDisponibilidadeTamanho(tamanhosAtualizar);

             let registroDeOperacao = new RegistroDeOperacaoService(null, empresa.obtenhaLinkLoja(Ambiente.Instance.producao) )

             if(!disponibilidadePorEmpresa) {
               await mapeadorProduto.atualizeDisponibilidades(produtosAtualizar);

               await new MapeadorDeOpcaoDeAdicionalDeProduto().atualizeDisponibilidades(opcoesAtualizar);
               await new MapeadorDeProdutoTemplate().atualizeDisponibilidadesOpcoes(templatesOpcoesAtualizar)

               for(let i = 0; i < produtosAtualizar.length; i++)
                 await registroDeOperacao.alterouDisponibilidadeDoProduto(produtosAtualizar[i])


               for(let i = 0; i < opcoesAtualizar.length; i++)
                 await registroDeOperacao.alterouDisponibilidadeOpcaoAdicional(opcoesAtualizar[i])

             } else {
               let mapeadorProdutoNaEmpresa = new MapeadorDeProdutoNaEmpresa()
               await mapeadorProdutoNaEmpresa.atualizeOuInsiraDisponibilidadesNaEmpresa(produtosAtualizar)
               let mapeadorOpcaoNaEmpresa = new MapeadorDeOpcaoNaEmpresa()
               await mapeadorOpcaoNaEmpresa.atualizeOuInsiraDisponibilidadesNaEmpresa(opcoesAtualizar)

               for(let i = 0; i < produtosAtualizar.length; i++)
                 await registroDeOperacao.alterouDisponibilidadeDoProdutoDaEmpresa(produtosAtualizar[i])


               for(let i = 0; i < opcoesAtualizar.length; i++)
                 await registroDeOperacao.alterouDisponibilidadeOpcaoAdicionalDaEmpresa(opcoesAtualizar[i])

             }

             this.ultimaSincronizacaoDisponivel = moment(ultimaAtualizacao).toDate();
             await new MapeadorDeIntegracaoDelivery().atualizeUltimaSincronizacao(this);

             await new MapeadorDeEmpresa().removaDasCaches(empresa);
             await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

             commit( () => {
                 resolve();
             })
           })
         } else {
           resolve();
         }
       } else {
         resolve();
       }
     } else {
       resolve();
     }

   })

  }

  estoqueEstaSincronizado(ultimaSincronizacao: any){
    if(!this.ultimaSincronizacaoDisponivel) return false;

    return moment(ultimaSincronizacao).diff(this.ultimaSincronizacaoDisponivel, 's') === 0
  }

  inicializeToken(): Promise<any> {
    return Promise.resolve(this.token);
  }

  async atualizeUltimaImportacaoProdutos(){
   this.ultimaSincronizacaoProdutos = new Date();
   await new MapeadorDeIntegracaoDelivery().atualizeUltimaSincronizacaoProdutos(this)
  }

  async atualizeUltimaSincronizacaoPrecos(){
   this.ultimaSincronizacaoPrecos = new Date();
   await new MapeadorDeIntegracaoDelivery().atualizeUltimaSincronizacaoPrecos(this)
  }

  async atualizeUltimaSincronizacaoEstoque(){
    this.ultimaSincronizacaoEstoque = new Date();
    await new MapeadorDeIntegracaoDelivery().atualizeUltimaSincronizacaoEstoque(this)

  }

  tempoEstimadoEntregaObrigatorio(){
    return false;
  }

  cepObrigatorio(){
    return false;
  }

  ehGCom(){
    return this.sistema === 'gcom'
  }

  integrarComComandas(){
    return false;
  }

  enviarMesasFecharConanda(){
    return false;
  }
}
