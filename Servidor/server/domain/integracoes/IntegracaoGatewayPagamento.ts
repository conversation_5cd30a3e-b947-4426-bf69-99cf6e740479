import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDeIntegracaoGatewayPagamento} from "../../mapeadores/MapeadorDeIntegracaoGatewayPagamento";
import {FormaDePagamento} from "../delivery/FormaDePagamento";
import {ConfigMeioDePagamento} from "../delivery/ConfigMeioDePagamento";
import {EnumMeioDePagamento} from "../delivery/EnumMeioDePagamento";
import {MapeadorDeConfigMeioDePagamento} from "../../mapeadores/MapeadorDeConfigMeioDePagamento";
import {MapeadorDeFormaDePagamento} from "../../mapeadores/MapeadorDeFormaDePagamento";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import * as moment from "moment";

export class IntegracaoGatewayPagamento extends ObjetoPersistente{
 public instalacaoId: string;
 public lojaId: string;
 public clienteId: string;
 public refreshToken: string;
 public dataExpiracao: Date;
 constructor(public gateway: string, public privateKey: string, public publicKey: string,
             public sandbox: boolean) {
   super();
 }

  ativeNaEmpresa(empresa: any){
    return new Promise( async (resolve, reject) => {
      let formasPagamento = ['Pagamento Online Cartão'];

      let formaDePagamentoPix: any, formaDePagamentoCartao: any, meioPagamento: any;

      //tirando pix, deixar somente no tuna
      if(this.pagarme()){
        meioPagamento = EnumMeioDePagamento.PagarmeHub;
        formasPagamento.push( 'Pagamento Online Pix');
      //  formaDePagamentoPix = empresa.formasDePagamento.find((item: any) => item.hubPagarme() && item.pix);
         formaDePagamentoCartao = empresa.formasDePagamento.find((item: any) => item.hubPagarme() && !item.pix);
      }

      if(this.pagbank()){
        meioPagamento = EnumMeioDePagamento.PagBankConnect;
        formaDePagamentoCartao = empresa.formasDePagamento.find((item: any) => item.pagseguroConnect() && !item.pix);
      }

      if(!meioPagamento) return reject('Gateway invalido: ' + this.gateway)


      this. mapeador().transacao(async (conexao: any, commit: any) => {
        await this.insiraGraph();
        this.mapeador().insiraNaEmpresa(empresa, this)

        for(let i = 0; i < formasPagamento.length; i++){
          let nome = formasPagamento[i];
          const ehPix = nome.indexOf('Pix') >= 0;
          let formaDePagamento: any;

          if(ehPix && formaDePagamentoPix)
            formaDePagamento = formaDePagamentoPix

          if(!ehPix && formaDePagamentoCartao)
            formaDePagamento = formaDePagamentoCartao


          if(!formaDePagamento){
            formaDePagamento = FormaDePagamento.nova(nome, nome, true, empresa);

            formaDePagamento.online = true;
            formaDePagamento.pix = ehPix

            let config = new ConfigMeioDePagamento();

            config.meioDePagamento =  meioPagamento

            formaDePagamento.configMeioDePagamento = config

            await new MapeadorDeConfigMeioDePagamento().insiraConfigMeioDePagamento(formaDePagamento.configMeioDePagamento);
            await  new MapeadorDeFormaDePagamento().insiraSync(formaDePagamento);
          } else {
            formaDePagamento.exibirCardapio = true;
            await new MapeadorDeFormaDePagamento().atualizeAtiva(formaDePagamento)
          }
        }

        await new MapeadorDeEmpresa().removaDasCaches(empresa);
        commit(() => {   resolve(''); })
      })
    });
  }


  pagarme(){
    return this.gateway === EnumMeioDePagamento.Pagarme
  }

  pagbank(){
    return this.gateway === EnumMeioDePagamento.Pagseguro
  }



  async remova(empresa: any = null){
    await super.remova();

    let formasDePagamentoOnline: any =  await new MapeadorDeFormaDePagamento().listeAsync({online: true});

    for(let j = 0; j < formasDePagamentoOnline.length; j++){
      let formaPagamento: any = formasDePagamentoOnline[j];

      if(this.pagarme()){
        if(formaPagamento.hubPagarme()){
          formaPagamento.exibirCardapio = false;
          await new MapeadorDeFormaDePagamento().atualizeAtiva(formaPagamento);
        }
      }

      if(this.pagbank()){
        if(formaPagamento.pagseguroConnect()){
          formaPagamento.exibirCardapio = false;
          await new MapeadorDeFormaDePagamento().atualizeAtiva(formaPagamento);
        }
      }
    }
    if(empresa)
      await new MapeadorDeEmpresa().removaDasCaches(empresa);
  }


  mapeador(): any {
   return new MapeadorDeIntegracaoGatewayPagamento();
 }

  setToken(access_token: string, refresh_token: string, expires_in: string, account_id: string) {
   this.privateKey = access_token;
   this.refreshToken = refresh_token;
   this.lojaId = account_id;

    if(expires_in)
      this.dataExpiracao = moment().add(expires_in, 's').startOf('d').toDate() //expires_in

  }

  tokenExpirado(){
    if(!this.dataExpiracao) return false;

    return !moment().isBefore(moment(this.dataExpiracao))
  }
}
