import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDeIntegracaoFidelidade} from "../../mapeadores/MapeadorDeIntegracaoFidelidade";
import {GcomERPService} from "../../service/integracoes/GcomERPService";

export class IntegracaoFidelidade  extends ObjetoPersistente{
  dataAtivacao: Date;
  operador: any;
  constructor( public empresa: any = null, public sistema: string = null, public loja: number = null) {
    super()
    this.dataAtivacao = new Date();
  }

  mapeador(): any {
    return new MapeadorDeIntegracaoFidelidade();
  }

  ehGcom(){
    return this.sistema === 'gcom'
  }

  setDadosCliente(resposta: any, dadosCashback: any){
    let completouCadastro =  dadosCashback.cadastrado === 'S';

    resposta.id_cliente = dadosCashback.id_cliente;
    resposta.id_empresa = dadosCashback.id_empresa;
    resposta.id_marca = dadosCashback.id_marca;
    resposta.id_programa_fidelidade = dadosCashback.id_programa_fidelidade;
    resposta.id_programa_fidelidade_versao = dadosCashback.id_programa_fidelidade_versao;
    resposta.cadastrado = dadosCashback.cadastrado;
    resposta.categoria = dadosCashback.categoria;
    resposta.aceitarFidelidade  = dadosCashback.id_cliente === 0 ||  !completouCadastro;
  }

  async setDadosRetornoCadastro(telefone: string, resposta: any, empresa: any){
    let dadosCashback: any =
      await new GcomERPService(null).obtenhaSaldoCashback(empresa, telefone).catch((erro) => {
        console.error(erro)
      });

    if(dadosCashback && dadosCashback.id_cliente != null){
      let completouCadastro =  dadosCashback.cadastrado === 'S';

      resposta.fidelidadeExterna = {
        aceitarFidelidade:  dadosCashback.id_cliente === 0 ||  !completouCadastro,
      }

      if(dadosCashback.id_cliente > 0)
        resposta.fidelidadeExterna.id_cliente = dadosCashback.id_cliente

    }
  }

  setLogoLinkExterno(empresa: any){
     (this as any).logo =   this.obtenhaLogo(empresa);
     (this as any).linkRegras = this.obtenhaLinkRegras(empresa);
  }

  obtenhaLogo(empresa: any){
    return empresa.ehUmaCib() ? 'https://www.chinainbox.com.br/assets/marca_fidelidade.svg' :
      (empresa.ehUmaGedai() ? 'https://www.gendai.com.br/file/general/gendai_vip_logo.png' : '')
  }

  obtenhaLinkRegras(empresa: any){
    return empresa.ehUmaCib() ? 'https://www.chinainbox.com.br/fidelidade' :
      (empresa.ehUmaGedai() ? 'https://www.gendai.com.br/fidelidade' : '')


  }
}
