import {IntegracaoDelivery} from "./IntegracaoDelivery";
import {IServiceIntegracaoExternaERP} from "./IServiceIntegracaoExternaERP";
import {GcomERPService} from "../../service/integracoes/GcomERPService";
import {ClienteApi} from "../api/ClienteApi";
import {MapeadorDeClienteApi} from "../../mapeadores/MapeadorDeClienteApi";
import {BearerToken} from "../api/BearerToken";
import {MapeadorDeBearerToken} from "../../mapeadores/MapeadorDeBearerToken";

export class IntegracaoGcomERP extends IntegracaoDelivery{
  unidadeChina: string;
  unidadeGendai: string;
  naoSincronizarGlobal: boolean;
  naoSincronizarDisponiveis: boolean;
  constructor(dados: any){
    if(dados){
      super(dados.id, dados.empresa, dados.token, 'gcom');
      this.unidadeChina = dados.unidadeChina;
      this.unidadeGendai = dados.unidadeGendai;
    } else {
      super( null, null, null, 'gcom');
    }
  }

  notificarNovo(pedido: any): boolean {
    return  false; //Gcom não notifican ovos, eles fazem pooling
  }


  obtenhaCredencial(): any{
    let credencial: any =  {
      token: this.token
    }

    return credencial;
  }

  inicializeToken(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      let cliente = new ClienteApi("Gcom api",  'gcomapi',  null, '1', true);

      cliente.gereIdESegredo();

      await new MapeadorDeClienteApi().insiraGraph(cliente);

      let bearerToken = new BearerToken(cliente);

      await new MapeadorDeBearerToken().insiraGraph(bearerToken);

      this.token = bearerToken.getToken();

      resolve(  this.token );
    });
  }

  obtenhaService(): IServiceIntegracaoExternaERP {
    return new GcomERPService(this.obtenhaCredencial())  ;
  }


  tempoEstimadoEntregaObrigatorio(){
    return true;
  }

  cepObrigatorio(){
    return true;
  }



}
