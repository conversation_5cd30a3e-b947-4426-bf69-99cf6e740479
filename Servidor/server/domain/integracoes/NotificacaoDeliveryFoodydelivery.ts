import {NotificacaoDelivery} from "./NotificacaoDelivery";
import {EnumStatusEntregaUber} from "../../lib/integracao/opendelivery/EnumStatusEntregaUber";

export class NotificacaoDeliveryFoodydelivery extends NotificacaoDelivery{
  constructor(pedido: any, payload: any) {
    super('foodydelivery', pedido, payload);
    this.deliveryId  = payload ? payload.uid : null;
    this.tipo = payload ? payload.status : null;


  }

  setDados(dados: any) {
    if(dados){
      delete dados.deliveryPoint;
      delete dados.collectionPoint;
      delete dados.customer;
      delete dados.orderDetails;
    }
    this.dados = dados;
  }

  obtenhaListaStatus(): any {
    return EnumStatusEntregaUber;
  }

}
