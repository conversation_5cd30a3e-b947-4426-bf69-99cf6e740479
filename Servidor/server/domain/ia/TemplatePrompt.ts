export class TemplatePrompt {
  id: number;
  nome: string;
  descricao: string;
  template: string;

  obtenhaMensagem(dados: any): string {
    let msg = this.template;

    msg = msg.replace(/\$\{(\w+(?:\.\w+)*)\}/g, function(match: string, propriedade: string) {
      let valores = propriedade.split(".");
      let objeto: any = dados;
      for (let i = 0; i < valores.length; i++) {
        if (!objeto.hasOwnProperty(valores[i])) {
          return match;
        }
        objeto = objeto[valores[i]];
      }
      return objeto;
    });

    return msg;
  }
}
