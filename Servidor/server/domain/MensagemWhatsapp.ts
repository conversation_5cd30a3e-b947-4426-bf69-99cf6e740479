import {Empresa} from "./Empresa";
import {TipoDeNotificacaoEnum} from "./TipoDeNotificacaoEnum";
import {Contato} from "./Contato";
import {Cartao} from "./Cartao";
import {StatusDeMensagem} from "../service/StatusDeMensagem";
import {Campanha} from "./Campanha";
import {LinkEncurtado} from "./LinkEncurtado";
import {EnumMeioDeEnvio} from "./EnumMeioDeEnvio";
import {NumeroWhatsapp} from "./NumeroWhatsapp";
import {EnumStatusMsgWhatsapp} from "./EnumStatusMsgWhatsapp";
import {Conversa} from "./whatsapp/Conversa";

export class MensagemWhatsapp {
  id: number;
  empresa: Empresa;
  mensagem: string;
  versao: number;
  idMensagem: string;
  idMensagemReply: string;
  idMensagemGsReply: string;
  horario: number;
  tipo: string;
  tipoPayload: string;
  status: EnumStatusMsgWhatsapp;
  countryCode: string;
  telefone: string;
  dialCode: string;
  nome: string;
  horarioModificacao: number;
  mensagemCompleta: string;
  conversa: Conversa;

  constructor() {}

  static nova(empresa: Empresa, dialCode: string, telefone: string, mensagem: string,
              idMensagem: string, idMensagemReply: string, idMensagemGsReply: string,
              horarioTimestamp: number, tipo: string, tipoPayload: string, countryCode: string,
              versao: number, nome: string, conversa: Conversa, mensagemJson: string): MensagemWhatsapp {
    const mensagemBot = new MensagemWhatsapp();

    mensagemBot.empresa = empresa;
    mensagemBot.mensagem = mensagem;
    mensagemBot.versao = versao;
    mensagemBot.idMensagem = idMensagem;
    mensagemBot.idMensagemReply = idMensagemReply;
    mensagemBot.idMensagemGsReply = idMensagemGsReply;
    mensagemBot.horario = horarioTimestamp;
    mensagemBot.tipo = tipo;
    mensagemBot.tipoPayload = tipoPayload;
    mensagemBot.status = EnumStatusMsgWhatsapp.ENQUEUED;
    mensagemBot.countryCode = countryCode;
    mensagemBot.telefone = telefone;
    mensagemBot.dialCode = dialCode;
    mensagemBot.nome = nome;
    mensagemBot.horarioModificacao = Date.now();
    mensagemBot.mensagemCompleta = mensagemJson;
    mensagemBot.conversa = conversa;

    return mensagemBot;
  }
}
