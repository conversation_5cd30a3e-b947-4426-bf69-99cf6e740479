import { XMLParser} from 'fast-xml-parser';
import {TipoDeOperacao} from "../../utils/nfce/comunicacao/TipoDeOperacao";

export class InutilizacaoNotaFiscal {
    static options = {
        ignoreAttributes: false,
        attributeNamePrefix : "@_",
        attributesGroupName : "@_"
    }

    static parser = new XMLParser(InutilizacaoNotaFiscal.options);

  id: string;
  cnpj: string;
  justificativa: string;
  modelo: string;
  serie = '1';
  numeracaoInicial: number;
  numeracaoFinal: number;
  descricaoStatus: string;
  uf: string;
  motivo = '';
  status: number;
  operacao: number;
  ambiente: number;

  public static obtenhaInutilizacaoAPartirDeXML(inutilizacaoEmXML: string): InutilizacaoNotaFiscal {
    // Implementação do parseamento do XML
    // Ajuste conforme a estrutura do seu XML
    const xml = this.parser.parse(inutilizacaoEmXML);
    const tag_infInut = xml.infInut;

    const inutilizacao = new InutilizacaoNotaFiscal();
    inutilizacao.processeTagInfInut(tag_infInut);
    inutilizacao.operacao = TipoDeOperacao.Inutilizar; // Assumindo a existência de Operacao.Inutilizar

    console.log(`Inutilizando ${inutilizacao.toString()}`);
    return inutilizacao;
  }




    toString(): string {
        return `InutilizacaoNotaFiscal(CNPJ: ${this.cnpj}, UF: ${this.uf}, NumInicial: ${this.numeracaoInicial}, NumFinal: ${this.numeracaoFinal})`;
    }

    private processeTagInfInut(tag_infInut: any): void {
        console.log(`Processando tag ${tag_infInut}`);

        // Implementação do processamento da tag InfInut
        // Ajuste conforme a estrutura do seu XML
        this.cnpj = tag_infInut.CNPJ;
        this.justificativa = tag_infInut.xJust;
        this.modelo = tag_infInut.mod;
        this.serie = tag_infInut.serie;
        this.numeracaoInicial = tag_infInut.nNFIni;
        this.numeracaoFinal = tag_infInut.nNFFin;
        this.uf = tag_infInut.cUF;
    }


    public ehValida(): boolean {
        return (this.cnpj !== '' && this.justificativa !== '' && this.modelo !== '' && this.serie !== ''
            && this.numeracaoInicial !== null && this.numeracaoFinal !== null && this.uf !== '');
    }


  obtenhaId(): string {
    //gera o id da inutilização com base no padrão ID{UF}{ANO}{CNPJ}{MODELO}{SERIE3}{NUMINICIAL9}{NUMFINAL9}
    //onde: UF tem 2 dígitos, cnpj tem 14, serie3 tem 3, numInicial9 tem 9, numFinal9 tem 9

    const uf = this.uf.padStart(2, '0');
    const ano = new Date().getFullYear().toString();
    const cnpj = this.cnpj.padStart(14, '0');
    const modelo = this.modelo.padStart(2, '0');
    const serie = this.serie.padStart(3, '0');
    const numInicial = this.numeracaoInicial.toString().padStart(9, '0');
    const numFinal = this.numeracaoFinal.toString().padStart(9, '0');

    return `ID${uf}${ano}${cnpj}${modelo}${serie}${numInicial}${numFinal}`;



  }
}
