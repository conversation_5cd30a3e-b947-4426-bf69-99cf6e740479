import {Vei<PERSON><PERSON>} from "./Veiculo";
import {NotaFiscalEletronica} from "./NotaFiscalEletronica";
import {TransporteXml} from "../../utils/nfce/xml/TransporteXml";

export class Transporte {
  cnpj: any;
  cpf: string;
  nome: string;
  inscricaoEstadual: string;
  enderecoCompleto: string;
  nomeMunicipio: string;
  siglaUF: string;
  valorDoServico: number;
  baseCalcICMS: number;
  aliquotaICMS: number;
  valorICMS: number;
  cfop: number;
  municipioFatorGerador: number;
  veiculoPrincipal: Veiculo;
  qtdeDeVolumes: number;
  especieVolumes: string;
  marcaVolumes: string;
  numeracaoVolumes: string;
  pesoLiquido: number;
  pesoBruto: number;


  static obtenhaTransporte(tagDeTransporte: any) {
    const transporte = new Transporte();

    const tagTransporte = tagDeTransporte.transporta;

    if (tagTransporte) {
      transporte.cnpj = tagTransporte.CNPJ;
      transporte.cpf = tagTransporte.CPF;
      transporte.nome = tagTransporte.xNome;
      transporte.inscricaoEstadual = tagTransporte.IE;
      transporte.enderecoCompleto = tagTransporte.xEnder;
      transporte.nomeMunicipio = tagTransporte.xMun;
      transporte.siglaUF = tagTransporte.UF;
    }

    const tagRetencao = tagDeTransporte.retTransp;

    if (tagRetencao) {
      transporte.valorDoServico = tagRetencao.vServ;
      transporte.baseCalcICMS = tagRetencao.baseCalcICMS;
      transporte.aliquotaICMS = tagRetencao.aliquotaICMS;
      transporte.valorICMS = tagRetencao.valorICMS;
      transporte.cfop = tagRetencao.cfop;
      transporte.municipioFatorGerador = tagRetencao.cMunFG;
    }

    const tagVeiculo = tagDeTransporte.veicTransp;

    if (tagVeiculo) {
      const veiculo = new Veiculo();
      transporte.veiculoPrincipal = veiculo;
      veiculo.placa = tagVeiculo.placa;
      veiculo.siglauf = tagVeiculo.UF;
      veiculo.rntc = tagVeiculo.RNTC;
    }

    const tagDeVolumes = tagDeTransporte.vol;

    if (tagDeVolumes) {
      transporte.qtdeDeVolumes = tagDeVolumes.qVol;
      transporte.especieVolumes = tagDeVolumes.esp;
      transporte.marcaVolumes = tagDeVolumes.marca;
      transporte.numeracaoVolumes = tagDeVolumes.nVol;
      transporte.pesoLiquido = tagDeVolumes.pesoL;
      transporte.pesoBruto = tagDeVolumes.pesoB;
    }

    return transporte;
  }

  static gereXml(nota: NotaFiscalEletronica): string {
    if(!nota.transporte) return "";

    return nota.transporte.gereXml();


  }

  public gereXml() {
    return new TransporteXml(this).obtenhaXml();
  }
}
