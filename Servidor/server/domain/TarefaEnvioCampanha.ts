import {Contato} from "./Contato";

const uuidv1 = require('uuid/v1');

export class TarefaEnvioCampanha {
  id: string;
  totalContatos: number;
  qtdeContatosProcessados: number;
  contatoAtual: Contato;
  percentual: number;
  terminou: boolean;

  constructor(totalContatos: number) {
    this.id = uuidv1();
    this.totalContatos = totalContatos;
    this.qtdeContatosProcessados = 0;
    this.percentual = 0.0;
    this.terminou = false;
  }

  novoContato(contato: Contato) {
    this.qtdeContatosProcessados ++;
    this.contatoAtual = contato;
    this.percentual = 100.0 * this.qtdeContatosProcessados / this.totalContatos;
  }

  completou() {
    this.terminou = true;
    this.percentual = 100.0;
  }
}
