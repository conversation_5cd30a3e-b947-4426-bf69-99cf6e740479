import {AplicacaoDeRegra, RegraDaPromocao} from "./RegraDaPromocao";
import {Pedido} from "./delivery/Pedido";
import {Promocao} from "./Promocao";

export class RegraDaPromocaoChinaInBoxRolinhos extends RegraDaPromocao {

  constructor(public ativa: boolean = true, public promocao: Promocao,
              valorMinimoPedido: number = 0) {

    super(promocao, 'promocao-china-in-box-rolinhos', valorMinimoPedido);
  }

  aplique(pedido: Pedido): AplicacaoDeRegra {
    if(!this.ativa) return new AplicacaoDeRegra(0);
    let descontoAplicado = 0
    let mapaTotalNoCarrinho: any = {
      "1109": 0,
      "1110": 0,
      "1124": 0,
      "1126": 0,
      "1136": 0,
      "35828": 0,
      "35826": 0,
      "35823": 0,
      "35824": 0,
      "35997": 0,
      "1101": 0,
      "1105": 0,
      "1123": 0,
      "1125": 0,
      "1132": 0,
      "35827": 0,
      "35825": 0,
      "35821": 0,
      "35822": 0,
      "35996": 0

    }

    for(let item of pedido.itens) {
      let todasEscolhas = item.obtenhaEscolhasTodosAdicionais()
      for (let escolha in todasEscolhas) {
        if(!escolha) {
          continue;
        }
        for(let opcaoEscolhida of todasEscolhas[escolha].opcoesEscolhidas) {
            if ( ["1109", "1110", "1124", "1126", "1136",
                "35828", "35826", "35823", "35824", "35997", "1101", "1105", "1123", "1125", "1132",
            "35827", "35825", "35821", "35822", "35996"].indexOf(opcaoEscolhida.opcao.codigoPdv)
            >= 0 && (item.qtde >= 2 || opcaoEscolhida.qtde >= 2)){
              let novoDesconto = 0.5 * opcaoEscolhida.opcao.valor

              if(novoDesconto > descontoAplicado)
                descontoAplicado = 0.5 * opcaoEscolhida.opcao.valor

            }
          else {
            mapaTotalNoCarrinho[opcaoEscolhida.opcao.codigoPdv] += 1

            if(mapaTotalNoCarrinho[opcaoEscolhida.opcao.codigoPdv] >= 2) {
              let novoDesconto = 0.5 * opcaoEscolhida.opcao.valor

              if(novoDesconto > descontoAplicado)
                descontoAplicado = 0.5 * opcaoEscolhida.opcao.valor
            }

          }
        }

      }
    }


    pedido.desconto += descontoAplicado

    return new AplicacaoDeRegra(descontoAplicado);
  }
}
