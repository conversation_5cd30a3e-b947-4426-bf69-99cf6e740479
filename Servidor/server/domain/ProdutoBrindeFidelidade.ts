import {Produto} from "./Produto";
import {ImagemDoProduto} from "./ImagemDoProduto";
import {Catalogo} from "./catalogo/Catalogo";
import {Categoria} from "./delivery/Categoria";
import {EnumTipoDeVenda} from "../lib/emun/EnumTipoDeVenda";

export class ProdutoBrindeFidelidade extends Produto {

  constructor(public  id: number= null, public nome: string = null, public valorResgate: number= null,
              public descricao: string = null, public mensagemPedido: string = null, public imagens: ImagemDoProduto[]= null ,
              public catalogo: Catalogo = null, public exibirNoSite: boolean = false,
              public disponibilidade: any = null,  public categoria: Categoria = null) {
    super(id, nome, 0, descricao, mensagemPedido, imagens, catalogo,  exibirNoSite,
      disponibilidade, null, categoria,   EnumTipoDeVenda.Unidade )

    this.tipo = 'brindefidelidade';

  }

  obtenhaPreco(tamanho: any, sabores: Array<any>){
    return 0;
  }

  brindeFidelidade(){
    return true;
  }

  obtenhaErroNaoDisponivel() {
    return  '';
  }
}

