import {MapeadorDeLinkEncurtado} from "../mapeadores/MapeadorDeLinkEncurtado";
const randtoken = require('rand-token');
import * as base62 from "base62";
import * as async from 'async';
import {RespostaEncurtarLinks} from "../utils/RespostaEncurtarLinks";

export class LinkEncurtado {
  static TAMANHO_TOKEN = 3;
  static EXPRESSAO_URL = /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g;

  id: number;
  token: string;
  url: string;
  visitas: number;
  dataCriacao: Date;
  ultimaVisita: Date;


  static encurte(url: string): Promise<LinkEncurtado> {
    return new Promise<LinkEncurtado>((resolve, reject) => {
      const novoLink = new LinkEncurtado( randtoken.generate(LinkEncurtado.TAMANHO_TOKEN), url);

      (new MapeadorDeLinkEncurtado()).insiraSync(novoLink).then( () => {
        resolve(novoLink);
      }).catch( (erro: any) => {
        reject(erro);
      });
    });
  }

  static obtenha(idUrl: string): Promise<LinkEncurtado> {
    return new Promise<LinkEncurtado>(resolve => {
      if(!idUrl) return resolve(null);

      if(idUrl.length < 4) return resolve(null);

      const tokenInformado = idUrl.slice(LinkEncurtado.TAMANHO_TOKEN * -1);
      const idEncodado = idUrl.substr(0, idUrl.length  - LinkEncurtado.TAMANHO_TOKEN);

      let id = base62.decode(idEncodado);


      (new MapeadorDeLinkEncurtado()).selecioneSync({id: id}).then( (linkEncurtado: LinkEncurtado) => {
        if(linkEncurtado.token !== tokenInformado) {
          return resolve(null);
        }

        resolve(linkEncurtado);
      }).catch( (erro) => {
        console.log(erro);
        resolve(null);
      });
    })
  }

  static encurteLinksDaMensagem(msgFinal: string): Promise<RespostaEncurtarLinks> {
    return new Promise<any>((resolve) => {
      const urls = msgFinal.match(LinkEncurtado.EXPRESSAO_URL);
      let mensagemEncurtada = msgFinal;
      let links: Array<LinkEncurtado> = [];

      async.eachSeries(urls,
        (url: string, cb) => {
        LinkEncurtado.encurte(url).then((linkEncurtado: LinkEncurtado) => {
          links.push(linkEncurtado);
          mensagemEncurtada = mensagemEncurtada.replace(url, linkEncurtado.obtenhaUrlEncurtadaCompleta());
          cb();
        });
      },
        (erro) => {
          resolve(new RespostaEncurtarLinks(mensagemEncurtada, links));
      });
    });
  }


  constructor(token: string, url: string) {
    this.token = token;
    this.url = url;
    this.visitas = 0;
    this.dataCriacao = new Date();
  }

  obtenhaUrlEncurtada() {
    return "/l/" + base62.encode(this.id) + this.token;
  }

  obtenhaUrlEncurtadaCompleta() {
    return "http://pkt.to" + this.obtenhaUrlEncurtada();
  }

  desencurte(): Promise<string> {
    return new Promise<string>(resolve => {
      this.visitas += 1;
      this.ultimaVisita = new Date();

      (new MapeadorDeLinkEncurtado()).atualizeSync(this).then( () => {
        resolve(this.url);
      });
    })
  }

}
