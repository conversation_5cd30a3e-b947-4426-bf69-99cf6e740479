{"name": "<PERSON><PERSON><PERSON>", "version": "3.1.0", "description": "Fully responsive web application ui kit", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "coderthemes", "repository": {"url": "http://coderthemes.com/ubold"}, "license": "https://themeforest.net/licenses/standard", "devDependencies": {"browser-sync": "^2.18.12", "gulp": "^3.9.1", "gulp-autoprefixer": "^4.0.0", "gulp-clean-css": "^3.9.4", "gulp-concat": "^2.6.1", "gulp-cssnano": "^2.1.2", "gulp-file-include": "^2.0.1", "gulp-htmlmin": "^4.0.0", "gulp-imagemin": "^3.3.0", "gulp-newer": "^1.3.0", "gulp-rename": "^1.2.2", "gulp-sass": "^3.1.0", "gulp-sourcemaps": "^2.6.0", "gulp-uglify": "^3.0.0", "gulp-sequence": "^1.0.0", "lodash": "^4.17.4", "sass-lint": "^1.10.2"}, "dependencies": {"@google/maps": "^0.4.6", "RWD-Table-Patterns": "^5.3.3", "admin-resources": "git+https://github.com/coderthemes/admin-resources#master", "animate.css": "^3.7.0", "autonumeric": "^1.9.39", "bootstrap": "^4.3.1", "bootstrap-colorpicker": "^3.0.3", "bootstrap-datepicker": "^1.8.0", "bootstrap-maxlength": "^1.6.0", "bootstrap-select": "^1.13.2", "bootstrap-table": "^1.12.1", "bootstrap-touchspin": "^4.2.5", "c3": "^0.6.7", "chart.js": "^2.7.2", "chartist": "^0.11.0", "chartist-plugin-tooltips": "0.0.17", "clockpicker": "0.0.7", "cropper": "^4.0.0", "custombox": "^4.0.3", "d3": "^5.7.0", "datatables.net": "^1.10.19", "datatables.net-bs4": "^1.10.19", "datatables.net-buttons": "^1.4.2", "datatables.net-buttons-bs4": "^1.4.2", "datatables.net-keytable": "^2.3.2", "datatables.net-keytable-bs4": "^2.3.2", "datatables.net-responsive": "^2.2.1", "datatables.net-responsive-bs4": "^2.2.1", "datatables.net-select": "^1.2.5", "datatables.net-select-bs4": "^1.2.5", "devbridge-autocomplete": "^1.4.9", "dropify": "^0.2.2", "dropzone": "^5.5.1", "flatpickr": "^4.5.2", "flot-charts": "^0.8.3", "flot-orderbars": "^1.0.0", "footable": "^2.0.6", "fullcalendar": "^3.10.0", "gmaps": "^0.4.24", "hopscotch": "^0.3.1", "ion-rangeslider": "^2.3.0", "jquery": "^3.3.1", "jquery-countdown": "^2.2.0", "jquery-knob": "^1.2.11", "jquery-mapael": "^2.2.0", "jquery-mask-plugin": "^1.14.15", "jquery-mockjax": "^2.5.0", "jquery-nice-select": "^1.1.0", "jquery-slimscroll": "^1.3.8", "jquery-sparkline": "^2.4.0", "jquery-tabledit": "^1.0.0", "jquery-toast-plugin": "^1.3.2", "jquery-ui": "git+https://github.com/components/jqueryui.git#1.11.4", "jquery.counterup": "^2.1.0", "jquery.flot.tooltip": "^0.9.0", "jquery.scrollto": "^2.1.2", "jsgrid": "^1.5.3", "justgage": "^1.2.2", "katex": "^0.10.0-rc.1", "ladda": "^1.0.6", "magnific-popup": "^1.1.0", "metismenu": "^2.7.4", "mohithg-switchery": "^0.8.4", "moment": "^2.22.2", "morris.js": "^0.5.0", "multiselect": "^0.9.12", "natives": "^1.1.6", "nestable2": "^1.6.0", "node-waves": "^0.7.6", "parsleyjs": "^2.8.1", "pdfmake": "^0.1.38", "peity": "^3.3.0", "popper.js": "^1.14.7", "quill": "^1.3.6", "raphael": "^2.2.7", "rickshaw": "^1.6.6", "select2": "^4.0.6-rc.1", "summernote": "^0.8.11", "sweetalert2": "^7.33.1", "tablesaw": "^3.0.9", "tippy.js": "^2.5.4", "toastr": "^2.1.4", "twitter-bootstrap-wizard": "^1.2.0", "waypoints": "^4.0.1", "x-editable": "^1.5.1"}}