/**
 * Copyright 2021-present, Facebook, Inc. All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * Instagram For Original Coast Clothing
 *
 */

const { I18n } = require('i18n');
const path = require("path");

export class I18nConfig {
  static _instance: I18nConfig;
  private _i18n: any;

  public static get Instance(){
    return this._instance || (this._instance = new I18nConfig());
  }

  constructor() {
    const caminho = path.join(__dirname, "../../locales");
    console.log(caminho);
    this._i18n = new I18n()

    this._i18n.configure({
      locales: ["en_US"],
      defaultLocale: "en_US",
      directory: caminho,
      objectNotation: true,
      api: {
        __: "translate",
        __n: "translateN"
      }
    });
  }

  i18n() {
    return this._i18n;
  }
}
