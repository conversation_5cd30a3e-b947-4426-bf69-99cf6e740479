import axios from "axios";

export class TratadorDeMensagemWitai {
  processeTexto(q: string): Promise<string> {
    return new Promise( async (resolve, reject) => {
      const tokenStr = 'AMIDZM7SE4GYFY6TBHATYD2YXMN5KQGK';

      const url = `https://api.wit.ai/message?v=20210904&q=${encodeURIComponent(q)}`;

      try {
        const dados = await axios.get(url, {
          headers:
            {"Authorization": `Bearer ${tokenStr}`}
        });

        const resposta = dados.data;

        if (resposta.intents.length === 0) {
          console.log('Não achou nenhuma intent:', q, ' r: ', resposta);

          return resolve('NAO_ENTENDI');
        }

        const intent = resposta.intents[0];

        if (intent.confidence < 0.70) {
          console.log('Confiança menor que 0.8: ', intent.confidence);

          return resolve('NAO_ENTENDI');
        }

        resolve(intent.name.toLowerCase());

        return;
      } catch (erro) {
        console.log(erro);
        return resolve('FALHOU');
      }
    });
  }

  classifiqueIntentBotDuvidas(q: string): Promise<string> {
    return new Promise( (resolve, reject) => {
      const tokenStr = 'OIOP6BNKYC4NIJBYEX5KC2V7YU2YCDNN';

      const url = `https://api.wit.ai/message?v=20230227&q=${encodeURIComponent(q)}`;

      axios.get(url, {headers:
          {"Authorization": `Bearer ${tokenStr}`}}).then(
        (dados: any) => {
          const resposta = dados.data;

          if (resposta.intents.length === 0) {
            return resolve('NAO_ENTENDI');
          }


          const intent = resposta.intents[0];

          if( intent.confidence < 0.75 ) {
            return resolve('NAO_ENTENDI');
          }

          resolve(intent.name.toLowerCase());

          return;
        }).catch( (erro) => {
        return resolve('FALHOU');
      });
    });
  }
}
