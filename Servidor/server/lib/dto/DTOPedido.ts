import {Pedido} from "../../domain/delivery/Pedido";
import {StatusPagamentoPedidoLabel} from "../emun/EnumStatusPagamento";
import {ItemPedido} from "../../domain/delivery/ItemPedido";
import {<PERSON>rindeResgatado} from "../../domain/BrindeResgatado";
import {Produto} from "../../domain/Produto";
import {DTOComanda} from "./DTOComanda";
import {PedidoGenerico} from "../../domain/delivery/PedidoGenerico";
import {MultiPedido} from "../../domain/delivery/MultiPedido";
import {DTODadosPedido} from "./DTODadosPedido";

// @ts-ignore
import moment = require("moment");
// @ts-ignore
import _ = require("underscore");
import {PedidoBeneficio} from "../../domain/integracoes/PedidoBeneficio";
import {inflate} from "zlib";
import {PedidoAlteracaoEndereco} from "../../domain/integracoes/PedidoAlteracaoEndereco";
import {ValorDeAdicionaisEscolhaSimples} from "../../domain/delivery/ValorDeAdicionaisEscolhaSimples";
import {ValorDeAdicionaisMultiplaEscolha} from "../../domain/delivery/ValorDeAdicionaisMultiplaEscolha";

export class DTOPedido extends DTODadosPedido{
  mesa: any;
  promocoesAplicadas: any[];
  adicionaisImprimir: any;
  comanda: DTOComanda;
  referenciaExternaDelivery: string
  referenciaExterna: string
  naoNotificado: boolean;
  naoNotificadoLogistica = false;
  erroExterno: string
  garcom: any;
  agruparAdicionais: boolean;
  numCupomFiscal: string;
  multipedido: boolean;
  empresas: any = [];
  entregadorReduzido: string;
  objetoEntregador: any;
  delivery: any;
  disputa: any;
  alteracaoEndereco: any;
  ifood: any;
  patrocinadoresDesconto: string;
  naoAlterarStatus = false;
  notaFiscalEmitida: boolean = false;
  notaFiscal: any = null;
  pedirNovamente: boolean;
  ocultarCobranca = false;
  gerarMensagemlinkPagamento: boolean
  enviarParaPDV = false;
  constructor(pedido: PedidoGenerico, empresa: any) {
    super(pedido, empresa);
    this.referenciaExterna = pedido.referenciaExterna;
    this.referenciaExternaDelivery = pedido.referenciaExternaDelivery;
    this.erroExterno =  pedido.erroExterno
    this.visualizado = pedido.visualizado;
    this.pontosGanhos = pedido.pontosGanhos;
    if(empresa.integracaoDelivery)
      this.naoNotificado =  pedido.erroExterno && !pedido.referenciaExterna;

    if(pedido.erroExternoDelivery && !pedido.referenciaExternaDelivery){
      this.naoNotificadoLogistica = true;
      this.erroExterno = pedido.erroExternoDelivery ;
    }

    if(empresa.configImpressao && empresa.configImpressao.ocultarCobranca)
      this.ocultarCobranca = true;

    if (pedido.nfce && pedido.nfce.id) {
      this.notaFiscalEmitida = true;
      this.notaFiscal = {
        id: pedido.nfce.id,
        numeroNFe: pedido.nfce.numeroNFe,
        serie: pedido.nfce.serie,
        chaveDeAcesso: pedido.nfce.chaveDeAcesso,
        status: pedido.nfce.status,
        dataDeEmissao: pedido.nfce.dataDeEmissao
      };
    }

    this.doIfood =  pedido.doIfood()
    this.balcao = pedido.deBalcao();
    if(pedido.deliveryPedido){
      this.delivery =  pedido.deliveryPedido.toDTO()
      if(pedido.deliveryPedido.foiAceita)
        this.naoNotificadoLogistica = false;

      if(pedido.foiEntregue() || pedido.foiCanceladoOuDevolvido())
        this.delivery.finalizado = true

      if(pedido.ifoodCodigoRetirada)
        this.delivery.codigoRetirada = pedido.ifoodCodigoRetirada


      if(!pedido.deliveryPedido.aindaNaoColetou())
        this.delivery.codigoRetirada  = null;
    }

    if(pedido.disputa)
      this.disputa = pedido.disputa.toDto( (pedido as any).itens, pedido.idLojaExterna);

    if(pedido.alteracaoEndereco && pedido.alteracaoEndereco.aceito == null )
      this.alteracaoEndereco =   pedido.alteracaoEndereco.toDto();

    if(pedido.doIfood()){
      let ifoodExtra: any   = pedido.ifoodExtra ? JSON.parse(pedido.ifoodExtra) : {};
      let codigoLocalizador = ifoodExtra.localizer ?
        `${ifoodExtra.localizer.substring(0, 4)} ${ifoodExtra.localizer.substring(4)}` : null

      this.ifood = {
          numeroCliente: ifoodExtra.number,
          localizadorPedido: codigoLocalizador,
          displayId: ifoodExtra.displayId,
          taxaServico: pedido.ifoodTaxaServico || 0,
          entredaPeloIfood: ifoodExtra.deliveredBy === 'IFOOD'
      }

      if(this.ifood.entredaPeloIfood)
         this.aguardandoColeta = false;

      if(pedido.ifoodCodigoRetirada)
        this.ifood.codigoRetirada =  pedido.ifoodCodigoRetirada;

      if(ifoodExtra.cpfNota)
        this.ifood.cpfNota = ifoodExtra.cpfNota;

      if(ifoodExtra.deliveredBy)
        this.ifood.entregadorPor =
          (ifoodExtra.deliveredBy === 'MERCHANT' ? "Entrega Própria" : 'Entrega por ' + ifoodExtra.deliveredBy);

      if(pedido.saiuParaEntrega() || pedido.foiEntregue())
        this.naoAlterarStatus = true;

    }

    if(pedido.beneficios.length)
      this.patrocinadoresDesconto =  pedido.beneficios.map((item: PedidoBeneficio) => item.obtenhaPatrocinador()).join(', ')

    if(pedido.ehMultipedido()){
      this.setDadosMultipedido(pedido as MultiPedido)
    } else {
      this.setDadosPedido(pedido as Pedido, empresa);
    }

    if(pedido.cupom) this.cupom = pedido.cupom.nome;
    if(pedido.promocoesAplicadas) {
      this.promocoesAplicadas = pedido.promocoesAplicadas
      this.promocoesAplicadas.forEach((item: any) => {
        delete item.pedido
      })
    }
    if(pedido.entregador) {
      let nomereduzido = pedido.entregador.obtenhaNomeReduzido()

      this.entregadorReduzido = nomereduzido
      this.entregador = pedido.entregador.nome
      this.objetoEntregador = pedido.entregador
    }
    this.agruparAdicionais = empresa.agruparAdicionais;

    if(pedido.feitoNoIframe() && pedido.pagarViaPix() && pedido.aguardandoPagamentoOnline() )
      this.gerarMensagemlinkPagamento = true;

    if( this.possuiValor(pedido.adicionaisEscolhaSimples, pedido.adicionaisMultiplaEscolha) ) {

      const objItem = new ItemPedido(new Produto(null, 'Extras do Pedido'), null, 1, '', 0, null);
      objItem.adicionaisEscolhaSimples = pedido.adicionaisEscolhaSimples;
      objItem.adicionaisMultiplaEscolha = pedido.adicionaisMultiplaEscolha;
      objItem.definicoesDosAdicionais = empresa.camposAdicionais
      objItem.calculeTotal();
      this.itens.push(new DTOItemPedido(objItem));
    }

    if(empresa.integracaoPedidoFidelidade){
      this.temCashback = empresa.integracaoPedidoFidelidade.plano.ehCashback();
      this.pontosGanhosDescricao = empresa.integracaoPedidoFidelidade.plano.obtenhaDescricaoPontos(pedido.pontosGanhos);
    }

    if(empresa.integracaoDelivery && !this.multipedido &&    !this.aguardandoPagamentoOnline){
      if(this.aceito && !this.referenciaExterna){
        this.enviarParaPDV =   this.mesa ?     !empresa.integracaoDelivery.enviarMesasFecharConanda() : true;
      }
    }

    if(!pedido.ehDelivery()){
      //todo: tem bug salvar endereço para retirada
      delete this.endereco;
    }
  }

  setDadosPedido(pedido: Pedido, empresa: any){
    this.operador = pedido.operador;
    this.aceito = pedido.aceito;
    this.numCupomFiscal = pedido.numCupomFiscal;
    this.garcom = pedido.garcom;
    this.multipedido = pedido.fazParteMultipedido();
    let _empresa: any = {id: empresa.id,  nome: empresa.nome, totalProdutos: pedido.itens.length};

    this.empresas.push(_empresa);
    if(this.multipedido || pedido.integrouComIfood() || pedido.pago)
      this.podeEditar = false;


    let itensOrdenados = this.obtenhaItensOrdenados(pedido.itens)

    this.itens = itensOrdenados.map(item => new DTOItemPedido(item));

    if(pedido.mesa && pedido.mesa.id)
      this.mesa = { nome: pedido.mesa.nome, id: pedido.mesa.id  };

    if( pedido.comanda && pedido.comanda.id && pedido.mesa )
      this.comanda = new DTOComanda(pedido.comanda, empresa);

    let trocasDeProdutos = pedido.trocas.filter((brindeResgatado: any) =>
        !brindeResgatado.cartao.plano.acumulaReais() && !brindeResgatado.gerouItemPedido());

    let trocas: any = _.groupBy(trocasDeProdutos, troca => troca.brinde.id)

    Object.keys(trocas).forEach( brinde => this.itens.push(new DTOItemPedidoTroca(trocas[brinde][0], trocas[brinde].length )))

    this.pedirNovamente = !pedido.trocas.length;

  }

  setDadosMultipedido(multipedido: MultiPedido){
    multipedido.pedidos.forEach((pedido: Pedido) => {

      let empresa: any = {id: pedido.empresa.id,  nome: pedido.empresa.nome, totalProdutos: pedido.itens.length};

      this.empresas.push(empresa);
      let itensOrdenados = this.obtenhaItensOrdenados(pedido.itens);

      itensOrdenados.forEach((itemPedido: any) => {
        let item = new DTOItemPedido(itemPedido);
        item.empresa = empresa;
        this.itens.push(item);
      })
    })
  }

  setDescontosETaxas(){
    //todo: fazer metodo para tirar ifs da telas impressao , return taxas e decontos no itens
    let taxasDescontos: any   = []

    taxasDescontos.push({ descricao: 'Taxa entrega', operacao: '(+)', valor: this.taxaEntrega !== -1 ?  this.taxaEntrega : 'A Informar'})

    if(this.descontoTaxaEntrega){
      if(this.cupom){
        taxasDescontos.push({descricao: 'Cupom' + this.cupom , operacao: '-', valor: this.descontoTaxaEntrega})
      } else {
        taxasDescontos.push({descricao: 'Desconto Taxa', operacao: '-', valor: this.descontoTaxaEntrega})
      }
    }
  }

  private possuiValor(adicionaisEscolhaSimples: ValorDeAdicionaisEscolhaSimples,
                      adicionaisMultiplaEscolha: ValorDeAdicionaisMultiplaEscolha) {
    if(!adicionaisEscolhaSimples && !adicionaisMultiplaEscolha) return false

    let possuiPropriedade = false

    if(adicionaisEscolhaSimples)
      for(let i = 0; i < 10; i++) {
        let propriedade = 'campo' + i as keyof ValorDeAdicionaisEscolhaSimples
        if(adicionaisEscolhaSimples[propriedade]){
          possuiPropriedade = true;
          break;
        }
      }

    if(adicionaisMultiplaEscolha)
      for(let i = 0; i < 10; i++) {
        let propriedade = 'lista' + i as keyof ValorDeAdicionaisMultiplaEscolha
        if(adicionaisMultiplaEscolha[propriedade] && adicionaisMultiplaEscolha[propriedade].opcoes.length > 0){
          possuiPropriedade = true;
          break;
        }
      }

    return possuiPropriedade;
  }
}

export class DTOPagamento{
  id: number;
  valor: number;
  taxa: number;
  formaDePagamento: string;
  status: string;
  trocoPara  = 0;
  valorTroco = 0;
  levarTroco = false;
  link: string;
  descricao: string;
  cartaoMask: string;
  online: boolean
  autenticado: boolean;
  statusNumero: number;
  resgate: boolean;
  fidelidade: boolean;
  pixManual: boolean;
  constructor(pagamento: any, total: number) {
    this.id = pagamento.id;
    this.formaDePagamento = pagamento.formaDePagamento;
    if( pagamento.formaDePagamento && pagamento.formaDePagamento.configMeioDePagamento)
        pagamento.formaDePagamento.apagueCredenciais();
    this.statusNumero =  pagamento.status;
    this.status = StatusPagamentoPedidoLabel.get(Number(pagamento.status));

    this.valor = pagamento.valor;
    this.taxa = pagamento.taxa;
    this.link = pagamento.link;
    this.descricao = pagamento.obtenhaDescricao()
    this.online =  pagamento.foiOnline();
    this.pixManual = pagamento.foiPixManual();
    this.autenticado = pagamento.codigoAutenticacao != null;
    if(pagamento.foiPorResgate())
      this.resgate = true;

    if(pagamento.pagoPorPlanoFidelidade())
      this.fidelidade = true;

    if(pagamento.foiPorDinheiro()){
      if( pagamento.trocoPara > 0){
        this.trocoPara = pagamento.trocoPara;
        this.valorTroco = pagamento.trocoPara - pagamento.valor
        this.levarTroco = this.valorTroco > 0;
      }
    } else if(pagamento.finalCartao){
      this.cartaoMask = String(`**** **** **** ${pagamento.finalCartao}`)
    }
  }
}

export class DTOItemPedido {
  id: number;
  descricao: string;
  qtde: number;
  valor: number;
  total: number
  valorResgatado: number
  observacao: string;
  unidade: string;
  unidadeTxt: string;
  descricaoQtde: string;
  produto: any;
  adicionaisEscolhaSimples: any;
  adicionaisMultiplaEscolha: any;
  adicionais: any;
  adicionaisImprirmir: any = [];
  produtoTamanho: any;
  sabores: any;
  adicionaisAgrupados: any[];
  empresa: any;
  constructor(itemPedido: ItemPedido) {
    if(!itemPedido) return;

    this.id = itemPedido.id;
    this.descricao = itemPedido.descricao
    this.qtde = itemPedido.qtde;
    this.valor = itemPedido.valor;
    this.total =  itemPedido.total;
    this.valorResgatado =  itemPedido.valorResgatado || 0;
    this.produto = itemPedido.produto;
    this.produtoTamanho = itemPedido.produtoTamanho;
    this.sabores = itemPedido.sabores;
    this.observacao = itemPedido.observacao;
    this.unidade = itemPedido.obtenhaUnidade();
    this.unidadeTxt = itemPedido.obtenhaUnidadeTexto();
    this.descricaoQtde =  String(`${this.qtde}${this.unidade}`).trim();
    this.produto.setValoresDeVenda();
    this.produto.carreguePrecoNaEmpresa();

    if(itemPedido.pedido && itemPedido.pedido.empresa)
       this.produto.empresa = { id: itemPedido.pedido.empresa.id, nome: itemPedido.pedido.empresa.nome};

    if(this.sabores && this.sabores.length){
      this.sabores.forEach( (sabor: any) => {
        if(sabor.produtoTamanho) sabor.produtoTamanho.setValoresDeVenda();
        sabor.produto.setValoresDeVenda()
      })
    }

    if(this.produtoTamanho) this.produtoTamanho.setValoresDeVenda();

    this.prepareAdicionaisDeTela(itemPedido);

  }

  private prepareAdicionaisDeTela(itemPedido: ItemPedido) {
    this.adicionaisEscolhaSimples = itemPedido.adicionaisEscolhaSimples
    this.adicionaisMultiplaEscolha = itemPedido.adicionaisMultiplaEscolha
    this.adicionais = {};


    this.adicionaisImprirmir = itemPedido.obtenhaAdicionaisImprimir()

    if (itemPedido.adicionaisEscolhaSimples)
      Object.assign(this.adicionais, itemPedido.adicionaisEscolhaSimples.convertaParaTela())

    if (itemPedido.adicionaisMultiplaEscolha)
      Object.assign(this.adicionais, itemPedido.adicionaisMultiplaEscolha.convertaParaTela())


    this.calculeAdicionaisAgrupados()
    this.descricao = itemPedido.obtenhaDescricaoProduto(true)

  }

  private calculeAdicionaisAgrupados() {
    if(!this.adicionaisImprirmir || this.adicionaisImprirmir.length < 1)
      return;

    this.adicionaisAgrupados = []

    let adicionalAtual = null
    let grupoAtual = null
    let item = this;


    for(let opcaoSelecionada of item.adicionaisImprirmir) {
      if(!opcaoSelecionada.sabor && !opcaoSelecionada.adicional) continue;

      if((adicionalAtual === null) ||
        (!opcaoSelecionada.sabor && (adicionalAtual.id !== opcaoSelecionada.adicional.id))) {
        if(grupoAtual) item.adicionaisAgrupados.push(grupoAtual);

        if(opcaoSelecionada.sabor) {
          adicionalAtual = { nome: 'Sabores' };
        } else {
          adicionalAtual = opcaoSelecionada.adicional;
        }

        grupoAtual = {
          nomeAdicional: adicionalAtual ? adicionalAtual.nome : "Opções",
          opcoes: []
        };
      }

      grupoAtual.opcoes.push(opcaoSelecionada);
    }

    if(grupoAtual)
      item.adicionaisAgrupados.push(grupoAtual)
  }


}

export class DTOItemPedidoTroca extends DTOItemPedido{
  brinde: any;
  cartao: any;
  troca    = true;
  constructor(brindeResgatado: BrindeResgatado, qtde: number) {
    super(null);
    this.id = brindeResgatado.id;
    this.qtde = qtde;
    this.valor = qtde * brindeResgatado.valorEmPontos;
    this.total = this.valor;
    this.brinde = brindeResgatado;
    this.produto = {nome : brindeResgatado.obtenhaNomeBrinde(), preco: 0 }
    this.cartao = { id: brindeResgatado.cartao.id }
    this.descricao = brindeResgatado.obtenhaNomeBrinde();
  }

  /*
          qtde: brindeResgatado.qtde,
        produto: { nome: brindeResgatado.brinde.nome, preco: 0},
        brinde: brindeResgatado.brinde ,
        cartao: { id: brindeResgatado.cartao.id }
   */
}
