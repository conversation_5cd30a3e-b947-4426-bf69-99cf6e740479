import {Pedido} from "../../domain/delivery/Pedido";

// @ts-ignore
import * as pluralize from "pluralize";
import {EnumStatusPedido, StatusPedidoLabel} from "../emun/EnumStatusPedido";
import {StatusPagamentoPedidoLabel} from "../emun/EnumStatusPagamento";
// @ts-ignore
import moment = require("moment");
// @ts-ignore
import _ = require("underscore");
import {ItemPedido} from "../../domain/delivery/ItemPedido";
import {BrindeResgatado} from "../../domain/BrindeResgatado";


export class DTOPedidoAPI {
  id: number;
  codigo: string;
  horario: any;
  cliente: any = {};
  operador: any = {};
  endereco: any;
  status: string;
  statusOrdem: number;
  subvalor: number;
  desconto: number;
  cashback: number;
  total: number;
  itens: any = [];
  pagamentos: any = [];
  pago: boolean;
  cancelado: boolean;
  podeEditar: boolean;
  taxaEntrega: number;
  observacoes: string;
  finalizado: boolean
  retirar: boolean
  formaDeEntrega: string;
  horarioEntregaAgendada: Date;
  mesa: any;
  aguardandoPagamentoOnline: boolean;
  foiPagoOnline: boolean;
  cupom: string;
  pontosGanhos: any;
  temCashback: boolean;
  comerNoLocal: boolean;
  constructor(pedido: Pedido, integracaoPedidoFidelidade: any = null) {
    this.codigo = pedido.codigo;
    this.horario = pedido.horarioAtualizacao;
    if( pedido.contato ) {
      this.cliente = pedido.contato.obtenhaDTOContatoAPI();
      this.cliente.telefone = pedido.contato.telefone
    }
    this.endereco = pedido.endereco;
    this.operador = pedido.operador;
    this.statusOrdem = Number(pedido.status);
    this.itens = pedido.itens.map(item => new DTOItemPedidoAPI(item));
    this.subvalor = pedido.valor +  pedido.desconto;
    this.desconto = pedido.desconto;
    this.taxaEntrega = pedido.taxaEntrega;
    this.total = pedido.obtenhaTotal();

    this.pago = pedido.pago;
    this.cancelado = pedido.foiCanceladoOuDevolvido();
    this.podeEditar =  pedido.status <= 3 && !pedido.doIfood();
    this.pagamentos = pedido.pagamentos;
    this.horarioEntregaAgendada = pedido.horarioEntregaAgendada;
    this.pagamentos = pedido.pagamentos ? pedido.pagamentos.map( pagamento => new DTOPagamentoAPI(pagamento, this.total)) : []
    this.observacoes = pedido.observacoes;
    this.finalizado = pedido.pago && Number(pedido.status) > 3;
    this.formaDeEntrega = pedido.formaDeEntrega ? pedido.formaDeEntrega.nome : '';
    this.retirar = pedido.retirarPessoalmente();

    if(this.retirar)
      this.comerNoLocal = pedido.comerNoLocal

    this.aguardandoPagamentoOnline = pedido.pagarOnline() && !pedido.pago && !pedido.foiCanceladoOuDevolvido();
    this.foiPagoOnline = pedido.pagarOnline() &&  pedido.pago;


    this.status =  this.aguardandoPagamentoOnline ? this.obtenhaStatusPagamentoOnline(pedido) :
      StatusPedidoLabel.get(Number(pedido.status));

    if(pedido.mesa)
      this.mesa = { nome: pedido.mesa.nome, id: pedido.mesa.id, codigoPdv: pedido.mesa.codigoPdv };

    if(pedido.cupom) this.cupom = pedido.cupom.nome;

    let trocasDeProdutos = pedido.trocas.filter((brindeResgatado: any) =>  !brindeResgatado.cartao.plano.ehCashback());

    let trocas: any = _.groupBy(trocasDeProdutos, troca => troca.brinde.id)

    Object.keys(trocas).forEach( brinde => this.itens.push(new DTOItemPedidoTrocaAPI(trocas[brinde][0], trocas[brinde].length )))

    this.pontosGanhos = pedido.pontosGanhos;
    if(integracaoPedidoFidelidade){
      this.temCashback = integracaoPedidoFidelidade.plano.ehCashback();
      this.pontosGanhos = integracaoPedidoFidelidade.plano.obtenhaDescricaoPontos(pedido.pontosGanhos);
    }

  }

  private obtenhaStatusPagamentoOnline(pedido: Pedido) {
    switch(pedido.status) {
      case EnumStatusPedido.FalhaNoPagamento:
        return 'Falha no pagamento';
      case EnumStatusPedido.PagamentoRecusado:
        return 'Pagamento recusado';
    }

    return "Pagamento a confirmar";
  }
}

export class DTOPagamentoAPI{
  id: number;
  valor: number;
  formaDePagamento: string;
  status: string;
  trocoPara: number;
  valorTroco: number;
  online: boolean
  levarTroco: boolean;
  link: string;
  constructor(pagamento: any, total: number) {
    this.id = pagamento.id;
    this.formaDePagamento = pagamento.formaDePagamento.nome;
    this.online = pagamento.formaDePagamento.online
    this.status = StatusPagamentoPedidoLabel.get(pagamento.status);
    this.trocoPara = pagamento.trocoPara;
    this.levarTroco = pagamento.trocoPara > 0;
    this.valor = pagamento.valor;
    if(pagamento.trocoPara)
      this.valorTroco = pagamento.trocoPara - pagamento.valor
  }
}

export class DTOItemPedidoAPI {
  id: number;
  nome: string;
  qtde: number;
  valor: number;
  total: number
  observacao: string;
  unidade: string;
  codigoPDV: string;
  produto: any;
  private adicionais: any;
  sabores: any;
  constructor(itemPedido: ItemPedido) {
    if(!itemPedido) return;
    this.id = itemPedido.id;
    this.nome = itemPedido.descricao;
    this.qtde = itemPedido.qtde;
    this.valor = itemPedido.valor;
    this.total =  itemPedido.total;
    this.codigoPDV = itemPedido.produto.codigoPdv;
    this.produto = itemPedido.produto
    this.observacao = itemPedido.observacao;
    this.unidade = itemPedido.obtenhaUnidade();
    this.sabores = itemPedido.sabores;
    this.prepareAdicionaisDeTela(itemPedido)
  }

  private prepareAdicionaisDeTela(itemPedido: any) {
    this.adicionais = {}

    if(itemPedido.adicionaisEscolhaSimples)
      Object.assign(this.adicionais, itemPedido.adicionaisEscolhaSimples.convertaParaTela())

    if(itemPedido.adicionaisMultiplaEscolha)
      Object.assign(this.adicionais, itemPedido.adicionaisMultiplaEscolha.convertaParaTela())

    let keys  = Object.keys(this.adicionais);

    for(let i = 0; i < keys.length; i++ ){
      if(keys[i].startsWith('lista')){
        let adicional: any = this.adicionais[keys[i]];

        let props = Object.keys(adicional);

        adicional.opcoes = [];
        let indice = 0;
        for(let j = 0;  j < props.length; j++ ){

          if(props[j].startsWith('opcao_')){
            let prop: any = adicional[props[j]];
            let opcao: any = Object.assign({}, prop.opcao);


            opcao.qtde  = prop.qtde;
            opcao.valorTotal  = prop.valorTotal;
            opcao.indice = indice++;

            delete opcao.disponivel;
            delete opcao.template;
            delete opcao.dependencias;

            adicional.opcoes.push(opcao)
          }
        }
      }
    }

  }
}

export class DTOItemPedidoTrocaAPI extends DTOItemPedidoAPI{
  brinde: any;
  cartao: any;
  troca    = true;
  constructor(brindeResgatado: BrindeResgatado, qtde: number) {
    super(null);
    this.id = brindeResgatado.id;
    this.qtde = qtde;
    this.valor = qtde * brindeResgatado.valorEmPontos;
    this.total = this.valor;
    this.brinde = brindeResgatado;
    this.produto = {nome : brindeResgatado.obtenhaNomeBrinde(), preco: 0 }
    this.cartao = { id: brindeResgatado.cartao.id }
    this.nome = brindeResgatado.obtenhaNomeBrinde();
  }

  /*
          qtde: brindeResgatado.qtde,
        produto: { nome: brindeResgatado.brinde.nome, preco: 0},
        brinde: brindeResgatado.brinde ,
        cartao: { id: brindeResgatado.cartao.id }
   */
}
