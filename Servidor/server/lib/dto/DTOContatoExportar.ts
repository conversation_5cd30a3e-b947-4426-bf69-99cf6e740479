import {Contato} from "../../domain/Contato";

import * as moment from "moment";
import {Cartao} from "../../domain/Cartao";
import * as _ from 'underscore';
import {DTOExportarCSV} from "./DTOExportarCSV";


class DTOResumoPlano  extends DTOExportarCSV {
  plano: string;
  pontos = 0;
  pontosAcumulado = 0;
  pontosResgatado = 0;
  qtdeVisitas = 0;
  constructor(cartao: Cartao) {
    super();
    if(cartao){
      this.plano = cartao.plano.nome;
      this.pontos =  cartao.pontos;
      this.pontosAcumulado = cartao.pontosAcumulados  || 0;
      this.pontosResgatado =  cartao.pontosResgatados  || 0;
      this.qtdeVisitas = cartao.qtdeVisitas || 0;

      if(!cartao.acumulaReais()){
        this.pontos = Math.floor(this.pontos);
        this.pontosAcumulado =  Math.floor( this.pontosAcumulado  )
        this.pontosResgatado =  Math.floor(     this.pontosResgatado )
        this.qtdeVisitas = Math.floor(  this.qtdeVisitas )
      }

    }
  }

}

export class DTOContatoExportar  extends DTOExportarCSV {
  private id: number;
  private nome: string;
  private telefone: string;
  private email: string;
  private dataNascimento: string;
  private ultimaVisita: string;
  private sexo: string;
  private status: string;
  private planos: Array<DTOResumoPlano> = [];

  constructor(contato: Contato) {
    super()
    this.id = contato.id;
    this.nome = contato.nome;
    this.telefone = contato.telefone;
    this.sexo = contato.sexo || '';
    this.email = contato.email || '';
    // @ts-ignore
    this.dataNascimento = contato.dataNascimento ? moment(contato.dataNascimento).format('DD/MM/YYYY') : '';
    // @ts-ignore
    this.ultimaVisita = contato.ultimaVisita ? moment(contato.ultimaVisita).format('DD/MM/YYYY') : '';
    this.status = contato.status;
    this.planos = contato.cartoes.map( cartao => new DTOResumoPlano(cartao));
  }

  static listeComPlano(dtos: Array<DTOContatoExportar>): any {
    let planos = dtos.map( dto => dto.planos.map( (plano: any) => plano.plano));
    let listaCsv: any = [];

    planos  = _.uniq( _.flatten(planos) );

    dtos.forEach( (dto: DTOContatoExportar) => {
       let dados = dto.toCSV();

       planos.forEach( nomePlano => {
         let sufixo = (planos.indexOf(nomePlano) + 1).toString();
         let resumoPlano: any =  dto.planos.find((item: any) => item.plano === nomePlano) ;

         if(!resumoPlano) {
           resumoPlano =  new DTOResumoPlano(null);
           resumoPlano.plano = nomePlano;
         }

         Object.assign(dados, resumoPlano.toCSV(  sufixo ))

       })


      listaCsv.push(dados)

    })

    //listaCsv = _.sortBy(listaCsv, obj =>  -Object.keys(obj).length)

    return listaCsv;
  }
}


