
import {PedidoGenerico} from "../../domain/delivery/PedidoGenerico";
import * as moment from "moment";
import {MCITokenizeUtils} from "../integracao/MCITokenizeUtils";
import {Ambiente} from "../../service/Ambiente";

export class DTOTransacaoERede{
  capture = true;
  kind = 'credit'; //debit
  reference = "";
  amount = 0;
  installments: number;
  softDescriptor: string;
  subscription: boolean;
  origin = 1; //e.Rede - 1
  distributorAffiliation: number  //pv
  cardholderName: string;
  cardNumber: string;
  expirationMonth: number
  expirationYear: number;
  securityCode: string;
  storageCard: string;

  billing: any;
  threeDSecure: any;
  urls: any;
  qrCode: any;
  brandTid: string;
  tokenCryptogram: string;
  gerarCriptograma: boolean
  sai: string; // ECI - Obrigatório para as bandeiras Visa e ELO. Opcional em transações card-on-file
  transactionCredentials: any; // {credentialId: string}  //Indica a categoria da transação com credencial armazenada
  constructor(pedido: PedidoGenerico) {
    this.reference = pedido.id.toString();
    this.amount = Number((pedido.obtenhaTotalPagar() * 100).toFixed(0))
  }

  setPagamentoPix(){
    this.kind = 'Pix'
    this.qrCode = {
      dateTimeExpiration: moment().add(60, 'm').format('YYYY-MM-DDTHH:mm:ss')
    }
  }

  setToken(token: string, criptograma: string){
    this.cardNumber = token;
    this.tokenCryptogram = criptograma;
    delete this.gerarCriptograma
  }

  obtenhaDataExpiracao(){
    return this.qrCode ? moment(this.qrCode.dateTimeExpiration, 'YYYY-MM-DDTHH:mm:ss').toDate() : null;
  }


  setPagamentoCartao( dadosCartao: any, enderecoCobranca: any,  codigo: string, descricaoCartao: string ){

    this.softDescriptor = descricaoCartao || 'Meucardapio'
    this.subscription = false;

    if(dadosCartao.token && dadosCartao.token.startsWith(MCITokenizeUtils.prefixo)){
      let card: any = MCITokenizeUtils.decryptCardData(dadosCartao.token, dadosCartao.key);
      this.cardNumber = card.numero;
      this.cardholderName = card.nome.replace(/\s+/, ' ');
      this.securityCode = card.cvv;
      this.expirationMonth = Number(card.mes);
      this.expirationYear =  Number(card.ano);
      if(card.tipo)
        this.kind = card.tipo;
    } else {
      this.cardNumber = dadosCartao.token;
      this.gerarCriptograma = true;
      if(dadosCartao.tipoDoCartao)
        this.kind = dadosCartao.tipoDoCartao.id
      if(dadosCartao.validade){
        this.expirationMonth = Number(moment(dadosCartao.validade).format('M'));
        this.expirationYear = Number(moment(dadosCartao.validade).format('YYYY'));
      }
    }

    if(this.kind === 'credit')
      this.installments =  dadosCartao.parcela ? dadosCartao.parcela : 1;


    this.storageCard  = "0";


    const deviceInfo: any = dadosCartao.deviceInfo;

    console.log(deviceInfo);
    //decline: não prossegue com a transação financeira caso a autenticação falhar
    this.threeDSecure = {
      embedded: true, //3d2rede
      onFailure: 'decline' , //continue: prossegue com a transação financeira mesmo se a autenticação falhar
      userAgent: deviceInfo.userAgent,
      ipAddress: deviceInfo.ip , //Suporta informações somente em iPv4. Exemplo: ********
      device: {
        colorDepth: deviceInfo.colorDepth,
        deviceType3ds: deviceInfo.deviceType3ds,
        javaEnabled: deviceInfo.javaEnabled,
        language: deviceInfo.language,
        screenHeight: deviceInfo.screenHeight,
        screenWidth: deviceInfo.screenWidth,
        timeZoneOffset: deviceInfo.timeZoneOffset
      }
    }

    let address: string =
      String(`${enderecoCobranca.logradouro} ${enderecoCobranca.complemento}`).trim();

    address = `, N: ${address} ${enderecoCobranca.numero || 'SN'}, ${enderecoCobranca.bairro}`

    this.billing = {
      address: address,
      city: enderecoCobranca.cidade.nome,
      postalcode: enderecoCobranca.cep,
      state:  enderecoCobranca.cidade.estado.sigla,
      country: "Brasil",
      emailAddress: enderecoCobranca.email ,
      phoneNumber: enderecoCobranca.telefone
    }

    let host: string = Ambiente.Instance.empresaContexto().obtenhaEnderecoSite(true);

    //testar local eventos com ngrock
    if( !Ambiente.Instance.producao){
      host = 'https://promokit.teste.com.br:8443'
      this.threeDSecure.ipAddress   = '127.0.0.1'
    }

    //tentar seguir sem ipv6 da erro:
    if( this.threeDSecure.ipAddress.length > 20 ) //ipv6
      delete  this.threeDSecure.ipAddress


    this.urls = [
      {
        "kind": "threeDSecureSuccess",
        "url": `${host}/erede/3ds/retorno/sucesso/${codigo}`
      },
      {
        "kind": "threeDSecureFailure",
        "url": `${host}/erede/3ds/retorno/falha/${codigo}`
      }
    ]
  }

  gereCodigoAleatorioPedido() {
    //criar outra transação se nao vai dar erro transaçao existente
    this.reference = `${this.reference}T${new Date().getTime().toString().substring(6)}`.substring(0, 16);
  }
}
