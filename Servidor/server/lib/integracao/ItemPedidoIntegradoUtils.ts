import {ItemPedido} from "../../domain/delivery/ItemPedido";

export interface IOpcaoDoAdicional{
  descricao: string;
  codigoPdv: string;
  qtde: number;
  valor: number;
  total: number;

  // iten.qtde x opcao.qtde
  qtdeSomada: number;
  totalSomado: number;
  opcaoId: number
  adicionalNome: string;
  saborPizza: boolean;
  itemPedido: any
}


//tentar criar um visão unificada de itemPedido , adicionais e sabores de pizzas para facilitar envio plataformas integradas.
export class ItemPedidoIntegradoUtils{
  id: any
  descricao: string;
  qtde  = 0;
  valor  = 0;
  total  = 0;
  obs = '';
  codigoPdv: any
  adicionais: any = [];
  valorProduto = 0;
  valorAdicionais = 0;
  totalDoAdicionais  = 0;
  unidade: string;
  temSabores = false;
  constructor(itemPedido: ItemPedido, multisabor = false, validar = true) {
     if(validar && !itemPedido.produto.codigoPdv)
        if(!itemPedido.produto.temTamanho())
          throw Error (
            String(`Produto "${itemPedido.produto.nome}" não possui código do PDV cadastrado`));


    this.id = itemPedido.id;
    this.codigoPdv = itemPedido.produto.codigoPdv;
    this.descricao =  itemPedido.produto.nome;
    this.qtde = itemPedido.qtde;
    this.obs = itemPedido.observacao || '';
    this.valor = Number(itemPedido.valor.toFixed(2));
    this.total =  Number(itemPedido.total.toFixed(2));
    this.totalDoAdicionais = 0;
    this.unidade = itemPedido.unidadeMedida ? itemPedido.unidadeMedida.sigla.toUpperCase() : 'UN'

    let adicionaisDoItem: Array<any> = itemPedido.obtenhaAdicionaisEnviarComoProduto();
    let adicionaisNormais = adicionaisDoItem.filter( adicional => !adicional.sabor);
    let adicionaisSaboresPizza = adicionaisDoItem.filter( adicional => adicional.sabor);


    let dtoAdicionais: any   = [],   dtoAdicionaisSabores = [], totalEmAdicionais = 0;

    adicionaisNormais.forEach( (itemAdicional: any) => {
      if(itemAdicional.codigoPdv || !validar){
        let dtoItemAdicional = ItemPedidoIntegradoUtils.novoDeAdicional(itemPedido.qtde, itemAdicional);
        totalEmAdicionais +=  dtoItemAdicional.total;
        dtoAdicionais.push(dtoItemAdicional)
      } else {
        throw Error (
          String(`Adicional "${itemAdicional.nome}" do produto "${itemPedido.produto.nome}" não possui código do PDV cadastrado`));
      }
    })

    let totalSabores = adicionaisSaboresPizza.length;

    if(totalSabores > 0){
      if(multisabor || totalSabores > 1){ //  todos sabores entrarão como adicionais.
        this.temSabores = true;
        let itemPizzaTamanhoSabores =
          itemPedido.produtoTamanho.template.deParaTamanhoSabores.find(
            dePara => dePara.qtdeSabores === totalSabores ||  !dePara.qtdeSabores)

        if(itemPizzaTamanhoSabores){
          this.codigoPdv = itemPizzaTamanhoSabores.codigoPdv
        } else if(validar){
          let produtoPizza: any = itemPedido.produto;
          let descricao = String(`Template ${produtoPizza.template.identificador } ${itemPedido.produtoTamanho.template.descricao}`)
          throw Error (
            String(` ${descricao} não possui código do PDV cadastrado para ${totalSabores} sabores`));
        }

        let qtdeMedia   = Number((1 /  totalSabores ).toFixed(2));
        let qtdeDistribuidoSabores = 0 , totalProdutoPizza = itemPedido.valor - totalEmAdicionais;
        this.descricao  = String(`${( itemPedido.produto as any).template.identificador } ${itemPedido.produtoTamanho.template.descricao} ${totalSabores} SABORES`).toUpperCase();
        this.valor = 0;
        this.qtde = itemPedido.qtde;
        this.valorProduto = totalProdutoPizza;

        for(let i = 0; i < totalSabores; i++) {
          let itemAdicional: any = adicionaisSaboresPizza[i];
          let qtdeSabor = qtdeMedia;
          let ultimo: boolean = (i === totalSabores - 1);

          if(ultimo)
            qtdeSabor =  Number((1 - qtdeDistribuidoSabores).toFixed(2));

          if(itemAdicional.codigoPdv || !validar){
            // valor igual para todos sabores, o que muda é qtde
            itemAdicional.preco = Number( totalProdutoPizza.toFixed(2));
            dtoAdicionaisSabores.push(ItemPedidoIntegradoUtils.novoDeAdicional( qtdeSabor, itemAdicional,  true));
            qtdeDistribuidoSabores += qtdeMedia;

          } else {
            let descricaoItem =     String(`Tamanho "${itemAdicional.descricaoTamanho}" do produto "${itemAdicional.nome}" `);

            throw Error (
              String(`${descricaoItem} não possui código do PDV cadastrado`));
          }
        }
      } else {
        if(validar && !itemPedido.produtoTamanho.codigoPdv){
          let descricaoItem =     String(`Tamanho "${itemPedido.produtoTamanho.descricao}" do produto "${itemPedido.produto.nome}" `);

          throw Error (
            String(`${descricaoItem} não possui código do PDV cadastrado`));
        }
        this.descricao = itemPedido.descricao
        this.codigoPdv = itemPedido.produtoTamanho.codigoPdv;
      }
    }

    // add sabores primerio na lista
    for(let i = 0; i < dtoAdicionaisSabores.length; i++){
      let adicional: any = Object.assign({}, dtoAdicionaisSabores[i]);

      this.valorAdicionais +=  adicional.total ; // total aqui representa unitario
      this.totalDoAdicionais += adicional.total;

      this.adicionais.push(adicional)
    }


    for(let i = 0; i < dtoAdicionais.length; i++){
      let adicional: any = Object.assign({}, dtoAdicionais[i]);

      this.valorAdicionais += adicional.valor;
      this.totalDoAdicionais += adicional.total;

      this.adicionais.push(adicional)
    }

    this.totalDoAdicionais = Number(this.totalDoAdicionais.toFixed(2));

    if(!this.valorProduto)
      this.valorProduto =  Number ((this.valor - this.totalDoAdicionais).toFixed(2));

  }

  public static obtenhaOpcoesAdicionaisDoPedido(pedido: any, validar = true){
    //ItemPedidoIntegradoUtils

    let adicionaisPedido: Array<any> = pedido.obtenhaAdicionaisImprimir();

    let itens: Array<IOpcaoDoAdicional> = [];

    adicionaisPedido.forEach( (itemAdicional: any) => {
      if (itemAdicional.codigoPdv || !validar) {
        let dtoItemAdicional = ItemPedidoIntegradoUtils.novoDeAdicional(1, itemAdicional);

        itens.push(dtoItemAdicional)
      } else {
        let descricaoItem = String(`Adicional  "${itemAdicional.nome}" do Pedido`);

        throw Error (
          String(`${descricaoItem} não possui código do PDV cadastrado`));
      }
    })

    return itens;
  }

  private static novoDeAdicional(qtdeItens: number, opcao: any,
                                 saborPizza: boolean = false): IOpcaoDoAdicional {
    let dtoItem: any = {
      codigoPdv:  opcao.codigoPdv,
      descricao: opcao.nome,
      qtde: qtdeItens < 1 ? qtdeItens : opcao.qtde   ,
      qtdeSomada:  opcao.qtde   * qtdeItens ,
      valor: Number(opcao.preco.toFixed(2)),
      saborPizza: saborPizza,
      adicionalNome: opcao.adicional ? opcao.adicional.nome : null
    }

    if(!saborPizza) dtoItem.opcaoId = opcao.id;

    dtoItem.total = Number((dtoItem.valor * dtoItem.qtde).toFixed(2));
    dtoItem.totalSomado = Number((dtoItem.valor * dtoItem.qtdeSomada).toFixed(2));

    return dtoItem;
  }
}

//todo: refator ItemPedidoIntegradoUtils e ProdutoExtraPedidoIntegrado virar herença
//todo: cria metodo static que passa pedido e retorna lista com dois tipos ja em lista
export class ProdutoExtraPedidoIntegrado   {
  id: any
  descricao: string;
  qtde  = 0;
  valor  = 0;
  total  = 0;
  obs = '';
  codigoPdv: any
  adicionais: any = [];
  valorProduto = 0;
  valorAdicionais = 0;
  totalDoAdicionais  = 0;
  unidade: string;
  temSabores = false;
  constructor(itemDoAdicional: any) {
    //todo: algum caso quando vem da tela nao vem prop adicional
    if(itemDoAdicional.adicional){
      this.id = itemDoAdicional.adicional.id;
      this.descricao = `${itemDoAdicional.adicional.nome} - ${itemDoAdicional.nome}`;
    } else {
      this.id = itemDoAdicional.id
      this.descricao = `Extra Pedido - ${itemDoAdicional.nome}`;
    }

    this.codigoPdv = itemDoAdicional.codigoPdv;
    this.qtde = itemDoAdicional.qtde;
    this.valorProduto =  this.valor = itemDoAdicional.preco;
    this.total = Number((this.qtde * this.valor).toFixed(2));
  }

  static gereItensPedido(pedido: any){
    let itens: Array<ProdutoExtraPedidoIntegrado> = [];

    let opcoesAdicionais: Array<any> = pedido.obtenhaAdicionaisImprimir();

    opcoesAdicionais.forEach( (itemDoAdicional: any) => {
      if (itemDoAdicional.codigoPdv ) {
        let dtoItemAdicional: any =   new ProdutoExtraPedidoIntegrado(itemDoAdicional)

        itens.push(dtoItemAdicional)
      } else {
        let descricaoItem = String(`Adicional  "${itemDoAdicional.nome}" do Pedido`);

        throw Error (
          String(`${descricaoItem} não possui código do PDV cadastrado`));
      }
    })

    return itens

  }
}
