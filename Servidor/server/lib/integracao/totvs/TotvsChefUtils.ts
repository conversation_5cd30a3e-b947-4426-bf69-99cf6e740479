import {Produto} from "../../../domain/Produto";
import {EnumDisponibilidadeProduto} from "../../emun/EnumDisponibilidadeProduto";
import {Categoria} from "../../../domain/delivery/Categoria";
import {AdicionalDeProdutoEscolhaSimples} from "../../../domain/delivery/AdicionalDeProdutoEscolhaSimples";
import {AdicionalDeProdutoMultiplaEscolha} from "../../../domain/delivery/AdicionalDeProdutoMultiplaEscolha";
import {OpcaoDeAdicionalDeProduto} from "../../../domain/delivery/OpcaoDeAdicionalDeProduto";

export class TotvsChefUtils{

  static convertaParaProdutos(produtosTotvs: any) {
    let produtosDelivery: any = [], adicionais: any = [], produtosAdicionais: any = [], composicoes: any = []

    if(produtosTotvs){
      //let emPromocao = produtosTotvs.filter((item: any) => item.EmPromocao);
      produtosTotvs.forEach( (produtoTotvs: any) => {
        let produto: Produto =
          new Produto(null, produtoTotvs.Descricao,  produtoTotvs.PrecoVenda);

        if(produtosTotvs.PrecoPromocional)
          produto.setPrecoPromocional(produtosTotvs.PrecoPromocional)

        produto.codigoPdv = produtoTotvs.Codigo;
        produto.disponibilidade =  EnumDisponibilidadeProduto.SempreDisponivel;

        if(produtoTotvs.BaixarEstoqueOnline){
          produto.disponibilidade =  produtoTotvs.QuantidadeEstoque >= 0 ?
            EnumDisponibilidadeProduto.SempreDisponivel :  EnumDisponibilidadeProduto.NaoDisponivel
        }

        let categoria =
          new Categoria(null, produtoTotvs.Subgrupo, null, null, produtoTotvs.CodigoSubgrupo);

        produto.categoria = categoria;

        if(produtoTotvs.ProdutosAdicionais && produtoTotvs.ProdutosAdicionais.length)
          adicionais.push({ produto: produto,
            max: produtoTotvs.AdicionaisQuantidadeMaxima,
            min: produtoTotvs.AdicionaisQuantidadeMinima,
            opcoes: produtoTotvs.ProdutosAdicionais});

        let temComposicoes =  produtoTotvs.Composicoes && produtoTotvs.Composicoes.length

        if(!temComposicoes){

          if(produtoTotvs.ExibirComoAdicional)
            produtosAdicionais.push(produto)
          else
            produtosDelivery.push(produto)
        } else {
          composicoes.push({
            produto: produto,
            opcoes: produtoTotvs.Composicoes
          })

        }
      });
    }

    composicoes.forEach((composicao: any) => {

    })

    adicionais.forEach((adicional: any) => {
       let obrigatorio = adicional.min > 0,
           nomeAdicional = 'Selecione um adicional';
       let adicionalProduto: any;

       if(adicional.max === 1){
         adicionalProduto =
           new AdicionalDeProdutoEscolhaSimples(nomeAdicional, obrigatorio , [])

       } else{
         nomeAdicional = 'Selecione os adicionais';
         adicionalProduto =
           new AdicionalDeProdutoMultiplaEscolha(nomeAdicional, obrigatorio , [],
             adicional.min, adicional.max || 10, true);

       }

      adicional.opcoes.forEach((idProduto: any) => {
          let produto = produtosAdicionais.find((item: any) => item.codigoPdv === idProduto);

          if(!produto)
               produto = produtosDelivery.find((item: any) => item.codigoPdv === idProduto);

          if(produto){
            let opcaoDeAdicional: any =
              new OpcaoDeAdicionalDeProduto(produto.nome, produto.obtenhaPreco(),
                produto.disponibilidade ===  EnumDisponibilidadeProduto.SempreDisponivel, produto.descricao);

            opcaoDeAdicional.codigoPdv = produto.codigoPdv;

            adicionalProduto.opcoesDisponiveis.push(opcaoDeAdicional);

          } else {
            console.log(String(`Produto nao encontrado: ${idProduto}`))
          }
      })

      if( adicionalProduto.opcoesDisponiveis.length){
        adicionalProduto.ordem = adicional.produto.camposAdicionais.length + 1;
        adicional.produto.camposAdicionais.push(adicionalProduto)
      } else {
        console.log(String(`Nenhuma opção de adicional  do produto "${adicional.produto.nome}" encontrado: `))
      }
    })

    return produtosDelivery;
  }
}
