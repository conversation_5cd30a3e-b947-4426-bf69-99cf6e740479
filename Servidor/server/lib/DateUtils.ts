let moment = require('moment');
const horaFormato = 'YYYYMMDDHHmmss';
const UTCSERVER  = -3;
export class DateUtils {
  static  agoraNofuso(fusoHorario: number){
    if(fusoHorario === UTCSERVER) return new Date();

    let agoraUTC0 = moment( moment.utc().format(horaFormato), horaFormato)

    return  agoraUTC0.add(fusoHorario, 'h').toDate();
  }
  static obtenhaVencimentoDiaUtil(){
    let  vencimento = moment();

    vencimento.add(1, 'd');  // vencimento ser amanha

    DateUtils.garantaVencimentoSexaASabado(vencimento)

    return vencimento;

  }

  static garantaVencimentoSexaASabado(vencimento: any){
    if(vencimento.day() === 6){ //sabado
      vencimento.add(2, 'days');
    } else     if(vencimento.day() === 0) { //domingo
      vencimento.add(1, 'days');
    }
  }

  static obtenhaProximoVencimento(diaVencimento: number){
    let  vencimento = moment();

    vencimento.date(diaVencimento);

    if(vencimento.isBefore(moment()))
      vencimento.add(1, 'month');

    return vencimento;
  }

  static vencimentoBoletoPagSeguro(){
    let data = moment(),
      diaSemana = data.day();

    data.add(3, 'd'); //pagseguro boleto são emitos pra 3 dias

    if(diaSemana !== 1 && diaSemana !== 2) //nao é seg ou terça.
      data.add(2, 'd') //add 2 dias (sab,dom)

    console.log('vencimento: ' + data.toDate());
    return data.toDate();
  }

  static amanha() {
    let data = moment();

    data.add(1, 'd');

    return data.toDate();
  }

  static strToDate(str: string ) {
    return moment(str).toDate();
  }

  static dateToStr(date: Date) {
    return moment(date).format('YYYY-MM-DD')
  }

  static diasParaData(data: Date){
    return  moment(data).startOf("d").diff(moment().startOf('d'), 'd');
  }

  static utcServer(){
    return UTCSERVER;
  }
}
