import * as path from "path";
const fs = require('fs');
const util = require('util');


export class ErroCartao {
  constructor(public codigo: string, public definicao: string,
              public significado: string,  public acao: string) { }

  obtehaMensagemErro() {
    return  this.definicao;
  }
}

export class ErrosCartaoUtils {
  static erros: any =  []

  static carregueErros(){
    try{
      let arquivo: string = path.join(__dirname, '..', 'public', 'erros-cartao-lr.csv');
      let data: any = fs.readFileSync(arquivo);
      data.toString().split('\n').forEach( (linha: string) => {
        let colunas = linha.split(',');
        let codigoErro = colunas[0].toString().padStart(2, '0');
        ErrosCartaoUtils.erros.push(new ErroCartao(codigoErro , colunas[1], colunas[2], colunas[3]))
      })

      console.log('total erros carregados:' +     ErrosCartaoUtils.erros.length)


    }catch (e) {
      console.log('Arquivo de erros com  codigos LR nao carregados')
      console.log(e.message)
    }

  }

  static obtenhaErro(codigoLR: string){
    if(!ErrosCartaoUtils.erros.length)
      ErrosCartaoUtils.carregueErros();

    if(!codigoLR) return null;

    return ErrosCartaoUtils.erros.find( (erro: ErroCartao) => erro.codigo  === codigoLR)
  }

}




