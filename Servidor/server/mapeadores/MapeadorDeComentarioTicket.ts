import { MapeadorBasico } from "./MapeadorBasico";

export class MapeadorDeComentarioTicket extends MapeadorBasico {
  constructor() {
    super('comentario_ticket');
  }

  async listePorTicket(ticketId: number): Promise<any[]> {
    return this.listeAsync({
      ticket: ticketId
    });
  }

  async listePublicosPorTicket(ticketId: number): Promise<any[]> {
    return this.listeAsync({
      ticket: ticketId,
      interno: false
    });
  }
}
