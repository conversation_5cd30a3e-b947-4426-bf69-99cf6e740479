import {Router} from 'express';
import {Resposta} from "../utils/Resposta";
import {QrCodeService} from "../service/QrCodeService";
import {QrCode} from "../domain/delivery/QrCode";

const router: Router = Router();

router.get('/:id', async (req, res) => {
  const id = req.params.id;

  try {
    const conditions = { id: id };
    const qrCodeService = new QrCodeService();
    const qrCode = await qrCodeService.selecioneSync(conditions);

    if (!qrCode) {
      return res.status(404).json(Resposta.erro('QR Code não encontrado.'));
    }

    res.json(Resposta.sucesso(qrCode));
  } catch (error) {
    res.status(400).json(Resposta.erro(error.message));
  }
});


router.get('/', async (req: any, res: any) => {
  let query: any = {};
  let nome = req.query.nome;

  if (nome) query.nome = '%' + nome + '%';

  const qrCodeService = new QrCodeService();
  let qrcodes: any[] = await qrCodeService.listeAsync(query);

  res.json(Resposta.sucesso(qrcodes));
});

// POST
router.post('/', async (req: any, res: any) => {
  const dados = req.body;
  const qrCode = new QrCode();
  Object.assign(qrCode, dados);

  const qrCodeService = new QrCodeService();

  try {
    const qrCodeInserido = await qrCodeService.insiraGraph(qrCode);
    res.json(Resposta.sucesso(qrCodeInserido));
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
});

// PUT
router.put('/', async (req: any, res: any) => {
  const dados = req.body;

  if (!dados.id) return res.json(Resposta.erro('É necessário informar o id do QR Code que será atualizado.'));

  const qrCodeService = new QrCodeService();
  let qrCode = await qrCodeService.selecioneSync({id: dados.id});

  Object.assign(qrCode, dados);
  try {
    const qrCodeInserido = await qrCodeService.atualizeSync(qrCode);
    res.json(Resposta.sucesso(qrCodeInserido));
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
});

router.delete('/:id', async (req: any, res: any) => {
  let id = req.params.id;

  if (!id) return res.json(Resposta.erro('É necessário informar o id do QR Code a ser removido.'));

  let qrCode: QrCode = new QrCode();
  qrCode.id = id;

  try {
    const qrCodeService = new QrCodeService();
    await qrCodeService.removaAsync(qrCode);
    return res.json(Resposta.sucesso());
  } catch (reason) {
    let mensagem = "Não foi possível remover o QR Code.";

    if (reason) mensagem += ' ' + reason;

    return res.json(Resposta.erro(mensagem));
  }
});

export const CadQrCodeController: Router = router;
