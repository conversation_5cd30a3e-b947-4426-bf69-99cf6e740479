import {Router} from 'express';
import {MapeadorDeBrinde} from '../mapeadores/MapeadorDeBrinde';
import {MapeadorDeCartao} from "../mapeadores/MapeadorDeCartao";
import {Resposta} from "../utils/Resposta";
import {Cartao} from "../domain/Cartao";
import {Brinde} from "../domain/obj/Brinde";
import {EmpresaService} from "../service/EmpresaService";
import {CartaoService} from "../service/CartaoService";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {MapeadorDeBrindeResgatado} from "../mapeadores/MapeadorDeBrindeResgatado";
import {DTOBrindeResgatado} from "../lib/dto/DTOBrindeResgatado";
import {FidelidadeService} from "../service/FidelidadeService";
import {RotaGuard} from "../lib/permissao/RotaGuard";

const router: Router = Router();

router.get('/:plano', async (req, res) => {
  const mapeadorDeBrinde = new MapeadorDeBrinde();

  mapeadorDeBrinde.listeAsync({idPlano: Number(req.params.plano)}).then( (brindes: any) => {
    res.json(Resposta.sucesso(brindes));
  });
});

router.post('/',  RotaGuard.alterarLoja, async (req, res) => {
  const dados: any = req.body;

  let plano = await new MapeadorDePlano().selecioneSync( {id: dados.plano.id, idEmpresa: dados.plano.empresa.id });

  if(!plano) return res.json(Resposta.erro('Nenhum encontrado.'))

  plano.empresa = dados.plano.empresa;

  let brinde = new Brinde(dados.id, dados.nome, dados.linkImagem, dados.valorEmPontos, plano, dados.artigo);

  new FidelidadeService().salveBrinde(brinde).then( (erro => {
       if(!erro){
         res.json(Resposta.sucesso({id: brinde.id}))
       }else{
         res.json(Resposta.erro(erro))
       }
  }))

});

router.put('/', RotaGuard.alterarLoja, async (req, res) => {
  const dados: any = req.body;

  let plano = await new MapeadorDePlano().selecioneSync( {id: dados.plano.id, idEmpresa: dados.plano.empresa.id });

  if(!plano) return res.json(Resposta.erro('Nenhum encontrado.'))

  plano.empresa = dados.plano.empresa;

  let brinde = new Brinde(dados.id, dados.nome, dados.linkImagem, dados.valorEmPontos, plano, dados.artigo);

  new FidelidadeService().salveBrinde(brinde).then( (erro => {
    if(!erro){
      res.json(Resposta.sucesso( ))
    }else{
      res.json(Resposta.erro(erro))
    }
  }))

});


router.post('/trocarPontos', RotaGuard.trocarBrindeFidelidade, async (req: any, res: any) => {
  const parametros: any = req.body;
  const operador = req.user;
  const idCartao = Number(parametros.ict);
  const idBrinde = Number(parametros.idb);
  const saldoUtilizar = Number(parametros.sau);
  const mensagem = parametros.msg;
  const mapeadorDeCartao = new MapeadorDeCartao();
  const cartao: Cartao = await mapeadorDeCartao.selecioneSync({id: idCartao});

  if ( !cartao )
    return res.json(Resposta.erro('Cartão não encontrado'));

  if( !operador)
    return res.json(Resposta.erro('Faça login para realizar a troca'));


  const mapeadorDeBrinde = new MapeadorDeBrinde(),
        brinde = await mapeadorDeBrinde.selecioneSync({id: idBrinde}),
        cartaoService: CartaoService = new CartaoService();

  cartaoService.troquePontos(operador, cartao, brinde, mensagem, saldoUtilizar).then( (brindeResgatado: any) => {
    res.json(Resposta.sucesso({ novaPontuacao: cartao.pontos, codigoTroca: brindeResgatado.codigo } ))
  }).catch( (erro: any) => {
    res.json(Resposta.erro(erro));
  });
});


router.get('/comprovante/:codigo', async (req, res) => {
   let codigo = req.params.codigo;

   let brindeResgatado = await new MapeadorDeBrindeResgatado().selecioneSync({ codigo: codigo});

   res.json(Resposta.sucesso(new DTOBrindeResgatado(brindeResgatado)))
});

router.delete('/:id', RotaGuard.alterarLoja, async(req: any, res) => {
  let id = req.params.id;

  if(!id)
    return res.json(Resposta.erro('É necessário informar o id do brinde a ser removido'));

  let brinde = {id: id, empresa: req.empresa};

  await new FidelidadeService().removaBrinde(brinde)

  return res.json(Resposta.sucesso());

});

export const BrindesController: Router = router;
