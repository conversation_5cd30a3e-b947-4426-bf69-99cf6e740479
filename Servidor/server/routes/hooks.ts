import {Router} from "express";
import {NotificacaoPedidoFoodyDelivery} from "../domain/integracoes/NotificacaoPedidoFoodyDelivery";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {NotificacaoPedidoService} from "../service/NotificacaoPedidoService";
import {MapeadorDeNotificacaoPedido} from "../mapeadores/MapeadorDeNotificacaoPedido";
import {Resposta} from "../utils/Resposta";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {MercadoPagoService} from "../service/meiospagamentos/MercadoPagoService";
import {PedidoService} from "../service/PedidoService";
import {StatusPagamentoDeParaMercadoPago} from "../lib/emun/EnumStatusPagamento";
import {MapeadorDeFormaDePagamento} from "../mapeadores/MapeadorDeFormaDePagamento";
import {MapeadorDePagamentoPedido} from "../mapeadores/MapeadorDePagamentoPedido";
import {NotificacaoPedidoEcletica} from "../domain/integracoes/NotificacaoPedidoEcletica";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {NotificacaoMeioPagamentoService} from "../service/NotificacaoMeioPagamentoService";
import {NotificacaoPedidoBluesoft} from "../domain/integracoes/NotificacaoPedidoBluesoft";
import {NotificacaoPedidoSaipos} from "../domain/integracoes/NotificacaoPedidoSaipos";
import {NotificacaoVenda} from "../domain/integracoes/NotificacaoVenda";
import {NotificacaoVendaService} from "../service/NotificacaoVendaService";
import {Empresa} from "../domain/Empresa";
import {NotificacaoDeliveryFoodydelivery} from "../domain/integracoes/NotificacaoDeliveryFoodydelivery";
import {NotificacaoDeliveryService} from "../service/NotificacaoDeliveryService";
import {NotificacaoMesa} from "../domain/integracoes/NotificacaoMesa";
import {NotificacaoMesaService} from "../service/NotificacaoMesaService";
import {Comanda} from "../domain/comandas/Comanda";
import {ERedeItauApi} from "../lib/ERedeItauApi";
import {NotificacaoMesaTransferida} from "../domain/integracoes/NotificacaoMesaTransferida";
import {NotificacaoMesaCancelada} from "../domain/integracoes/NotificacaoMesaCancelada";
import {NotificacaoMesaItemRemovido} from "../domain/integracoes/NotificacaoMesaItemRemovido";
const router: Router = Router();


function retorneErro400(req: any, res: any, msg: string){
  let empresa = req.empresa ? String(`${req.empresa.id} - ${req.empresa.nome}`) : ''
  console.error(String(`Falha ao chamar "${req.headers.host} em ${req.originalUrl}", empresa "${empresa}"`));
  console.error(msg);

  res.status(400).json(msg)
}

router.post('/mercadopago/pagamentos', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou mercadopago');
  console.log(dados);
  let id = dados.id,
    horario = new Date(dados.date_created),
    tipo = dados.type, //payment, plan, subscription, invoice
    acao = dados.action, //payment.created
    codigo = dados.data ? dados.data.id : dados['data[id]'];

  if(!codigo) return retorneErro400(req, res, 'Parametros inválidos') ;

  let pagamentoPedido = await new MapeadorDePagamentoPedido().selecioneSync( { codigoTransacao: codigo});
  if( !pagamentoPedido ) {
    console.log('Pagamento mercadopago não encontrado: ' + codigo);
    return  retorneErro400(req, res, 'Pagamento não encontrado: ' + codigo);
  }

  let formaPagamento: any = pagamentoPedido.formaDePagamento;
  //let formaPagamento: any =  req.empresa.obtenhaFormaPagamentoMercadoPago();
  //pegar com tokens

  formaPagamento = await new MapeadorDeFormaDePagamento().selecioneSync(formaPagamento.id);
  let payment = await new MercadoPagoService(formaPagamento.configMeioDePagamento.token).obtenhaPagamento(codigo).catch((err) => {
    console.log(err)
    retorneErro400(req, res, err);
  })

  if(payment){
    if(acao === 'payment.updated' || acao === 'payment.created'){
      ExecutorAsync.execute( async (cb: Function) => {
        let empresa = await new MapeadorDeEmpresa().selecioneSync(pagamentoPedido.pedido.empresa.id)
        require('domain').active.contexto.idEmpresa =  empresa.id;
        require('domain').active.contexto.empresa =  empresa;
        let novoStatus = StatusPagamentoDeParaMercadoPago.get(payment.status);

        pagamentoPedido.pedido.empresa = empresa;
        await new PedidoService().mudouStatusPagamento(pagamentoPedido.pedido, pagamentoPedido, novoStatus);
        await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoPedido, empresa)
        cb();
      }, () => {}, 500)

      //let notificacao: any = new NotificacaoMercadoPago(id, tipo, acao, horario, payment);
      // await  notificacao.salve();
      console.log('Respondeu notificação: ' + id )
      res.json({ success: true })
    }
  } else {
    res.status(400);
    res.json(new Resposta(false));
  }

})

//{  event: 'CONFIRMED',  cod_store: '123',   order_id: '121312131212'  }
//Pedido Confirmado: CONFIRMED;
//Pedido Aguardando Entrega: READY_TO_DELIVER;
//Pedido Saiu para Entrega: DISPATCHED;
//Pedido Finalizado/Entregue: CONCLUDED;
//Pedido Cancelado: CANCELLED
//https://saipos-docs-order-api.readme.io/reference/eventos-por-tipo-de-venda
//https://fibo.promokit.com.br/hooks/saipos/pedido
router.post('/saipos/pedido', async(req: any, res) => {
  let body: any = req.body;
  console.log('notificou saipos');
  console.log(body);

  let idEmpresa = body.cod_store,
      status = body.event,
    orderId = body.order_id;

  if(!idEmpresa || !status)
    return  retorneErro400(req, res, 'Parametros inválidos');

  let mapeador = new MapeadorDePedido();

  mapeador.desativeMultiCliente();

  let pedido =
    await new MapeadorDePedido().selecioneSync({ id: orderId, idEmpresa: idEmpresa });

  if(pedido){
    let dados: any = {status: status, empresa: idEmpresa, orderId: orderId};

    let notificacaoPedido = new NotificacaoPedidoSaipos(pedido, dados)
    await notificacaoPedido.salve();

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoPedidoService.executeSaipos(notificacaoPedido)
      cbAsync();
    }, () => {});

    res.json({ success: true })
  } else {
    return  retorneErro400(req, res, String(`Pedido "${orderId}" naõ econtrado na empresa "${idEmpresa}"`));
  }
})

//https://soneda.promokit.com.br/hooks/bluesoft/cargaprecos
router.post('/bluesoft/cargaprecos', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou  bluesoft precos');
  console.log(dados);
  res.json({ success: true })
})

//:rede
router.post('/bluesoft/pedido/:rede', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou  bluesoft pedido rede');
  console.log(dados);
  let rede = req.params.rede ;

  if(!dados.pedidoVendaKey)
    return res.status(400).json('Parametros inválidos');

  let pedido = await new MapeadorDePedido().selecioneSync({ referenciaExterna: dados.pedidoVendaKey, rede: rede})

  if(pedido){
    let notificacaoPedido = new NotificacaoPedidoBluesoft(pedido, dados)
    await notificacaoPedido.salve();

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoPedidoService.executeBlueSoft(notificacaoPedido)
      cbAsync();
    }, () => {});
  }else{
    //bluesoft não distingue de onde sao criados os pedidos
    console.log('Ignorar notificação, pedido não existe na nosssa base')
  }

  res.json({ success: true })
})

router.post('/bluesoft/pedido', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou  bluesoft pedido');
  console.log(dados);
  let rede = req.params.rede || 'soneda';

  if(!dados.pedidoVendaKey)
    return res.status(400).json('Parametros inválidos');

  let pedido = await new MapeadorDePedido().selecioneSync({ referenciaExterna: dados.pedidoVendaKey, rede: rede})

  if(pedido){
    let notificacaoPedido = new NotificacaoPedidoBluesoft(pedido, dados)
    await notificacaoPedido.salve();

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoPedidoService.executeBlueSoft(notificacaoPedido)
      cbAsync();
    }, () => {});
  }else{
    //bluesoft não distingue de onde sao criados os pedidos
    console.log('Ignorar notificação, pedido não existe na nosssa base')
  }

  res.json({ success: true })
})

//https://promokit.zapkit.com.br/hooks/foodydelivery/order
router.post('/foodydelivery/order', async(req: any, res) => {
  let dados = req.body;
  let integracaoFoody: any = req.empresa.integracaoFoodyDelivery;
  console.log('notificou  foodydelivery');
  console.log(dados);

  if(!integracaoFoody)
    return res.status(400).json({ erro: 'Nenhuma integração ativa'})

  if(req.empresa){
    console.log('#empresa da req: ' + req.empresa.nome)
  } else {
    console.log("não carregou tem empresa na req")
  }

  if(dados.event && dados.event !== 'hook_test'){
    let pedido = await new MapeadorDePedido().selecioneSync({ codigoExterno: dados.uid})
    if(pedido){
      let order: any = await integracaoFoody.obtenhaService().obtenhaPedido(dados.uid).catch((err: any) => {
        console.error(err)
      })

      if(order){
        let notificacao  = new NotificacaoDeliveryFoodydelivery(pedido, order);
        await notificacao.salve();
        ExecutorAsync.execute( async (cbAsync: any) => {
          await NotificacaoDeliveryService.executeNotificacao(notificacao, pedido)
          cbAsync();
        }, () => {});
      } else {
        let erro =  'Não foi possivel consulta order: ' + dados.uid;
        console.log(erro)
        return res.status(400).json({ erro: erro})
      }
    }else{
      //foody delivery não distinge de onde sao criados os pedidos
      console.log('Ignorar notificação, pedido não existe na nosssa base: ' + dados.uid)
    }
  }

  res.json({ success: true })
})

//POST 	https://<dominio>.promokit.com.br/hooks/ecletica/venda/estorne
router.post('/ecletica/venda/estorne', async(req: any, res) => {
  let dados: any = req.body;
  let contentType: string =  req.headers['content-type'];
  console.log('notificou  ecletica venda');
  console.log('content-type: ' + contentType)
  console.log(dados);
  let notificacaoVenda: NotificacaoVenda;
  let empresa: any = req.empresa;

  if( contentType !== 'application/json')
    return   retorneErro400( req, res, String(`content-type não esperado: "${contentType}"`));


  if(dados.PostEstorno){
    let dadosEstorno: {
      NumeroPedido: string, LojaID: string
      RedeID: string
    } = dados.PostEstorno.Estorno;

    notificacaoVenda = new NotificacaoVenda('ecletica',  dadosEstorno.RedeID, dadosEstorno.LojaID,
      dadosEstorno.NumeroPedido, 0, dadosEstorno);

    notificacaoVenda.estorno = true;

    if(empresa.ehDaRedeEcletica(notificacaoVenda.rede, notificacaoVenda.loja)){
      notificacaoVenda.empresa = empresa;
      await notificacaoVenda.salve();
      NotificacaoVendaService.executeEcletica(notificacaoVenda).then((pontuacaoRegistrada: any) => {
        let resposta: any = {
          "codigo": 0,
          "mensagem": "Operação efetuada com sucesso",
          "ticket": pontuacaoRegistrada.codigo
        }
        console.log(resposta)
        res.json( resposta)
      }).catch((erro: any) => {
        retorneErro400( req, res, erro);
      })

    } else {
      retorneErro400( req, res, String(`Empresa "${empresa.nome}" nao faz parte da rede "${notificacaoVenda.rede}" e loja "${notificacaoVenda.loja}"`));
    }
  } else {
    retorneErro400( req, res, 'Parametros inválidos');
  }
})

//POST 	https://<dominio>.promokit.com.br/hooks/ecletica/venda
router.post('/ecletica/venda', async(req: any, res) => {
  let dados: any = req.body;
  let contentType: string =  req.headers['content-type'];
  console.log('notificou ecletica venda');
  console.log('content-type: ' + contentType)
  console.log(dados);
  let notificacaoVenda: NotificacaoVenda;
  let empresa: any = req.empresa;

  if( contentType !== 'application/json')
    return   retorneErro400( req, res, String(`content-type não esperado: "${contentType}"`));


  if(dados.PostOperacao){
    // "TipoOperacao": "C",   "TipoOperacao": "D"
    let dadosVenda: {
      LojaID: string, RedeID: string,
      NumeroPedido: string, ValorPedido: number, Frete: number, Desconto: number,
      ClienteCPF: string, ClienteNome: string, ClienteEmail: string
      Data: string, OrigemVenda: string, pinNumber: number,
      OperacaoItem: Array<{
        ProdutoNome: string, ProdutoSKU: string, ValorEmPontos: number, ValorEmReais: number
        Quantidade: number, Desconto: number, TipoOperacao: string
      }>
    } = dados.PostOperacao.Operacao;

    let resposta: any = {
      "codigo": 0,
      "mensagem": "Operação efetuada com sucesso",
      "ticket": ""
    }

    if(dadosVenda.OrigemVenda.indexOf('MEU CARDAPIO AI') >= 0 ){
      console.log('Ignorar origem de vendas MeuCardapio.');
      console.log(resposta);
      res.json( resposta);
      return;
    }

    notificacaoVenda = new NotificacaoVenda('ecletica',  dadosVenda.RedeID, dadosVenda.LojaID,
      dadosVenda.NumeroPedido, dadosVenda.ValorPedido, dadosVenda);

    if(empresa.ehDaRedeEcletica(notificacaoVenda.rede, notificacaoVenda.loja)){
      notificacaoVenda.empresa = empresa;
      await notificacaoVenda.salve();

      NotificacaoVendaService.executeEcletica(notificacaoVenda).then((pontuacaoRegistrada: any) => {
        resposta.ticket = pontuacaoRegistrada.codigo;
        console.log(resposta)
        res.json( resposta)
      }).catch((erro: any) => {
        retorneErro400( req, res, erro);
      })

    } else {
      retorneErro400( req, res, String(`Empresa "${empresa.nome}" nao faz parte da rede "${notificacaoVenda.rede}" e loja "${notificacaoVenda.loja}"`));
    }
  } else {
    retorneErro400( req, res, 'Parametros inválidos');
  }
})

//https://fibo.promokit.com.br/hooks/teknisa/pedido
router.post('/teknisa/pedido', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou teknisa');
  console.log(dados);

  res.status(400).json('Nenhum pedido encontrado');
})

//https://fibo.promokit.com.br/hooks/ecletica/conta/transferiu
router.post('/ecletica/conta/transferiu', async(req: any, res) => {
  const { operacao, rede, loja, de, para }  = req.body;
  console.log('notificou ecletica transferiu conta');
  let inicio = new Date().getTime();
  console.log( req.body);

  if(!rede || !loja)  return  res.status(400).json('Rede ou Loja não informado')
  if(!de  )  return  res.status(400).json('Mesa/Comanda origem nao informada')
  if(!para  )  return  res.status(400).json('Mesa/Comanda destino nao informada')

  let empresas: any = await new MapeadorDeEmpresa().listeAsync({rede: rede , loja: loja});

  if(!empresas.length)
    return res.status(400).json( String(`Empresa não encontrada "rede: ${ rede}  loja: ${ loja}"`));

  if(empresas.length > 1)
    return res.status(400).json(String(`Mais de uma empresa vinculada a essa Loja: "rede: ${ rede}  loja: ${ loja}"`));

  let numeroOrigem = de.numero;
  let numeroPara = para.numero;

  let notificacao = new NotificacaoMesaTransferida(empresas[0], numeroOrigem,  operacao, req.body );

  let retorno: any = { sucesso: true }

  let comanda: Comanda = await notificacao.obtenhaComanda().catch(async (err) => {
    console.error(err)
    retorno.mensagem = err.message || err;
  });


  if(comanda){

    notificacao.comanda = comanda;

    retorno.ticket = notificacao.id;

    let erro  = await notificacao.valide();

    if(erro) return res.status(400).json(erro);

    await notificacao.salve( false);

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMesaService.executeTrocaDaMesa(notificacao);
      cbAsync();
    }, (erroAsync: any) => { console.log(erroAsync)}, 100);

    console.log(String(`Notificação tranferiu conta ${operacao} - ${numeroOrigem}
    para ${numeroPara}" processada com sucesso: ${notificacao.id}, tempo:   ${new Date().getTime() - inicio}`))

    retorno.mensagem =   'Notificação processada com sucesso!'

    console.log(retorno)
    res.json(retorno);
  } else {
    if(!retorno.mensagem){
      retorno.mensagem = 'Mesa não possui nenhuma comanda aberta no momento';
      console.log(retorno)
      res.status(404).json(  retorno.mensagem);
    } else {
      console.log(retorno)
      res.status(400).json(  retorno.mensagem);
    }
  }

})


//https://fibo.promokit.com.br/hooks/ecletica/fecheConta
router.post('/ecletica/fecheConta', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou ecletica fechar conta');
  console.log(dados);
  let inicio = new Date().getTime();

  let numero = dados.numero, operacao = dados.tipoOperacao, rede = dados.rede, loja = dados.loja;

  if(!rede || !loja)
    return  res.status(400).json('Rede ou Loja não informado')

  let empresas: any = await new MapeadorDeEmpresa().listeAsync({rede: rede , loja: loja});
  console.log('tempo pegar empresa:', (new Date().getTime() - inicio))
  if(!empresas.length)
    return res.status(400).json( String(`Empresa não encontrada "rede: ${ dados.rede}  loja: ${ dados.loja}"`));

  if(empresas.length > 1)
    return res.status(400).json(String(`Mais de uma empresa vinculada a essa Loja: "rede: ${ dados.rede}  loja: ${ dados.loja}"`));

  let notificacao = new NotificacaoMesa(empresas[0], numero, operacao, dados);
  let retorno: any = { sucesso: true }

  let comanda: Comanda = await notificacao.obtenhaComanda().catch(async (err) => {
    console.error(err)
    retorno.mensagem = err.message || err;
  });

  if(comanda){
    console.log('tempo pegar comanda:', (new Date().getTime() - inicio))
    notificacao.comanda = comanda;

    retorno.ticket = notificacao.id;

    let erro  = await notificacao.valide();

    if(erro) return res.status(400).json(erro);

    await notificacao.salve( true);

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMesaService.executeFecharConta(notificacao);
      cbAsync();
    }, (erroAsync: any) => { console.log(erroAsync)}, 100);

    console.log(String(`Notificação feche conta ${operacao} - ${numero}" processada com sucesso: ${notificacao.id}, tempo:   ${new Date().getTime() - inicio}`))

    retorno.mensagem =   'Notificação processada com sucesso!'

    console.log(retorno)
    res.json(retorno);
  } else {
    if(!retorno.mensagem){
      retorno.mensagem = 'Mesa não possui nenhuma comanda aberta no momento';
      console.log(retorno)
      res.status(404).json(  retorno.mensagem);
    } else {
      console.log(retorno)
      res.status(400).json(  retorno.mensagem);
    }
  }
})

//https://fibo.promokit.com.br/hooks/ecletica/canceleItem
router.post('/ecletica/cancelaItens', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou ecletica cancelou conta');
  console.log(dados);
  let inicio = new Date().getTime();

  let numero = dados.numero, operacao = dados.tipoOperacao, rede = dados.rede, loja = dados.loja;

  if(!rede || !loja)
    return  res.status(400).json('Rede ou Loja não informado')

  let empresas: any = await new MapeadorDeEmpresa().listeAsync({rede: rede , loja: loja});

  if(!empresas.length)
    return res.status(400).json( String(`Empresa não encontrada "rede: ${ dados.rede}  loja: ${ dados.loja}"`));

  if(empresas.length > 1)
    return res.status(400).json(String(`Mais de uma empresa vinculada a essa Loja: "rede: ${ dados.rede}  loja: ${ dados.loja}"`));

  let notificacao = new NotificacaoMesaItemRemovido(empresas[0], numero, operacao, dados);
  let retorno: any = { sucesso: true }


  let comanda: Comanda = await notificacao.obtenhaComanda().catch(async (err) => {
    console.error(err)
    retorno.mensagem = err.message || err;
  });

  if(comanda){
    notificacao.comanda = comanda;

    retorno.ticket = notificacao.id;

    let erro  = await notificacao.valide();

    if(erro) return res.status(400).json(erro);

    await notificacao.salve( false);

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMesaService.executeRemovaItens(notificacao);
      cbAsync();
    }, (erroAsync: any) => { console.log(erroAsync)}, 100);

    console.log(String(`Notificação remova item ${operacao} - ${numero}" processada com sucesso: ${notificacao.id}, tempo:   ${new Date().getTime() - inicio}`))

    retorno.mensagem =   'Notificação processada com sucesso!'

    console.log(retorno)
    res.json(retorno);
  } else {
    if(!retorno.mensagem){
      retorno.mensagem = 'Mesa não possui nenhuma comanda aberta no momento';
      console.log(retorno)
      res.status(404).json(  retorno.mensagem);
    } else {
      console.log(retorno)
      res.status(400).json(  retorno.mensagem);
    }
  }
})




//https://fibo.promokit.com.br/hooks/ecletica/cancelaConta
router.post('/ecletica/cancelaConta', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou ecletica cancelou conta');
  console.log(dados);
  let inicio = new Date().getTime();

  let numero = dados.numero, operacao = dados.tipoOperacao, rede = dados.rede, loja = dados.loja;

  if(!rede || !loja)
    return  res.status(400).json('Rede ou Loja não informado')

  let empresas: any = await new MapeadorDeEmpresa().listeAsync({rede: rede , loja: loja});

  if(!empresas.length)
    return res.status(400).json( String(`Empresa não encontrada "rede: ${ dados.rede}  loja: ${ dados.loja}"`));

  if(empresas.length > 1)
    return res.status(400).json(String(`Mais de uma empresa vinculada a essa Loja: "rede: ${ dados.rede}  loja: ${ dados.loja}"`));

  let notificacao = new NotificacaoMesaCancelada(empresas[0], numero, operacao, dados);
  let retorno: any = { sucesso: true }

  let comanda: Comanda = await notificacao.obtenhaComanda().catch(async (err) => {
    console.error(err)
    retorno.mensagem = err.message || err;
  });

  if(comanda){
    notificacao.comanda = comanda;

    retorno.ticket = notificacao.id;

    let erro  = await notificacao.valide();

    if(erro) return res.status(400).json(erro);

    await notificacao.salve( false);

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMesaService.executeContaCancelada(notificacao);
      cbAsync();
    }, (erroAsync: any) => { console.log(erroAsync)}, 100);

    console.log(String(`Notificação feche conta ${operacao} - ${numero}" processada com sucesso: ${notificacao.id}, tempo:   ${new Date().getTime() - inicio}`))

    retorno.mensagem =   'Notificação processada com sucesso!'

    console.log(retorno)
    res.json(retorno);
  } else {
    if(!retorno.mensagem){
      retorno.mensagem = 'Mesa não possui nenhuma comanda aberta no momento';
      console.log(retorno)
      res.status(404).json(  retorno.mensagem);
    } else {
      console.log(retorno)
      res.status(400).json(  retorno.mensagem);
    }
  }
})

//https://fibo.promokit.com.br/hooks/ecletica/pedido
router.post('/ecletica/pedido', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou ecletica pedido');
  console.log(dados);
  let inicio = new Date().getTime();

  if(!dados.orderId)
    return res.status(400).json('id da transação nao informado.');

  if(!dados.statusId)
    return res.status(400).json('status do pedido nao informado.');

  if(!dados.rede || !dados.loja)
    return res.status(400).json('Rede ou Loja não informado');

  let pedido = await new MapeadorDePedido().selecioneSync({ codigoExterno: dados.orderId})

  console.log(String(`Carregou pedido (${dados.orderId}): ${new Date().getTime() - inicio}`));

  if(pedido){
    let empresa: Empresa = await new MapeadorDeEmpresa().selecioneCachePoId(pedido.empresa.id);
    console.log(String(`Carregou empresa (${dados.orderId}): ${new Date().getTime() - inicio}`));

    if(!empresa || !empresa.ehDaRedeEcletica(dados.rede, dados.loja))
      return res.status(400).json('Loja não encontrada');

    let notificacaoPedido = new NotificacaoPedidoEcletica(pedido, dados)

    await notificacaoPedido.salve();

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoPedidoService.executeEcletica(notificacaoPedido)
      cbAsync();
    }, (erroAsync: any) => { console.log(erroAsync)}, 100);

    let retorno: any = { success: true, ticket: notificacaoPedido.id }

    console.log(String(`Notificação pedido "${dados.orderId}" processada com sucesso: ${notificacaoPedido.id}, tempo:   ${new Date().getTime() - inicio}`))

    res.json(retorno);
  } else {
    res.status(400).json('Pedido não existe');
  }
})

router.get('/foodydelivery/execute/:id', async(req: any, res) => {
  let notificacao: NotificacaoPedidoFoodyDelivery = await new MapeadorDeNotificacaoPedido().selecioneSync(req.params.id);

  if(!notificacao) return res.json(Resposta.erro('Notificação nao existe'))

  if(notificacao.executada) return res.json(Resposta.erro('Notificação ja foi executada: ' + notificacao.id))

  await NotificacaoPedidoService.executeFoodyDelivery(notificacao)

  res.json({ success: true })
})

router.get('/foodydelivery/pendentes/execute', async(req: any, res) => {
  await NotificacaoPedidoService.executePendentes();


  res.json(Resposta.sucesso( ))
})

export const HooksController: Router = router;
