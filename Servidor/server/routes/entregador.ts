import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeEntregador} from "../mapeadores/MapeadorDeEntregador";
import {Entregador} from "../domain/delivery/Entregador";
import {MapeadorDeProdutoTemplate} from "../mapeadores/MapeadorDeProdutoTemplate";
import {RelatorioPedidoEntregador} from "../domain/relatorios/RelatorioPedidoEntregador";

const router: Router = Router();

router.get('/', async(req: any, res: any) => {
  let query: any = {}
  let nome = req.query.nome

  if(nome) query.nome = '%' + nome + '%'

  let entregadores: any[] = await new MapeadorDeEntregador().listeAsync(query);

  for(let i = 0; i < entregadores.length; i++)
    entregadores[i].nomeReduzido = entregadores[i].obtenhaNomeReduzido()

  res.json(Resposta.sucesso(entregadores));
})

router.get('/relatorio/pedidos/:idEntregador', async(req: any, res: any) => {
  let inicio = new Date(req.query.inicio)
  let fim = new Date(req.query.fim)

  if(!inicio)
    return res.json(Resposta.erro("É necessário informar o início do período."))

  if(!fim)
    return res.json(Resposta.erro("É necessário informar o fim do período."))


  let idEntregador = req.params.idEntregador

  if(idEntregador === "Todos") idEntregador = null
    idEntregador = Number(idEntregador)

  if(!idEntregador)
    return res.json(Resposta.erro("É necessário informar um id de entregador válido."))
  let mapeadorEntregador = new MapeadorDeEntregador()
  let entregador = await mapeadorEntregador.selecioneSync({id: idEntregador});

  if(!entregador)
    return res.json(Resposta.erro("Não foi encontrado entregador com id " + entregador.id))

  let relatorioPedidosEntregador: RelatorioPedidoEntregador[] =
    await new MapeadorDeEntregador().obtenhaRelatorioPedidosEntregador(inicio, fim,  entregador)

  let totalAntesDesconto = 0;
  let totalTaxas = 0;
  let entregasGratis = 0;
  let totalDescontos = 0;
  let qtdCorridas  = relatorioPedidosEntregador.length

  for(let i = 0; i < relatorioPedidosEntregador.length; i++) {
    relatorioPedidosEntregador[i].calculeEnderecoCompleto()
    totalTaxas += relatorioPedidosEntregador[i].taxaEntrega
    totalAntesDesconto += relatorioPedidosEntregador[i].taxaEntrega
    totalDescontos += relatorioPedidosEntregador[i].descontoTaxaEntrega
    totalAntesDesconto += relatorioPedidosEntregador[i].descontoTaxaEntrega
    entregasGratis += relatorioPedidosEntregador[i].taxaEntrega > 0 ? 0 : 1
  }

  return res.json(Resposta.sucesso({
    entregador: entregador,
    totalAntesDesconto: totalAntesDesconto,
    totalDescontos: totalDescontos,
    totalTaxas: totalTaxas,
    qtdCorridas: qtdCorridas,
    entregasGratis: entregasGratis,
    pedidos: relatorioPedidosEntregador
  }))
})

router.get('/relatorio/periodo', async (req: any, res: any) => {
  let inicio = new Date(req.query.inicio)
  let fim = new Date(req.query.fim)

  if(!inicio)
    return res.json(Resposta.erro("É necessário informar o início do período."))

  if(!fim)
    return res.json(Resposta.erro("É necessário informar o fim do período."))


  let idEntregador = req.query.idEntregador

  if(idEntregador === "Todos") idEntregador = null

  let relatorioEntregas = await new MapeadorDeEntregador().obtenhaRelatorioEntregas(inicio, fim, idEntregador ? {id: idEntregador} : null)

  if(relatorioEntregas.length === 1 && !relatorioEntregas[0].nomeEntregador)
    return res.json(Resposta.sucesso([]))

  res.json(Resposta.sucesso(relatorioEntregas))
})

router.post('/', async(req: any, res: any) => {
  const dados = req.body;

  if(!dados.nome)
    return res.json(Resposta.erro("É necessário informar o nome do entregador."))

  if(!dados.telefone)
    return res.json(Resposta.erro("É necessário informar o telefone do entregador."))

  const entregador = new Entregador();
  Object.assign(entregador, dados);

  const mapeador = new MapeadorDeEntregador();

  await mapeador.insiraGraph(entregador);

  res.json(Resposta.sucesso(entregador));

})

router.put('/', async (req: any, res) => {
  const dados = req.body;

  const mapeador = new MapeadorDeEntregador();

  if(!dados.id) return  res.json(Resposta.erro('É necessário informar o id do entregador que será atualizado.'))

  let entregador = await mapeador.selecioneSync(dados.id)

  Object.assign(entregador, dados);

  await mapeador.atualizeSync(entregador);

  res.json(Resposta.sucesso(entregador ));
});

router.delete('/:id', async(req: any, res: any) => {
  let id = req.params.id;

  if(!id)
    return res.json(Resposta.erro('É necessário informar o id do entregador a ser removido'));

  const mapeador = new MapeadorDeEntregador();

  let entregador = {id: id}

  mapeador.removaAsync(entregador).then(() => {
    return res.json(Resposta.sucesso());
  }).catch((reason: any) => {
    let mensagem = "Não foi possível remover o entregador."

    if(reason) mensagem += ' ' + reason;

    return res.json(Resposta.erro(mensagem))
  })
})

router.delete('/:id', async (req: any, res: any) => {
  let id = req.params.id
  let template =  await new MapeadorDeProdutoTemplate().selecioneSync(Number(req.params.id));

  let temProduto = await  new MapeadorDeProdutoTemplate().existeSync({id: template.id});


  await new MapeadorDeEntregador().removaAsync({id: id});

  res.json(Resposta.sucesso({ }));
})


export const EntregadoresController: Router = router;
