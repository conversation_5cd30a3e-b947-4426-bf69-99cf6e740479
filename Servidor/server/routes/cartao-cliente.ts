import {Router} from "express";
import {MapeadorDeCartaoCliente} from "../mapeadores/MapeadorDeCartaoCliente";
import {Resposta} from "../utils/Resposta";

const router: Router = Router();

router.get('/', async (req: any, res) => {
    const mapeador = new MapeadorDeCartaoCliente();

    const cartoes = await mapeador.listeAsync({});

    console.error('cartoes:', cartoes);

    res.json(Resposta.sucesso(cartoes));
});

router.get('/:id', async (req: any, res) => {
    const mapeador = new MapeadorDeCartaoCliente();
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
        return res.json(Resposta.erro('ID inválido'));
    }

    const cartao = await mapeador.selecioneSync(id);
    console.log('Cartão encontrado:', cartao);

    if (!cartao) {
        return res.json(Resposta.erro('Cartão não encontrado'));
    }

    res.json(Resposta.sucesso(cartao));
});

router.post('/', async (req: any, res) => {
    const mapeador = new MapeadorDeCartaoCliente();

    const cartao = req.body;
    cartao.empresa = req.empresa;

    console.log('dados', cartao);


    // Verifica se já existe cartão com o mesmo código
    const cartaoExistente = await mapeador.selecioneSync(cartao);
    if (cartaoExistente) {
        return res.json(Resposta.erro('Já existe um cartão cadastrado com este código.'));
    }

    const resultado = await mapeador.insiraSync(cartao);
    res.json(Resposta.sucesso(resultado));
});

router.put('/:id', (req: any, res) => {
    const mapeador = new MapeadorDeCartaoCliente();
    try {
        const cartao = req.body;
        cartao.id = req.params.id;
        cartao.empresa = req.empresa;
        mapeador.atualizeSync(cartao);
        res.json(Resposta.sucesso(cartao));
    } catch (erro) {
        res.json(Resposta.erro(erro));
    }
});

router.delete('/:id', async (req: any, res) => {
    const mapeador = new MapeadorDeCartaoCliente();
    try {
        await mapeador.removaCartaoCliente(req.params.id);
        res.json(Resposta.sucesso({}));
    } catch (erro) {
        res.json(Resposta.erro(erro));
    }
});

export const CartaoClienteController: Router = router;
