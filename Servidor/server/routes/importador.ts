import {Router} from "express";
import {ImportadorIfood} from "../lib/ifood/ImportadorIfood";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {ImportadorProdutosCSV} from "../lib/integracao/ImportadorProdutosCSV";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {MapeadorDeCategoria} from "../mapeadores/MapeadorDeCategoria";
import {Produto} from "../domain/Produto";
import {DTOProdutoSincronizar} from "../lib/integracao/ecletica/DTOProdutoSincronizar";
import {EcleticaProdutoUtils} from "../lib/integracao/ecletica/EcleticaProdutoUtils";
import {ImportardorTrendFoods} from "../lib/integracao/ImportardorTrendFoods";
import {ImportadorProduto} from "../lib/integracao/ImportadorProduto";
import {ImportadorProdutosUtils} from "../lib/integracao/ImportadorProdutosUtils";
import {TrendFoodsTarefa} from "../lib/integracao/TrendFoodsTarefa";
import {Empresa} from "../domain/Empresa";
import {MapeadorDeCatalogo} from "../mapeadores/MapeadorDeCatalogo";
import {CacheUtils} from "../service/CacheUtils";

const router: Router = Router();

router.get('/ifood/produtos/:link', async (req: any, res: any) => {
   let reducao = req.body.reducao;

   new ImportadorIfood(req.params.link).obtenhaProdutos(req.empresa, reducao).then( (produtosIfood: any) => {
     res.json(Resposta.sucesso(produtosIfood))
   }).catch( erro => {
     res.json(Resposta.erro(erro))
   })
})



router.get('/ifood/produtos/:link/novos', async (req: any, res: any) => {
  let reducao = req.query.reducao;
  let sincronizarPrecos = req.query.precos;
  let sincronizarImagens = req.query.imagens;

  new ImportadorIfood(req.params.link).obtenhaProdutos(req.empresa, reducao).then( async (resposta: any) => {
    let produtosIfood = resposta.produtos;
    let novosProdutos: any = [];
    let produtosAtualizar: any = [];
    let todosProdutos = await new MapeadorDeProduto(req.empresa.catalogo).listeAsync({});
    let todasCategorias = await new MapeadorDeCategoria(req.empresa.catalogo).listeAsync({});

    produtosIfood.forEach( (produtoIfood: any) => {
       let produtoExistente = todosProdutos.find(
         (produto: any) => produtoIfood.idIfood != null && produto.idIfood === produtoIfood.idIfood);
       if(!produtoExistente && produtoIfood.codigoPdv){
         if( produtoIfood.codigoPdv === EcleticaProdutoUtils.CODIGO_CORINGA) { // codigo coringa ecletica
           produtoExistente = todosProdutos.find((produto: any) =>
             produto.nome === produtoIfood.nome && produto.preco === produtoIfood.preco && !produto.idIfood);
         } else {
           produtoExistente = todosProdutos.find((produto: any) => produto.codigoPdv === produtoIfood.codigoPdv && !produto.idIfood);
         }

         if(produtoExistente)
           produtoExistente.idIfood = produtoIfood.idIfood
       }

       if(!produtoExistente){
         novosProdutos.push(produtoIfood)
       } else {
         DTOProdutoSincronizar.obtenhaProdutoSincronizar(produtoIfood, produtoExistente, produtosAtualizar,
           sincronizarPrecos, sincronizarImagens)
       }
    })

    let retorno: any = ImportadorProdutosUtils.retorneProdutosImportar(novosProdutos, produtosAtualizar, [], todasCategorias)

    retorno.empresaNoIfood = resposta.empresaNoIfood;

    res.json(Resposta.sucesso(retorno))

  }).catch( erro => {
    res.json(Resposta.erro(erro))
  })
})

router.get('/chinna/produtos', async (req: any, res: any) => {
  let empresa = req.empresa;
  let sincronizarPrecos = req.query.precos;
  let sincronizarImagens = req.query.imagens;

  if(!empresa.integracaoDelivery.unidadeChina && !empresa.integracaoDelivery.unidadeGendai)
    return res.json(Resposta.erro('Nenhuma unidade china/gendai configurada na integração.'))

  let importador =   new ImportardorTrendFoods(empresa.integracaoDelivery);

  importador.obtenhaProdutos( ).then( async (respostaProdutos: any) => {
      importador.obtenhaApenasProdutosComPrecos(respostaProdutos.produtos, respostaProdutos.produtosComSku)
        .then( async (produtosImportar) => {
        let service = empresa.integracaoDelivery.obtenhaService();

        let produtosErpIndisponiveis: any = await service.listeProdutosIndisponiveis( ).catch( (err: any) => {
          res.json(Resposta.erro(err))
        })

        if(produtosErpIndisponiveis){
          let resposta: any =
            await ImportadorProdutosUtils.classifiqueProdutosImportar(empresa.catalogo, produtosImportar, produtosErpIndisponiveis,
                                                                      sincronizarPrecos, sincronizarImagens)

          res.json(Resposta.sucesso(resposta))
        }
      }).  catch( error => {
        console.log(error)
        res.json(Resposta.erro(error.message ?  error.message : error))
      })

  }).catch( erro => {
    res.json(Resposta.erro(erro))
  })
})

router.post('/finalizou', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(empresa.integracaoDelivery){
    await empresa.integracaoDelivery.atualizeUltimaImportacaoProdutos()
    await new MapeadorDeEmpresa().removaDasCaches(empresa)
  }

  await new MapeadorDeProduto(empresa.catalogo).recalculeOrdens(empresa.catalogo);
  await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

  res.json(Resposta.sucesso( ))
})


router.post('/produto', async (req: any, res: any) => {
  let dadosProduto = req.body;

  new ImportadorProduto().importeProduto(req.empresa.catalogo, dadosProduto).then( async(produto: Produto) => {
     await CacheUtils.limpeCacheEmpresa(req.empresa)
     res.json(Resposta.sucesso(produto))
  }).catch( (erro) => {
     res.json(Resposta.erro(erro))
  })

})

router.post('/empresa/:idEmpresa/ifood', async (req: any, res: any) => {
  let dados = req.body;
  let link = dados.link;
  let reducao = dados.reducao;
  let idEmpresa = req.params.idEmpresa;

  if(!idEmpresa)
    return res.json({
      sucesso: false,
      erro: "É necessário informar o id da empresa"
    })

  let importador = new ImportadorIfood(link);
  let mapeadorDeEmpresa = new MapeadorDeEmpresa();

  mapeadorDeEmpresa.selecioneCachePoId(idEmpresa).then((empresa) => {
    if(!empresa)
      return res.json({
        sucesso: false,
        erro: "Não foi possível encontrar uma empresa com o id " + idEmpresa
      })

    importador.obtenhaMenu(empresa, reducao).then((resposta: any) => {
      res.json(resposta);
    }).catch((rejeicao: any) => {
      res.send(rejeicao)
    })

  })
});


router.post('/catalogo/:id/precos/csv', async (req: any, res) => {
  const file: any = req.files.file,
    partesNome: any = file.name.split('.'),
    extensao: any = file.name.split('.')[partesNome.length - 1];

  let config = req.body.data ? JSON.parse(req.body.data) : null

  let idCatalogo = req.params.id;

  let catalogo = await new MapeadorDeCatalogo().selecioneSync(idCatalogo);

  if(extensao !== 'csv')
    return res.json( Resposta.erro( "Não temos suporte a arquivos do tipo " + extensao))

  let importador = new ImportadorProdutosCSV(req.empresa)


  let listaProdutosPrecos = await importador.obtenhaProdutos(file, extensao, config).catch((err) => {
    res.json(Resposta.erro(err))
  })

  if(listaProdutosPrecos){
    let resposta =  await ImportadorProdutosUtils.classifiqueProdutosPrecosAlterados(catalogo, listaProdutosPrecos)

    res.json(Resposta.sucesso(resposta))
  }

});

router.post('/empresa/:idEmpresa/csv', async (req: any, res) => {
  const file: any = req.files.file,
    partesNome: any = file.name.split('.'),
    extensao: any = file.name.split('.')[partesNome.length - 1];

  let config = req.body.data ? JSON.parse(req.body.data) : null

  let idEmpresa = req.params.idEmpresa;

  if(extensao !== 'csv')
    return res.json({
      sucesso: false,
      erro: "Não temos suporte a arquivos do tipo " + extensao
    })


  let mapeador = new MapeadorDeEmpresa()

  mapeador.selecioneCachePoId(idEmpresa).then((empresa) => {
    let importador = new ImportadorProdutosCSV(empresa)

    importador.importeProdutosDoArquivo(file, extensao, config).then((resposta) => {
      res.json(resposta)
    }).catch((rejeicao: any) => {
      res.json({
        sucesso: false,
        erro: 'Ocorreu um erro ao importar: ' + rejeicao
      })
    })
  })
})

router.post('/catalogo/salveLojaIfood', async (req: any, res: any) => {
  let catalogo: any  = req.empresa.catalogo;

  if(catalogo){
    catalogo.lojaIfood = req.body.linkIfood.split('/').pop();
    await new MapeadorDeCatalogo().atualizeLojaIfood(catalogo);
    await new MapeadorDeEmpresa().removaDasCaches(req.empresa, true);
  }

  res.json(Resposta.sucesso());
})


export const ImportadorController: Router = router;
