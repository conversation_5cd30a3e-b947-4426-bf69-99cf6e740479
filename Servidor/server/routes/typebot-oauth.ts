import { v4 as uuidv4 } from 'uuid';
import { Router } from 'express';
var jwt = require('jsonwebtoken');
var jose = require('node-jose');
import * as fs from 'fs';
import * as path from 'path';
const redis = require('redis');
const redisClient = redis.createClient();
redisClient.on('error', (err: any) => {
    console.error('[typebot-oauth] Redis error:', err);
});

const router = Router();

const CLIENT_ID = 'tenant_1';
const CLIENT_SECRET = 'pass_tent_1';
const USER = 'defaultUser';

// Removendo constantes de caminho de arquivos e definindo armazenamento como Redis
const KEYS_STORAGE = 'redis';

console.log('[typebot-oauth] Inicializando módulo com CLIENT_ID:', CLIENT_ID);
console.log('[typebot-oauth] Diretório de chaves:', KEYS_STORAGE);

// Variável global para armazenar as chaves em memória
let keys: any = null;

// Função para gerar chaves RSA e salvar no Redis
const generateKeys = async () => {
  console.log('[typebot-oauth] Gerando novo par de chaves RSA...');
  try {
    const keyStore = jose.JWK.createKeyStore();
    const key = await keyStore.generate('RSA', 2048, {
      alg: 'RS256',
      use: 'sig',
      kid: 'typebot-oauth-key-' + new Date().toISOString().split('T')[0]
    });

    const result = {
      privateKey: key,
      publicKey: key.toJSON(),
    };

    // Salvando as chaves no Redis para persistência
    try {
      await new Promise((resolve, reject) => {
        redisClient.set("typebot_private_key", JSON.stringify(key.toJSON(true)), (err: any) => {
          if (err) reject(err);
          else resolve(null);
        });
      });
      await new Promise((resolve, reject) => {
        redisClient.set("typebot_public_key", JSON.stringify(key.toJSON()), (err: any) => {
          if (err) reject(err);
          else resolve(null);
        });
      });
      console.log('[typebot-oauth] Chaves salvas no Redis com sucesso');
    } catch (redisError) {
      console.error('[typebot-oauth] AVISO: Não foi possível salvar as chaves no Redis:', redisError);
      console.log('[typebot-oauth] Continuando com chaves apenas em memória');
    }

    console.log('[typebot-oauth] Par de chaves RSA gerado com sucesso');
    return result;
  } catch (error) {
    console.error('[typebot-oauth] ERRO ao gerar chaves RSA:', error);
    throw error;
  }
};

// Função para carregar chaves existentes do Redis
const carregueChavesExistentes = async () => {
  try {
    const privateKeyStr = await new Promise((resolve, reject) => {
      redisClient.get("typebot_private_key", (err: any, result: any) => {
        if (err) reject(err);
        else resolve(result);
      });
    });
    const publicKeyStr = await new Promise((resolve, reject) => {
      redisClient.get("typebot_public_key", (err: any, result: any) => {
        if (err) reject(err);
        else resolve(result);
      });
    });
    if (privateKeyStr && publicKeyStr) {
      console.log('[typebot-oauth] Chaves existentes encontradas no Redis');
      const privateKeyJson = JSON.parse(privateKeyStr as string);
      const publicKeyJson = JSON.parse(publicKeyStr as string);
      const keyStore = jose.JWK.createKeyStore();
      const privateKey = await keyStore.add(privateKeyJson);
      console.log('[typebot-oauth] Chaves carregadas do Redis com sucesso');
      return { privateKey, publicKey: publicKeyJson };
    }
    return null;
  } catch (error) {
    console.error('[typebot-oauth] ERRO ao carregar chaves do Redis:', error);
    return null;
  }
};

async function obtenhaKeys() {
  console.log('[typebot-oauth] Obtendo chaves...');

  if (keys) {
    console.log('[typebot-oauth] Usando chaves existentes em memória');
    return keys;
  }

  const chavesExistentes = await carregueChavesExistentes();
  if (chavesExistentes) {
    console.log('[typebot-oauth] Usando chaves carregadas do Redis');
    keys = chavesExistentes;
    return keys;
  }

  console.log('[typebot-oauth] Chaves não encontradas, gerando novas...');
  keys = await generateKeys();
  console.log('[typebot-oauth] Novas chaves geradas e armazenadas');

  return keys;
}

// Inicializa as chaves no startup para evitar atrasos na primeira requisição
(async () => {
  try {
    console.log('[typebot-oauth] Inicializando chaves no startup...');
    await obtenhaKeys();
    console.log('[typebot-oauth] Chaves inicializadas com sucesso');
  } catch (error) {
    console.error('[typebot-oauth] ERRO ao inicializar chaves:', error);
  }
})();

// Interface para os códigos de autorização
interface AuthorizationCode {
  client_id: string;
  redirect_uri: string;
  scope: string;
  state: string;
  createdAt: number;
}

// Map para armazenar os códigos de autorização em memória
const authorizationCodes = new Map<string, AuthorizationCode>();
const accessTokens = new Map<string, { user: string; scope: string }>();

router.get('/.well-known/openid-configuration', (req: any, res: any) => {
  console.log('[typebot-oauth] Requisição recebida para /.well-known/openid-configuration');
  console.log('[typebot-oauth] Headers:', JSON.stringify(req.headers));

  const host = req.headers.host as string;
  console.log('[typebot-oauth] Host detectado:', host);

  const config = {
    issuer: `https://${host}`,
    authorization_endpoint: `https://${host}/typebot-oauth/authorize`,
    token_endpoint: `https://${host}/typebot-oauth/token`,
    userinfo_endpoint: `https://${host}/typebot-oauth/userinfo`,
    jwks_uri: `https://${host}/typebot-oauth/jwks`,
    response_types_supported: ['code'],
    grant_types_supported: ['authorization_code'],
    scopes_supported: ['profile'],
  };

  console.log('[typebot-oauth] Enviando configuração OpenID:', JSON.stringify(config));
  return res.json(config);
});

router.get('/jwks', async (req: any, res: any) => {
  console.log('[typebot-oauth] Requisição recebida para /jwks');
  console.log('[typebot-oauth] Headers:', JSON.stringify(req.headers));

  try {
    const keys = await obtenhaKeys();
    console.log('[typebot-oauth] Enviando JWKS (chaves públicas)');
    return res.json({ keys: [keys.publicKey] });
  } catch (error) {
    console.error('[typebot-oauth] ERRO ao obter JWKS:', error);
    return res.status(500).json({ error: 'Erro ao obter chaves JWKS' });
  }
});

// Atualizando endpoint /authorize para usar Redis
router.get('/authorize', async (req: any, res: any) => {
  console.log('[typebot-oauth] Requisição recebida para /authorize');
  console.log('[typebot-oauth] Query params:', JSON.stringify(req.query));
  console.log('[typebot-oauth] Headers:', JSON.stringify(req.headers));

  try {
    const { client_id, redirect_uri, response_type, scope, state } = req.query;
    console.log('[typebot-oauth] Parâmetros de autorização:', { client_id, redirect_uri, response_type, scope, state });

    if (!client_id || !redirect_uri || response_type !== 'code') {
      console.error('[typebot-oauth] ERRO: Parâmetros inválidos', { client_id, redirect_uri, response_type });
      return res.status(400).json({ error: 'Parâmetros inválidos' });
    }

    const authorizationCode = uuidv4();
    console.log('[typebot-oauth] Código de autorização gerado:', authorizationCode);

    const authData: AuthorizationCode = {
      client_id,
      redirect_uri,
      scope,
      state,
      createdAt: Date.now()
    };

    await new Promise((resolve, reject) => {
      redisClient.hset("typebot_auth_codes", authorizationCode, JSON.stringify(authData), (err: any, reply: any) => {
        if (err) {
          console.error('[typebot-oauth] ERRO ao salvar código de autorização no Redis:', err);
          reject(err);
        } else {
          console.log('[typebot-oauth] Código de autorização salvo no Redis com sucesso');
          resolve(reply);
        }
      });
    });

    const redirectUrl = `${redirect_uri}?code=${authorizationCode}&state=${state}`;
    console.log('[typebot-oauth] Redirecionando para:', redirectUrl);
    return res.redirect(redirectUrl);
  } catch (error) {
    console.error('[typebot-oauth] ERRO no endpoint /authorize:', error);
    return res.status(500).json({ error: 'server_error' });
  }
});

router.post('/token', async (req: any, res: any) => {
  console.log('[typebot-oauth] Requisição recebida para /token');
  console.log('[typebot-oauth] Headers:', JSON.stringify(req.headers));
  console.log('[typebot-oauth] Body:', JSON.stringify(req.body));

  try {
    if (!req.headers.authorization) {
      console.error('[typebot-oauth] ERRO: Header de autorização ausente');
      return res.status(401).json({
        error: 'invalid_client',
        error_description: 'Header de autorização ausente'
      });
    }

    const authHeader = req.headers.authorization;
    console.log('[typebot-oauth] Authorization header:', authHeader);

    if (!authHeader.startsWith('Basic ')) {
      console.error('[typebot-oauth] ERRO: Header de autorização não é Basic');
      return res.status(401).json({
        error: 'invalid_client',
        error_description: 'Formato de autorização inválido'
      });
    }

    const base64Credentials = authHeader.split(' ')[1];
    console.log('[typebot-oauth] Credenciais Base64:', base64Credentials);

    const credentials = Buffer
      .from(base64Credentials, 'base64')
      .toString('ascii');
    console.log('[typebot-oauth] Credenciais decodificadas:', credentials);

    const [client_id, client_secret] = credentials.split(':');
    console.log('[typebot-oauth] Client ID extraído:', client_id);
    console.log('[typebot-oauth] Client Secret extraído (primeiros 4 caracteres):', client_secret ? client_secret.substring(0, 4) + '...' : 'undefined');

    const { grant_type, code } = req.body;
    console.log('[typebot-oauth] Grant Type:', grant_type);
    console.log('[typebot-oauth] Código de autorização recebido:', code);

    if (!grant_type || !code || !client_id || !client_secret) {
      console.error('[typebot-oauth] ERRO: Parâmetros obrigatórios ausentes', {
        grant_type: !!grant_type,
        code: !!code,
        client_id: !!client_id,
        client_secret: !!client_secret
      });
      return res.status(400).json({
        error: 'invalid_request',
        error_description: 'Parâmetros obrigatórios ausentes'
      });
    }

    console.log('[typebot-oauth] Verificando credenciais do cliente...');
    console.log('[typebot-oauth] CLIENT_ID esperado:', CLIENT_ID);
    if (client_id !== CLIENT_ID || client_secret !== CLIENT_SECRET) {
      console.error('[typebot-oauth] ERRO: Credenciais inválidas', {
        client_id_match: client_id === CLIENT_ID,
        client_secret_match: client_secret === CLIENT_SECRET
      });
      return res.status(401).json({
        error: 'invalid_client',
        error_description: 'Credenciais do cliente inválidas'
      });
    }
    console.log('[typebot-oauth] Credenciais do cliente validadas com sucesso');

    console.log('[typebot-oauth] Verificando código de autorização...');
    const authDataStr = await new Promise((resolve, reject) => {
      redisClient.hget("typebot_auth_codes", code, (err: any, result: any) => {
        if (err) reject(err);
        else resolve(result);
      });
    });
    if (!authDataStr) {
      console.error('[typebot-oauth] ERRO: Código não encontrado', { code });
      return res.status(400).json({
        error: 'invalid_grant',
        error_description: 'Código de autorização inválido'
      });
    }
    const authData: AuthorizationCode = JSON.parse(authDataStr as string);
    console.log('[typebot-oauth] Código encontrado:', JSON.stringify(authData));

    const tempoDecorrido = Date.now() - authData.createdAt;
    console.log('[typebot-oauth] Tempo decorrido desde a criação do código (ms):', tempoDecorrido);

    if (tempoDecorrido > 10 * 60 * 1000) {
      console.error('[typebot-oauth] ERRO: Código expirado', { tempoDecorrido });
      await new Promise((resolve, reject) => {
        redisClient.hdel("typebot_auth_codes", code, (err: any, result: any) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
      return res.status(400).json({
        error: 'invalid_grant',
        error_description: 'Código de autorização expirado'
      });
    }

    await new Promise((resolve, reject) => {
      redisClient.hdel("typebot_auth_codes", code, (err: any, result: any) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    const host = req.headers.host;
    console.log('[typebot-oauth] Host para geração de token:', host);

    const now = Math.floor(Date.now() / 1000);
    console.log('[typebot-oauth] Timestamp atual:', now);

    console.log('[typebot-oauth] Obtendo chaves para assinatura...');
    const keys = await obtenhaKeys();
    console.log('[typebot-oauth] Chaves obtidas com sucesso');

    console.log('[typebot-oauth] Gerando payload do token JWT...');
    const payload = {
      iss: `https://${host}`,
      sub: USER,
      aud: CLIENT_ID,
      name: 'Test User 6',
      email: '<EMAIL>',
      exp: now + 3600,
      iat: now,
      id: 'user:6'
    };
    console.log('[typebot-oauth] Payload do token:', JSON.stringify(payload));

    try {
      console.log('[typebot-oauth] Assinando token JWT...');
      const privateKeyPEM = keys.privateKey.toPEM(true);
      console.log('[typebot-oauth] Chave privada PEM obtida (primeiros 20 caracteres):', privateKeyPEM.substring(0, 20) + '...');

      const accessToken = jwt.sign(
        payload,
        privateKeyPEM,
        {
          algorithm: 'RS256'
        }
      );
      console.log('[typebot-oauth] Token JWT assinado com sucesso (primeiros 20 caracteres):', accessToken.substring(0, 20) + '...');

      const response = {
        access_token: accessToken,
        id_token: accessToken,
        token_type: 'Bearer',
        expires_in: 3600
      };
      console.log('[typebot-oauth] Enviando resposta de token');
      return res.json(response);
    } catch (jwtError) {
      console.error('[typebot-oauth] ERRO ao assinar JWT:', jwtError);
      return res.status(500).json({
        error: 'server_error',
        error_description: 'Erro ao gerar token JWT: ' + jwtError.message
      });
    }

  } catch (error) {
    console.error('[typebot-oauth] ERRO no endpoint /token:', error);
    console.error('[typebot-oauth] Stack trace:', error.stack);
    return res.status(500).json({
      error: 'server_error',
      error_description: error.message
    });
  }
});

// Atualizando endpoint de debug para usar Redis para códigos de autorização
router.get('/debug', (req: any, res: any) => {
  console.log('[typebot-oauth] Requisição de debug recebida');
  redisClient.hgetall("typebot_auth_codes", (err: any, codes: any) => {
    if (err) {
      console.error('[typebot-oauth] ERRO ao obter códigos de autorização do Redis:', err);
      codes = {};
    }
    const codesKeys = codes ? Object.keys(codes) : [];
    const keysInfo = keys ? {
      kid: keys.publicKey.kid,
      alg: keys.publicKey.alg,
      use: keys.publicKey.use,
      kty: keys.publicKey.kty,
      initialized: true
    } : { initialized: false };
    return res.json({
      status: 'online',
      keysInfo,
      authCodesActive: codesKeys.length,
      authCodesKeys: codesKeys,
      accessTokensActive: accessTokens.size,
      clientId: CLIENT_ID,
      timestamp: new Date().toISOString(),
      keysStorage: KEYS_STORAGE
    });
  });
});

export const TypebotOauthController: Router = router;

