import {Router} from "express";
import {ChatwootService} from "../service/ChatwootService";
import {GupshupService} from "../service/GupshupService";
const axios = require('axios');
const router: Router = Router();


router.get('/teste', async (req: any, res: any) => {
  const gupshupService = new GupshupService();

  await gupshupService.envieMensagem({telefone: '************'}, {texto: 'Oi'});

  res.json({
    sucesso: true
  });
});

router.post('/teste', async (req: any, res: any) => {
  const msg = req.body;
  const sender = msg.sender;
  const inbox = msg.inbox;
  const conversa = msg.conversation;
  const conta = msg.account;
  const contatoInbox = conversa.contact_inbox;

  const gupshupService = new GupshupService();

  if( msg.message_type === 'incoming' ) {
    return;
  }

  await gupshupService.envieMensagem({telefone: '************'}, {texto: msg.content});

  res.json({
    sucesso: true
  });
});

export const ChatwootController: Router = router;


//gupshup post message
