import {Router} from "express";
import MapeadorDeTablet from '../mapeadores/MapeadorDeTablet';
import {MapeadorDeMesa} from '../mapeadores/MapeadorDeMesa';
import {Resposta} from "../utils/Resposta";
import * as redis from 'redis';
import {TabletService} from "../service/TabletService";
import {Tablet} from "../domain/Tablet";

const router: Router = Router();

// Cliente Redis
const redisClient = redis.createClient( );

// Middleware para validar token Redis
const validarTokenTablet = async (req: any, res: any, next: any) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '') || req.body.token;

    if (!token)
      return res.json(Resposta.erro('Token de autenticação é obrigatório'));

    const tokenKey = `tablet_auth:${token}`;
     redisClient.get(tokenKey, (err: any, dadosUsuario: any) => {
      if (!dadosUsuario || typeof dadosUsuario !== 'string')
        return res.json(Resposta.erro('Token inválido ou expirado'));

      // Adicionar dados do usuário na requisição
      req.user  = JSON.parse(dadosUsuario);
      next();
    });

  } catch (error) {
    console.error('Erro ao validar token:', error);
    res.json(Resposta.erro('Erro interno do servidor'));
  }
};

// Rota para listar mesas com status de associação
router.get('/mesas', validarTokenTablet, async (req: any, res: any) => {
  try {
    const mapeadorMesa = new MapeadorDeMesa();
    const mapeadorTablet = new MapeadorDeTablet();
    // Buscar todas as mesas da empresa
    const mesas = await mapeadorMesa.listeAsync({});

    for(let mesa of mesas)
      mesa.disponivel  = !mesa.tablet

    res.json(Resposta.sucesso(mesas));

  } catch (error) {
    console.error('Erro ao listar mesas:', error);
    res.json(Resposta.erro('Erro interno do servidor'));
  }
});

// Rota para associar mesa ao tablet
router.post('/associar-mesa', validarTokenTablet, async (req: any, res: any) => {
  try {
    const { numeroTablet, idMesa } = req.body;
    const usuarioTablet = req.user;

    if (!numeroTablet || !idMesa)
      return res.json(Resposta.erro('Número do tablet e ID da mesa são obrigatórios'));

    let tablet: Tablet = await new TabletService().associeMesaAoTablet(numeroTablet, idMesa, req.empresa).catch((err) => {
        throw Error(err)
    });

    if(tablet){
      res.json(Resposta.sucesso({
        mensagem: `Mesa ${tablet.mesa.nome} associada com sucesso`,
        mesa: tablet.mesa
      }));
    }

  } catch (error) {
    console.error('Erro ao associar mesa:', error);
    res.json(Resposta.erro('Erro interno do servidor'));
  }
});

// Rota para obter informações do tablet
router.get('/info/:numero', async (req: any, res: any) => {
  try {
    const { numero } = req.params;

    if (!numero)
      return res.json(Resposta.erro('Número do tablet é obrigatório.'));

    const tablet = await  new MapeadorDeTablet().obtenhaTabletPorNumero(numero);

    if (!tablet)
      return res.json(Resposta.erro(`Tablet "${numero}" não cadastrado.`));

    res.json(Resposta.sucesso(tablet));
  } catch (error) {
    console.error('Erro ao obter informações do tablet:', error);
    res.json(Resposta.erro('Erro interno do servidor.'));
  }
});

export const TabletController: Router = router;
