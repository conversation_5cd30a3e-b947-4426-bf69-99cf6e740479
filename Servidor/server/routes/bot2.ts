import {Router} from 'express';

import {ComunicadorDialogFlow} from "../service/ComunicadorDialogFlow";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {Empresa} from "../domain/Empresa";
import {dialogflow} from "actions-on-google";
import {Resposta} from "../utils/Resposta";
import {Ambiente} from "../service/Ambiente";
import {BotsService} from "../service/BotsService";
import {MapeadorDeMensagemBot} from "../mapeadores/MapeadorDeMensagemBot";
import {DTOPedido} from "../lib/dto/DTOPedido";
import {MapeadorDeConfigMensagemDeBot} from "../mapeadores/MapeadorDeConfigMensagemDeBot";
import {NotificacaoService} from "../service/NotificacaoService";
import {ConfigMensagemDeBot} from "../domain/chatbot/ConfigMensagemDeBot";
import {TratadorDeMensagemWitai} from "../lib/instagram/TratadorDeMensagemWitai";
import {ResponseIG} from "../lib/instagram/ResponseIG";
import {I18nConfig} from "../lib/instagram/i18n.config";
import {Usuario} from "../domain/Usuario";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {Contato} from "../domain/Contato";
import {MensagemBot} from "../domain/MensagemBot";
import {MensagemWhatsappWeb} from "../domain/MensagemWhatsappWeb";
import {MapeadorDeMensagemWhatsappWeb} from "../mapeadores/MapeadorDeMensagemWhatsappWeb";
import {EnumServicoHorarioFuncionamento} from "../lib/emun/EnumServicoHorarioFuncionamento";
const _  = require('underscore');
let redis = require("redis");
let client = redis.createClient();

const router: Router = Router();

// Create an app instance
const app = dialogflow()

class RespostaMensagem {
  mensagem: string;
  beep: boolean;

  constructor(mensagem: string, beep: boolean) {
    this.mensagem = mensagem;
    this.beep = beep;
  }
}

router.post('/ativar', async(req, res) => {
  const telefone = req.body.tel;

  if( !telefone ) {
    return res.json({
      sucesso: false
    });
  }

  const ativar = req.body.ativar;

  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    const comunicador: ComunicadorDialogFlow = ComunicadorDialogFlow.Instance;

    comunicador.ativeBot(empresa, telefone, ativar).then( (objResposta: Resposta<any>) => {
      if( ativar === false ) {
        comunicador.obtenhPedido(empresa, telefone).then( (dtoPedido: DTOPedido) => {
          objResposta.data = dtoPedido;

          res.json(objResposta);
        }).catch( (erro) => {
          res.json(Resposta.erro(erro));
        });

        return;
      }

      res.json(objResposta);
    });
  });
});

router.get('/conversas', async(req, res) => {
  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    if( !empresa ) {
      return res.json(Resposta.sucesso([]));
    }

    let objResposta: any = await ComunicadorDialogFlow.Instance.conversasAtivas(empresa);

    res.json(objResposta);
  });
});

function respondaHorarioAtendimento(empresa: Empresa) {
  const msg = "⏰ Nossos horários são: \n" + empresa.obtenhaDescricaoHorarioAtendimento();

  return Promise.resolve(msg);
}

async function respondaEnviarCardapio(empresa: Empresa, usuario: Usuario, cliente: Contato, nome: string,
                                      telefone: string): Promise<RespostaMensagem> {
  const i18n = I18nConfig.Instance.i18n();

  let msg = '';

  if( cliente ) {
    msg = `Olá, ${nome}! Que bom conversar com você de novo 😃.\n\n`
  }

  const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

  const m = await notificacaoService.obtenhaMensagemSaudacao(empresa, nome, "+55", telefone, usuario);

  msg += m.data.msg.mensagemFinal;

  return Promise.resolve(new RespostaMensagem(msg, false));
}

async function respondaFome(empresa: Empresa, usuario: Usuario, nome: string, telefone: string) {
  const i18n = I18nConfig.Instance.i18n();

  let msg = ResponseIG.genText(i18n.__("fome.mensagem")).text;

  msg += `\n\n`;

  const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

  const m = await notificacaoService.obtenhaMensagemSaudacao(empresa, nome, "+55", telefone, usuario);

  msg += m.data.msg.mensagemFinal;

  return Promise.resolve(msg);
}

function respondaCumprimento() {
}

function respondaAgradecimento() {
  const i18n = I18nConfig.Instance.i18n();

  return Promise.resolve(ResponseIG.genText(i18n.__("agradecimento.mensagem")).text);
}

function respondaAtendenteAjudaPedido(empresa: Empresa, usuario: Usuario, nome: string, telefone: string, texto: string) {
  const i18n = I18nConfig.Instance.i18n();

  const msg = `Olá ${nome}, acesse o link e escolha a loja que deseja fazer a reserva 😊
https://zapflyingsushi.meucardapio.ai/`;

  const chaveAtendente = 'atendentezap:' + empresa.id + '_' + telefone;

  client.setex(chaveAtendente, 30 * 60 * 1, 'true', (erro: any) => {
  });

  return Promise.resolve(msg);
}

function respondaCupomRodizio(empresa: Empresa, usuario: Usuario, nome: string, telefone: string, texto: string) {
  const msg = `*RODIZIOFLYING10OFF*

✨✨Olá, você acaba de receber seu cupom de 10% de desconto no Rodízio do Flying Sushi.
Apresente o cupom RODIZIOFLYING10OFF e saboreie um excelente momento. ✨✨


Desconto válido uma única vez por CPF.
Promoção não cumulativa.
Obs: promoção não acumulativa com outras promoções Flying Sushi 🍣`;

    return Promise.resolve(msg);
}

function respondaAniversariante(empresa: Empresa, usuario: Usuario, nome: string, telefone: string, texto: string) {
  const msg = `CUPOM DE DESCONTO: MEUNIVERNOFLYING

Olá, você acaba de receber seu cupom de presente para comemorar seu aniversário no Flying.
Apresente o cupom MEUNIVERNOFLYING junto com seu documento, vá com mais 3 adultos pagantes e ganhe seu rodízio de presente.

Reserve aqui a sua data

Desconto válido uma única vez por CPF.
Promoção não cumulativa válida dentro do mês de aniversário`;

  return Promise.resolve(msg);
}


function respondaFreeRefil(empresa: Empresa, usuario: Usuario, nome: string, telefone: string, texto: string) {
  const msg = `*FREEREFILFLYING*

✨✨Olá, você acaba de ganhar grátis um free refil de refrigerante e água ao pedir um rodízio premium no Flying Sushi.✨✨

Apresente o cupom FREEREFILFLYING e se delicie em um momento regado de comida deliciosa e bebida a vontade (refrigerante e água)

Desconto válido uma única vez por CPF.
Promoção não cumulativa válida até 30/10/22
Obs: promoção não acumulativa com outras promoções Flying Sushi 🍣`;

  return Promise.resolve(msg);
}

function respondaAtendente(empresa: Empresa, usuario: Usuario, nome: string, telefone: string, texto: string) {
  const i18n = I18nConfig.Instance.i18n();

  const msg = `Olá, aguarde um momento por gentileza, estou chamando um dos nossos atendentes para lhe ajudar! 😊
`;

  const chaveAtendente = 'atendentezap:' + empresa.id + '_' + telefone;

  client.setex(chaveAtendente, 60 * 1, 'true', (erro: any) => {
  });

  return Promise.resolve(msg);
}

function respondaWhatsappDaLoja() {
  return Promise.resolve('Nós já estamos falando pelo Whatsapp. :-)');
}

function respondaTaxasDeEntrega() {
  return Promise.resolve('Acesse o cardápio digital para ver as taxas de entrega.');
}

async function respondaPedirPeloZap(empresa: Empresa, usuario: Usuario, nome: string, telefone: string, texto: string) {
  const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

  const contexto = await notificacaoService.obtenhaLinkCardapio(empresa, nome, "+55", telefone, usuario);

  let msg = `Oi ${nome}, se você fizer o pedido pelo cardápio, conseguiremos entregar seu pedido mais rápido. 🛵
Além disso, você nos ajuda muito pedindo pelo cardápio. 🤗
`;

  msg += `
Segue o link do cardápio para fazer seu pedido.

${contexto.linkCardapio}`;

  return Promise.resolve(msg);
}

async function respondaLinkDoCardapio(empresa: Empresa, usuario: Usuario, nome: string, telefone: string, texto: string) {
  const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));
  const contexto = await notificacaoService.obtenhaLinkCardapio(empresa, nome, "+55", telefone, usuario);

  let msg = `Obrigado por escolher o Flying Sushi! Para iniciar seu pedido:

- Clique no link para acessar nossa página central de pedidos.
- Insira seu CEP para ser direcionado para unidade mais próxima.

${contexto.linkCardapio}`;

  return Promise.resolve(msg);
}

async function processeMensagemDeTexto(empresa: Empresa, usuario: Usuario, contato: Contato, nome: string,
                                       telefone: string, texto: string): Promise<RespostaMensagem> {
  let mensagem = null;
  if( texto === '1' ) {
    mensagem = await respondaLinkDoCardapio(empresa, usuario, nome, telefone, texto);
    return new RespostaMensagem(mensagem, false);
  }

  if( texto === '2' ) {
    mensagem = await respondaAtendenteAjudaPedido(empresa, usuario, nome, telefone, texto);
    return new RespostaMensagem(mensagem, true);
  }

  if( texto === '3' ) {
    mensagem = await respondaAtendente(empresa, usuario, nome, telefone, texto);
    return new RespostaMensagem(mensagem, true);
  }

  const intent = await new TratadorDeMensagemWitai().processeTexto(texto);

  console.log(`msg: ${texto} intent: ${intent}`);

  switch (intent) {
    case 'pedirpelozap':
      mensagem = await respondaPedirPeloZap(empresa, usuario, nome, telefone, texto);
      return new RespostaMensagem(mensagem, false);
    case 'horario_atendimento':
      mensagem = await respondaHorarioAtendimento(empresa);
      return new RespostaMensagem(mensagem, false);
    case 'reserva':
      mensagem = await respondaAtendenteAjudaPedido(empresa, usuario, nome, telefone, texto);
      return new RespostaMensagem(mensagem, true);
    case 'fazerpedido':
      mensagem = await respondaLinkDoCardapio(empresa, usuario, nome, telefone, texto);
      return new RespostaMensagem(mensagem, false);
    case 'enviar_cardapio':
      return await respondaEnviarCardapio(empresa, usuario, null, nome, telefone);
    case 'fome':
      mensagem = await respondaFome(empresa, usuario, nome, telefone);
      return new RespostaMensagem(mensagem, false);
    case 'cumprimento':
      return await respondaEnviarCardapio(empresa, usuario, null, nome, telefone);
    case 'agradecimento':
      mensagem = await respondaAgradecimento();
      return new RespostaMensagem(mensagem, false);
    //case 'whatsapp':
//      return await respondaWhatsappDaLoja();
    case 'taxasdeentrega':
      mensagem = await respondaTaxasDeEntrega();
      return new RespostaMensagem(mensagem, false);
    case 'atendente':
      mensagem = await respondaAtendente(empresa, usuario, nome, telefone, texto);
      return new RespostaMensagem(mensagem, false);
    default:
      return new RespostaMensagem('', false);
  }
}

router.post('/salve-mensagem', async (req: any, res) => {
  const dados = req.body;
  const nome = req.body.n;
  const inicio = new Date();
  const empresa: Empresa = req.empresa;
  const usuario: Usuario = req.user;
  const chatId: string = req.body.chatId;

  const numeros = ['556281712622', '556281043475', '556282301144'];
  if (numeros.some(numero => dados.idDestinatario.includes(numero) || dados.idRemetente.includes(numero))) {
    //console.log(`Número bloqueado: ${dados.idDestinatario} ${dados.idRemetente}`);

    res.json(Resposta.erro('Não salvar'));

    return;
  }
  const mapeadorContato = new MapeadorDeContato();

  empresa.setHorariosFuncionamento(EnumServicoHorarioFuncionamento.Site);

  const mensagemWhatsappWeb = new MensagemWhatsappWeb();
  Object.assign(mensagemWhatsappWeb, req.body);
  if( !mensagemWhatsappWeb.nomeContato ) {
    mensagemWhatsappWeb.nomeContato = '';
  }

  const inseriu = await new MapeadorDeMensagemWhatsappWeb().insiraGraph(mensagemWhatsappWeb);

  if( true ) {
    res.json(Resposta.erro('Desativado'));
    return;
  }

  /*

  if(!empresa.estaAberta ) {
    return res.json(Resposta.erro('Loja não está aberta'));
  }

  const chaveConversa = 'conversazap:' + empresa.id + '_' + telefone;
  client.get(chaveConversa, async (erroChave: Error, temConversa: string) => {
    if( !temConversa ) {
      client.setex(chaveConversa, 60 * 60 * 1, 'true', (erro: any) => {
      });

      let objResposta = null;

      objResposta = await respondaEnviarCardapio(empresa, usuario, cliente, nome, telefone);

      const resposta = {
        telefone: telefone,
        mensagem: objResposta.mensagem,
        beep: objResposta.beep,
        tipo: 'CHAT'
      }

      res.json({
        sucesso: true,
        dados: {
          resposta: resposta
        }
      });

      return;
    }

    const chaveAtendente = 'atendentezap:' + empresa.id + '_' + telefone;
    console.log('chave: ' + chaveAtendente);

    client.get(chaveAtendente, async (erro: Error, valor: string) => {
      if (valor && false) {
        console.log('bot está ativo: ' + telefone);
        return res.json(Resposta.erro('Bot já está ativo'));
      }

      const objResposta = await processeMensagemDeTexto(empresa, usuario, cliente, nome, telefone, mensagem);

      const resposta = {
        telefone: telefone,
        mensagem: objResposta.mensagem,
        beep: objResposta.beep,
        tipo: 'CHAT'
      }

      //const resposta = await new MapeadorDeMensagemBot().insiraGraph(mensagemBot);

      res.json({
        sucesso: true,
        dados: {
          resposta: resposta
        }
      });
    });
  });

*/

  /*
  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    const sessaoAberta = await ComunicadorDialogFlow.Instance.possuiSessaoAberta(empresa, telefone);

    const contato = await new MapeadorDeContato().selecioneSync({
      telefone: telefone
    }).catch( (erro) => {

    });

    if( (!empresa.cardapio.botAtivo && !sessaoAberta) ) {
      return res.json(Resposta.erro('Bot não está ativo: ' + mensagem));
    }

    if( empresa.cardapio.modoTesteBot && telefone !== ComunicadorDialogFlow.TELEFONE_TESTE ) {
      return res.json(Resposta.erro('Bot está em modo teste.'));
    }

    if( contato ) {
        const pedido = await new MapeadorDePedido().selecioneSync({
          idContato: contato.id,
          inicio: 0,
          total: 1,
          emAberto: true
        }).catch( (erro) => {
      });

      if( pedido != null ) {
        res.json(Resposta.erro('Já tem um pedido em aberto.'));

        return;
      }
    }

    if( mensagem.indexOf('oi bot') !== -1 && !Ambiente.Instance.producao) {
      clientRedis.del(`bot_${empresa.id}_${telefone}`, async(error: Error, dados: number) => {
        await processarRequest();
      });

      return;
    }

    await processarRequest();
  });
   */
});

router.post('/processe', async (req, res) => {
  console.log(req.headers);

  app(req, res);
});

router.get('/ultimasConversas', async(req, res) => {
  const mapeadorDeConversa = new MapeadorDeMensagemWhatsappWeb();

  mapeadorDeConversa.obtenhaConversas().then( (conversas) => {
    res.json(conversas);
  });
});

router.get('/ultimasMensagens', async(req, res) => {
  const telefone = decodeURIComponent(req.query.tel + '');
  const mapeadorDeMensagemWhatsappWeb = new MapeadorDeMensagemWhatsappWeb();

  console.log(telefone);

  mapeadorDeMensagemWhatsappWeb.selecioneTodas({
    id_chat: telefone,
    inicio: 0,
    total: 50
  }).then( (conversas) => {
    //inverte a ordem das conversas
    conversas.reverse();
    res.json(conversas);
  });
});

router.get('/configMensagens', async(req, res) => {
  const mapeadorDeConfigMensagemDeBot = new MapeadorDeConfigMensagemDeBot();

  const configMensagens = await mapeadorDeConfigMensagemDeBot.listeAsync({ });

  res.json({
    sucesso: true,
    data: configMensagens
  });
})

router.put('/configMensagens', async (req, res) => {
  const dados = req.body;

  const configMensagem = new ConfigMensagemDeBot();
  Object.assign(configMensagem, dados);


  new BotsService().atualize(configMensagem).then(resp => {
    console.log(resp);

    res.json(Resposta.sucesso());
  }).catch( (erro: any) => {
    console.log(erro);
    res.json(Resposta.erro(erro));
  });
});


export const Bots2Controller: Router = router;
