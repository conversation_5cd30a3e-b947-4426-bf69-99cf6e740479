import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {Ambiente} from "../service/Ambiente";
import {MapeadorDeVitrine} from "../mapeadores/MapeadorDeVitrine";
import {Vitrine} from "../domain/delivery/Vitrine";

const router: Router = Router();

function estaLogado(req: any, res: any, next: any){
  if(!req.user)
    return res.json(Resposta.erro('Faça login para realizar a operação'))

  Ambiente.Instance.determineUsuarioLogado(req.user)
  return next();
}

router.get('/', estaLogado, async (req: any, res) => {
  let vitrines = await new MapeadorDeVitrine().listeAsync({});

  res.json(Resposta.sucesso(vitrines))
});

router.get('/:id', estaLogado, async (req: any, res) => {
  let vitrine = await new MapeadorDeVitrine().selecioneSync(Number(req.params.id));

  res.json(Resposta.sucesso(vitrine))
});


router.post('/', estaLogado, async (req: any, res) => {
  const dados = req.body;

  let vitrine = Object.assign(new Vitrine(), dados )

  await new MapeadorDeVitrine().insiraGraph(vitrine);

  res.json(Resposta.sucesso({id: vitrine.id}))
})


router.put('/', estaLogado, async (req: any, res) => {
  const dados = req.body;

  let vitrine = Object.assign(new Vitrine(), dados )

  await new MapeadorDeVitrine().atualizeSync(vitrine);

  res.json(Resposta.sucesso({id: dados.id}))
})

router.put('/ative', estaLogado, async (req: any, res) => {
  let vitrine: Vitrine = await Vitrine.get(req.body.id);

  if(!vitrine) return res.json(Resposta.erro('Vitrine não existe'))

  if(vitrine.produtosNaVitrine.length < 1)
    return res.json(Resposta.erro('Para ativar vitrine insira no mínimo 1 produto.'))

  await vitrine.ative();

  res.json(Resposta.sucesso({ }))
})


router.put('/desative', estaLogado, async (req: any, res) => {
  let vitrine: Vitrine = await Vitrine.get(req.body.id);

  if(!vitrine) return res.json(Resposta.erro('Vitrine não existe'))

  await vitrine.desative();

  res.json(Resposta.sucesso({ }))
})

router.put('/subirParaTopo', estaLogado, async (req: any, res) => {
  const idVitrine: any = req.body.id;
  let mapeador = new MapeadorDeVitrine();

  let vitrine = await Vitrine.get(idVitrine);

  let vitrineTopo = await mapeador.selecioneVitrineDoTopo();

  if(vitrine.id !== vitrineTopo.id){

    mapeador.transacao( async (conexao: any, commit: any) => {
      await mapeador.atualizeSubirTopo(vitrine);
      commit( () => {
        res.json(Resposta.sucesso())
      })
    });
  } else {
    res.json(Resposta.erro('Vitrine ja está  no topo'))
  }
})

router.put('/descerParaFinal', estaLogado, async (req: any, res) => {
  const idVitrine: any = req.body.id;
  let mapeador = new MapeadorDeVitrine();

  let vitrine = await Vitrine.get(idVitrine);
  let vitrineFim = await mapeador.selecioneVitrineDoFim();

  if(vitrine.id !== vitrineFim.id){
    mapeador.transacao( async (conexao: any, commit: any) => {
      await mapeador.atualizeDescerFinal(vitrine, vitrineFim.ordem);
      commit( () => {
        res.json(Resposta.sucesso())
      })
    });
  } else {
    res.json(Resposta.erro('Vitrine ja está  fim'))
  }
})


router.put('/ordem', estaLogado, async (req: any, res) => {
  const idVitrine: any = req.body.id;
  const comando = req.body.comando;

  let vitrine = await Vitrine.get(idVitrine);
  let mapeador = new MapeadorDeVitrine();
  let ordem = vitrine.ordem;
  let vitrineAcima = comando === 'SUBIR' ?  await mapeador.selecioneVitrineAcima(ordem) :
                                            await mapeador.selecioneVitrineAbaixo(ordem)

  if(!vitrineAcima) return res.json(Resposta.erro('Vitrine ja está  o extremo'))

  vitrine.ordem = vitrineAcima.ordem;
  vitrineAcima.ordem = ordem;

  mapeador.transacao( async (conexao: any, commit: any) => {
    await mapeador.atualizeOrdem(vitrine);
    await mapeador.atualizeOrdem(vitrineAcima);
    commit( () => {
      res.json(Resposta.sucesso())
    })
  });
})

router.put('/produto/subirParaTopo', estaLogado, async (req: any, res) => {
  const idVitrine: any = req.body.idVitrine;
  const idProdutoVitrine: any = req.body.idProdutoVitrine;

  let vitrine = await Vitrine.get(idVitrine);
  let produtoNaVitrine: any  = vitrine.produtosNaVitrine.find((pnv: any) => pnv.id === idProdutoVitrine);

  await new MapeadorDeVitrine().atualizeSubirProdutoTopo(vitrine, produtoNaVitrine);

  res.json(Resposta.sucesso())

})

router.put('/produto/descerParaFinal', estaLogado, async (req: any, res) => {
  const idVitrine: any = req.body.idVitrine;
  const idProdutoVitrine: any = req.body.idProdutoVitrine;

  let vitrine = await Vitrine.get(idVitrine);
  let produtoNaVitrine: any  = vitrine.produtosNaVitrine.find((pnv: any) => pnv.id === idProdutoVitrine);
  let ultimaPosicao = vitrine.produtosNaVitrine[vitrine.produtosNaVitrine.length - 1].posicao;

  await new MapeadorDeVitrine().atualizeDescerProdutoFinal(vitrine, produtoNaVitrine, ultimaPosicao);

  res.json(Resposta.sucesso())

})

router.put('/produto/ordem', estaLogado, async (req: any, res) => {
  const idVitrine: any = req.body.idVitrine;
  const idProdutoVitrine: any = req.body.idProdutoVitrine;
  const comando = req.body.comando;
  let mapeador = new MapeadorDeVitrine();
  let vitrine = await Vitrine.get(idVitrine);
  let indice = 0;

  for(let i = 0; i <  vitrine.produtosNaVitrine.length; i++){
    if( vitrine.produtosNaVitrine[i].id === idProdutoVitrine)
      indice = i;
  }

  let produtoNaVitrine = vitrine.produtosNaVitrine[indice];
  let posicao = produtoNaVitrine.posicao;
  let produtoNaVitrimeAcima = comando === 'SUBIR' ?  vitrine.produtosNaVitrine[indice - 1] : vitrine.produtosNaVitrine[indice + 1]

  if(!produtoNaVitrimeAcima) return res.json(Resposta.erro('Vitrine ja está  o extremo'))

  produtoNaVitrine.posicao = produtoNaVitrimeAcima.posicao;
  produtoNaVitrimeAcima.posicao = posicao;

  mapeador.transacao( async (conexao: any, commit: any) => {
    await mapeador.atualizePosicaoProduto(vitrine, produtoNaVitrine);
    await mapeador.atualizePosicaoProduto(vitrine, produtoNaVitrimeAcima);
    commit( () => {
      res.json(Resposta.sucesso())
    })
  });
})

router.post('/:id/produtos/add', estaLogado, async (req: any, res) => {
  let vitrine = req.body.vitrine,
    idsProdutos = req.body.produtos;

  if(!idsProdutos.length)
    return res.json(Resposta.erro("Nenhum produto informado"))

  await new MapeadorDeVitrine().insiraNaListaProdutos(vitrine, idsProdutos);

  res.json(Resposta.sucesso({ total: idsProdutos.length  }));
})


router.post('/:id/produtos/del', estaLogado, async (req: any, res) => {
  let vitrine = {id: Number(req.params.id)},
    produto = req.body;

  await new MapeadorDeVitrine().removaDaLista(vitrine, produto);

  res.json(Resposta.sucesso({  }));
})


export const VitinesController: Router = router;
