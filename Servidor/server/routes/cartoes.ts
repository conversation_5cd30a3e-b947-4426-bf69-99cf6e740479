import {Router} from "express";
import {MapeadorDeCartao} from "../mapeadores/MapeadorDeCartao";
import {Resposta} from "../utils/Resposta";
import {Contato} from "../domain/Contato";
import {CartaoService} from "../service/CartaoService";
import {Cartao} from "../domain/Cartao";
import {MapeadorDePontuacaoRegistrada} from "../mapeadores/MapeadorDePontuacaoRegistrada";
import {DTOPontuacaoGanha} from "../lib/dto/DTOPontuacaoGanha";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {PontuacaoRegistrada} from "../domain/PontuacaoRegistrada";
import {ExecutorAsync} from "../utils/ExecutorAsync";
// @ts-ignore
import _ = require("underscore");
import {ContatoService} from "../service/ContatoService";
import * as moment from "moment";
import {MapeadorDeAcaoDoContato} from "../mapeadores/MapeadorDeAcaoDoContato";
import {AcaoDoContato} from "../domain/AcaoDoContato";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {Pedido} from "../domain/delivery/Pedido";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
const async = require('async');

function estaLogado(req: any, res: any, next: any){
  if(!req.user)
    return res.json(Resposta.erro('Faça login para realizar a operação'))

  return next();
}


const router: Router = Router();

router.get('/', async (req, res) => {
  new MapeadorDeCartao().listeAsync({}).then( cartoes => {
    res.json(Resposta.sucesso(cartoes));
  });
});

router.post('/', estaLogado, async (req: any, res) => {
  const dados = req.body,
        dadosContato = dados.contato;
  let contato =  new Contato(dadosContato.id, dadosContato.nome, dadosContato.telefone, dadosContato.genero,
                            dadosContato.dataNascimento , dadosContato.cpf, dadosContato.email, dadosContato.codigoPais);

  if(contato.id) {
    contato = await new MapeadorDeContato().selecioneSync(dadosContato.id);

    contato.nome = dadosContato.nome;
    contato.telefone = dadosContato.telefone;
    contato.sexo = dadosContato.genero;
    if( dadosContato.dataNascimento ) {
      contato.dataNascimento = new Date(dadosContato.dataNascimento);
    }
    contato.email = dadosContato.email;
    contato.cpf = dadosContato.cpf;
  }

  contato.ultimaVisita = new Date();

  if( dadosContato.quemIndicou && dadosContato.quemIndicou.id ) {
    contato.quemIndicou = new Contato(dadosContato.quemIndicou.id, '', '');
  }

  if(dados.plano || dados.id){
    const cartao = new Cartao(dados.id, contato, dados.plano, 0);

    cartao.empresa = req.empresa;

    new CartaoService().salve(cartao).then(resp => {
      res.json(Resposta.sucesso({
        id: cartao.id,
        idContato: contato.id
      }));
    }).catch(erro => {
      res.json(Resposta.erro(erro));
    });
  } else {
    await new ContatoService().salve(contato).then((erro) => {
      res.json(Resposta.sucesso({
        idContato: contato.id
      }));
    }).catch(erro => {
      res.json(Resposta.erro(erro));
    });
  }
});

router.put('/', estaLogado, async (req, res) => {
  const dados = req.body,
    dadosContato = dados.contato;

  res.json(Resposta.erro('Contato já tem um cartão ativo'));
});

router.get('/comprovante/:codigo', async (req, res) => {
  let pontuacaoRegistrada: any = await new MapeadorDePontuacaoRegistrada().selecioneSync({ codigo: req.params.codigo } );

  res.json(Resposta.sucesso(new DTOPontuacaoGanha(pontuacaoRegistrada)))
});


router.get('/pontuacao/vencidadas/execute', async (req: any, res: any) => {
  let mapeador = new MapeadorDePontuacaoRegistrada();

  mapeador.desativeMultiCliente();

  let pontuacoesVencidas: any = await mapeador.listePontuacoesVencidas(null,  req.empresa.id);

  console.log('executar tarefa')

  ExecutorAsync.execute( async (cb: Function) => {
    let contexto = require('domain').active.contexto;

    let cartaoService = new CartaoService();

    for(let i = 0; i < pontuacoesVencidas.length; i++){
      let pontuacoes: any = pontuacoesVencidas[i].pontuacoes;
      let cartao =  pontuacoesVencidas[i].cartao;

      contexto.idEmpresa = cartao.empresa.id;
      contexto.empresa  = { id: cartao.empresa.id };

      await cartaoService.registrePontuacaoVencida(cartao, pontuacoes);
    }

    cb();

  }, () => {}, 0)



  res.json(Resposta.sucesso('Disparou: ' + pontuacoesVencidas.length))
});

router.get('/pontuacao/usadas/corrija', async (req, res) => {
  let mapeador = new MapeadorDePontuacaoRegistrada();

  mapeador.desativeMultiCliente();

  let pontuacoes = await  mapeador.listePontuacaoCorrigir();

  console.log(pontuacoes.length);

  let pontuacoesMap = _.groupBy(pontuacoes, (pontuacao: any) => pontuacao.cartao.empresa.id);

  let resposta: any = { empresas: {}}

  for(let empresaId of Object.keys(pontuacoesMap)){
    //if(empresaId !== '14') continue;
    console.log(String(`->>>>>Processar empresa: ${empresaId} total: ${pontuacoesMap[empresaId].length}`))
    resposta.empresas[empresaId] = { cartoes: [], erros: []}
    let contexto = require('domain').active.contexto;
    contexto.idEmpresa = Number(empresaId);
    let pontuacoesMapCartao = _.groupBy(pontuacoesMap[empresaId], (pontuacao: any) => pontuacao.cartao.id);

    for(let idCartao of Object.keys(pontuacoesMapCartao)){
     // if(idCartao.toString() !== '33205') continue;
      let pontuacoesCorrigir: any = pontuacoesMapCartao[idCartao];
      let pontuacoesDisponiveis: any = await new MapeadorDePontuacaoRegistrada().listePontuacoesDisponveis({id: idCartao});
      let pontuacoesUsadas: any =  [];

      let valorCorrigir = 0;

      pontuacoesCorrigir.forEach((pontuacao: any) => {
        let valorUsadoErrado =
          pontuacao.pontosUsados > pontuacao.pontosVencidos ? pontuacao.pontosVencidos : pontuacao.pontosUsados ;

        valorCorrigir += valorUsadoErrado;

        pontuacao.pontosUsados -= valorUsadoErrado;

      })

      let pontosUsados = 0,  saldoCartao = pontuacoesCorrigir[0].cartao.pontos;
      let totalDisponivel = pontuacoesDisponiveis.reduce(
        (sum: number, pontuacao: any) => sum +  ( pontuacao.pontos - pontuacao.pontosUsados - pontuacao.pontosVencidos), 0)

      if(  (totalDisponivel - valorCorrigir) < saldoCartao){
        valorCorrigir = totalDisponivel - saldoCartao
      }

      pontuacoesDisponiveis.forEach(  (pontuacaoRegistrada: PontuacaoRegistrada) => {
        let pontosDisponiveis =  ( pontuacaoRegistrada.pontos - pontuacaoRegistrada.pontosUsados - pontuacaoRegistrada.pontosVencidos);

        if(valorCorrigir > 0 && pontosDisponiveis > 0){
          if(pontosDisponiveis > valorCorrigir){
            pontuacaoRegistrada.pontosUsados += valorCorrigir;
            pontosUsados += valorCorrigir;
            valorCorrigir = 0;
          } else {
            pontuacaoRegistrada.pontosUsados += pontosDisponiveis;
            pontosUsados += pontosDisponiveis;
            valorCorrigir -= pontosDisponiveis;
          }

          pontuacoesUsadas.push(pontuacaoRegistrada)
        }
      })

      resposta.empresas[empresaId].cartoes.push({idCartao: idCartao,   pontosUsados: pontosUsados})

      for(let pontuacaoRegistrada of pontuacoesUsadas){
        await new MapeadorDePontuacaoRegistrada().atualizePontosUsados(pontuacaoRegistrada)
      }

      for(let pontuacaoRegistrada of pontuacoesCorrigir){
        await new MapeadorDePontuacaoRegistrada().atualizePontosUsados(pontuacaoRegistrada)
      }
    }

  }

  res.json(resposta)
});

router.get('/reprocessar/pontuacoes', async (req: any, res: any) => {
   let acoesContato = await new MapeadorDeAcaoDoContato().listeAsync({reprocessarPontos: true})

  console.log('Total acoes contato reprocessar: ' + acoesContato.length)

  let mapAcoes =  _.groupBy(acoesContato, (acao: any) => acao.cartao.id);
  let keys  = Object.keys(mapAcoes);

  let reprocessar: any = [], horarioCorteVencimentoPontos = moment('2021-08-01 23:59:59').toDate();

  for(let i = 0; i < keys.length ; i++){
    let idCartao = keys[i], acaoModelo  = mapAcoes[idCartao][0];
    let cartao: any =  acaoModelo.cartao;
    let contato: any =  acaoModelo.contato;
    let ultimaVisita = contato.ultimaVisita;
    cartao.pontos = 0;
    console.log('-->Processar cartao: ' + cartao.id)

    let acoes: any =  mapAcoes[idCartao];
    let pontuacoesRegistradas = [] , acoesPtosExpiraram: any = [];

    for( let idx = 0; idx < acoes.length; idx++ ){
       let acao = acoes[idx],
         ultima = idx === acoes.length - 1,
         pontosTrocar = Number(acao.pontos);

      if(acao.trocouPontos()){
      //  console.log('Troca ' + acao.mensagem  + " -> " + acao.pontos + '/' + cartao.pontos)
        cartao.pontos -= acao.pontos;

        while (pontosTrocar > 0){
          let pontuacao = pontuacoesRegistradas.find((item: any) => item.obtenhaPontosUsar() )

          if(pontuacao){
            let ptsUsar = pontuacao.obtenhaPontosUsar();

            pontuacao.pontosUsados += ptsUsar

            pontosTrocar -= ptsUsar;
          } else {
            console.log('Não tem pontos disponivel para uso: ' + idCartao + ' saldo: ' + cartao.pontos)
            pontosTrocar = 0;
          }
        }
      }

       if(acao.ganhouPontos()){
         cartao.pontos += acao.pontos;
        // console.log('Pontuou ' + acao.mensagem  + " -> " + acao.pontos + '/' + cartao.pontos)
         let novaPontuacao = new PontuacaoRegistrada();

         novaPontuacao.pontos = acao.pontos;
         novaPontuacao.cartao = acao.cartao;
         novaPontuacao.horario = acao.horario;

         if(moment(acao.horario).isAfter(horarioCorteVencimentoPontos)){
           novaPontuacao.dataVencimento = moment(acao.horario).add(90, 'days').toDate();
         } else {
           novaPontuacao.dataVencimento = null;
         }

         if(pontuacoesRegistradas.length){
           let horarioAnterior = ultima ? new Date() : pontuacoesRegistradas[ pontuacoesRegistradas.length - 1].horario;

           let diasretorno = moment(acao.horario).diff(horarioAnterior, 'days');

           if(diasretorno < 90){
             //renovar vencimento anteriores
             pontuacoesRegistradas.forEach((pontuacao: any) => {
                  if(pontuacao.dataVencimento && !pontuacao.pontosVencidos)
                     pontuacao.dataVencimento =  novaPontuacao.dataVencimento;
             })
           } else {
             //console.log('pts venceu , retorno somente com ' + diasretorno + ' dias')
             //vencer pontuação anterior
             pontuacoesRegistradas.forEach((pontuacao: any) => {
               let ptsusar =  pontuacao.obtenhaPontosUsar();

               if(pontuacao.dataVencimento && !pontuacao.pontosVencidos && ptsusar){
                 pontuacao.pontosVencidos +=  pontuacao.obtenhaPontosUsar();
                 pontuacao.cartao.contato = contato;
                 const acaoDoContato: AcaoDoContato = AcaoDoContato.venceuPontos(pontuacao);
                 acoesPtosExpiraram.push(acaoDoContato)

                 cartao.pontos  -=  pontuacao.pontosVencidos;
               }
             })
           }
         }

         pontuacoesRegistradas.push(novaPontuacao)
       }
    }

    let naoVoltaAmais90dias = moment().diff(moment(ultimaVisita), 'days') > 90;

    if(naoVoltaAmais90dias){
      let pontuacoesVenceu = pontuacoesRegistradas.filter((pontuacao: any) =>  pontuacao.obtenhaPontosUsar() )

      pontuacoesVenceu.forEach((pontuacao: any) => {
        pontuacao.pontosVencidos += pontuacao.obtenhaPontosUsar();
        pontuacao.cartao.contato = contato;
        pontuacao.dataVencimento = moment(ultimaVisita).add(90, 'days').toDate();

        const acaoDoContato: AcaoDoContato = AcaoDoContato.venceuPontos(pontuacao);
        acoesPtosExpiraram.push(acaoDoContato)
      })

      cartao.pontos = 0;
    }

    reprocessar.push({ cartao: cartao,  pontuacoes: pontuacoesRegistradas, acoes: acoesPtosExpiraram})
  }

  let resposta: any = { total: keys.length  };
  const mapeadorPontuacaoRegistrada = new MapeadorDePontuacaoRegistrada();
  const mapeadorDeAcaoDoContato = new MapeadorDeAcaoDoContato();
  const mapeadorDeCartao = new MapeadorDeCartao();

  console.log('Total cartoes reprocessar: ' + reprocessar.length)

  async.forEachSeries(reprocessar, (dadosSalvar: any, cb: any) => {
    let  cartao = dadosSalvar.cartao,
      pontuacoesRegistradas = dadosSalvar.pontuacoes,
      acoesExpiraram = dadosSalvar.acoes;


    if(cartao.pontos < 0) cartao.pontos = 0;

    new MapeadorDeCartao().transacao( async (conexao: any, commit: any) => {
      console.log(String(`Atualizar cartão ${cartao.id} para ${cartao.pontos} pts`))
      await mapeadorPontuacaoRegistrada.removaTodosCartao(cartao);

      for(let i = 0; i < pontuacoesRegistradas.length; i++)
        await mapeadorPontuacaoRegistrada.insiraSync(pontuacoesRegistradas[i])


      await mapeadorDeAcaoDoContato.removaAcoesPontosExpirados(cartao);

      for(let i = 0; i < acoesExpiraram.length; i++)
        await mapeadorDeAcaoDoContato.insiraSync(acoesExpiraram[i]);

      await mapeadorDeCartao.atualizeSync(cartao);
      commit( () => {
        cb();
      })
    })
  }, () => {
    let cartoesPositivo = reprocessar.filter((item: any) => item.cartao.pontos > 0);

    console.log('Total cartoes positivos: ' + cartoesPositivo.length)

    res.json(resposta)
  })



});

export const CartoesController: Router = router;
