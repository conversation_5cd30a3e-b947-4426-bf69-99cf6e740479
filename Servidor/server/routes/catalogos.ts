import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";
import {Resposta} from "../utils/Resposta";
import {Router} from "express";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeCatalogoDaRede} from "../mapeadores/MapeadorDeCatalogoDaRede";
import {DTOCatalogoDaRede} from "../domain/catalogo/DTOCatalogoDaRede";
import {CatalogoDaRede} from "../domain/catalogo/CatalogoDaRede";
import {MapeadorDeCatalogo} from "../mapeadores/MapeadorDeCatalogo";
import {Catalogo} from "../domain/catalogo/Catalogo";
import {MapeadorDeCategoria} from "../mapeadores/MapeadorDeCategoria";
import {Categoria} from "../domain/delivery/Categoria";
import {EmpresaService} from "../service/EmpresaService";

// @ts-ignore
import _ = require("underscore");
import {Empresa} from "../domain/Empresa";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {FiltroTelaProdutos} from "../utils/FiltroTelaProdutos";
import {Produto} from "../domain/Produto";
import {ImportadorProdutosUtils} from "../lib/integracao/ImportadorProdutosUtils";
import {MapeadorDeDisponibilidade} from "../mapeadores/MapeadorDeDisponibilidade";
import {MapeadorDeOpcaoDeAdicionalDeProduto} from "../mapeadores/MapeadorDeOpcaoDeAdicionalDeProduto";
import {OpcaoDeAdicionalDTO} from "../domain/utils/OpcaoDeAdicionalDTO";
import {MapeadorDeAdicionalDeProduto} from "../mapeadores/MapeadorDeAdicionalDeProduto";
import {AdicionalDeProduto} from "../domain/delivery/AdicionalDeProduto";
import {ImportadorProduto} from "../lib/integracao/ImportadorProduto";
import {RegistroDeOperacaoService} from "../service/RegistroDeOperacaoService";
import {Ambiente} from "../service/Ambiente";

const router: Router = Router();

function ehAdminRede(req: any, res: any, next: any){
  let usuario: any = req.user;

  let mapeador = new MapeadorDeUsuario()

  if(!usuario)  return res.json(Resposta.erro('Faça login para realizar a operação'))

  mapeador.selecioneSync(usuario).then((usuarioAtualizado: any) => {
    if(!usuarioAtualizado || !usuarioAtualizado.adminRede)   return res.json(Resposta.erro('Operação não permitida'))

    return next();
  });
}


function usuarioAdmin(req: any, res: any, next: any){
  if(req.user.admin)
    return  next();

  res.json(Resposta.erro('Operação não permitida'))
}

router.get('/', ehAdminRede, async(req: any, res: any) => {
  let empresa: any = req.empresa;
  let query: any = {rede: empresa.dadosRede.grupo};

  if(req.query.id)  query.id = req.query.id
  console.log(query)

  let catalogos = await new MapeadorDeCatalogoDaRede().listeAsync(query)

  let dtoCatalogos = catalogos.map((catalogoDaRede: CatalogoDaRede) => new DTOCatalogoDaRede(catalogoDaRede) )

  res.json(Resposta.sucesso(dtoCatalogos))

});

router.get('/:id', ehAdminRede, async(req: any, res: any) => {
  const idCatalogo: any = req.params.id;

  let catalogo = await new MapeadorDeCatalogo().selecioneSync({id: idCatalogo})
  catalogo.categorias = await new MapeadorDeCategoria(catalogo).listeAsync( { idCatalogo: catalogo.id})
  catalogo.disponibilidades =
    await new MapeadorDeDisponibilidade( catalogo).listeAsync({idCatalogo:  catalogo.id});

  res.json(Resposta.sucesso(catalogo))
})

router.put('/:id/categorias/posicoes', async(req: any, res) => {
  const categorias = req.body;

  let catalogo = await new MapeadorDeCatalogo().selecioneSync(Number(req.params.id));

  for (let i = 0; i < categorias.length; i++)
    await new MapeadorDeCategoria(catalogo).atualizeSync(categorias[i])



  const mapeadorDeProduto = new MapeadorDeProduto(catalogo);
  await mapeadorDeProduto.recalculeOrdens(catalogo);

  await mapeadorDeProduto.removaCacheProdutos();
  await new MapeadorDeEmpresa().removaListaDeCategoriasDaCache(req.empresa);

  res.json(Resposta.sucesso( ))
})

router.post('/:id/categorias/reset', async(req: any, res) => {
  let catagorias = req.empresa.catalogo.categorias;
  const mapeador = new MapeadorDeCategoria(req.empresa.catalogo);
  for (let i = 0; i < catagorias.length; i++){
    catagorias[i].posicao = i + 1;
    await mapeador.atualizeSync( catagorias[i])
  }

  await new MapeadorDeEmpresa().removaListaDeCategoriasDaCache(req.empresa);

  res.json(Resposta.sucesso( catagorias))

})

router.delete('/:id/categoria/:idCategoria', async(req: any, res) => {
  const dados: any = {id: Number(req.params.idCategoria)} ;

  if(!dados.id) return res.json(Resposta.erro("É necessário informar o id da categoria que será removida."));

  let catalogo = await new MapeadorDeCatalogo().selecioneSync(Number(req.params.id));
  catalogo.categorias = await new MapeadorDeCategoria(catalogo).listeAsync( { idCatalogo: catalogo.id})
  let existente = _.find(catalogo.categorias, (cat: Categoria) =>  cat.id === dados.id);

  if(!existente)
    return res.json(Resposta.erro('Não foi localizada uma categoria com o id: ' + dados.id))

  dados.catalogo = catalogo

  new MapeadorDeCategoria(catalogo).removaCategoria(dados).then(async (resultado: any) => {
    //new MapeadorDeEmpresa().removaDasCaches(empresa)
    await new MapeadorDeProduto(catalogo).removaCacheProdutos();
    await new MapeadorDeEmpresa().removaListaDeCategoriasDaCacheDoCatalogo(catalogo);

    res.json(Resposta.sucesso(dados))
  })


})


router.post('/:id/categoria', async(req: any, res) => {
  const dados = req.body;

  let catalogo = await new MapeadorDeCatalogo().selecioneSync(Number(req.params.id));
  catalogo.categorias = await new MapeadorDeCategoria(catalogo).listeAsync( { idCatalogo: catalogo.id})
  let existente = _.find(catalogo.categorias, (cat: Categoria) =>  cat.nome.toLowerCase() === dados.nome.toLowerCase() );

  if(existente && existente.id !== dados.id)
    return res.json(Resposta.erro('Já existe um categoria com esse nome: ' + existente.nome))

  let categoria;

  if(!dados.id){
    categoria = new Categoria(dados.id, dados.nome.trim(), catalogo);

    categoria.imagem = dados.imagem;
    categoria.posicao = catalogo.categorias.length + 1;


  } else {
    categoria = catalogo.categorias.find( (cat: any) => cat.id === dados.id);
    categoria.nome = dados.nome;
    categoria.imagem = dados.imagem;
  }

  categoria.impressoras = dados.impressoras;

  let empresa = dados.rede ? null :  req.empresa

  await new EmpresaService().salveCategoria(catalogo, categoria, empresa);

  res.json(Resposta.sucesso(categoria))

})




router.post('/empresa/:idEmpresa/modelo', ehAdminRede, async(req: any, res: any) => {
  let dados = req.body;
  const idEmpresa = req.params.idEmpresa

  console.log(dados)

  if(!dados.modeloCatalogoDaRede || !dados.modeloCatalogoDaRede.id)
    return res.json(Resposta.erro("Nenhum modelo informado "))

  let mapeadorDeEmpresa = new MapeadorDeEmpresa()

  mapeadorDeEmpresa.selecioneSync({id: idEmpresa}).then( async (empresa: Empresa) => {
    if(!empresa)   return res.json(Resposta.erro("Não existe empresa ativa com o id " + idEmpresa))

    empresa.modeloCatalogoDaRede = dados.modeloCatalogoDaRede;

    await mapeadorDeEmpresa.atualizeModeloCatalogo(empresa);

    res.json(Resposta.sucesso())
  })
})

router.post('/altere/empresa/:idEmpresa/:idCatalogo', ehAdminRede, async(req: any, res: any) => {
  const idEmpresa = req.params.idEmpresa
  const idCatalogo = req.params.idCatalogo

  let mapeadorDeEmpresa = new MapeadorDeEmpresa()

  mapeadorDeEmpresa.selecioneSync({id: idEmpresa}).then((empresa: Empresa) => {
    if(!empresa)
      return res.json(Resposta.erro("Não existe empresa ativa com o id " + idEmpresa))

    let mapeadorDeCatalogo = new MapeadorDeCatalogo()

    mapeadorDeCatalogo.selecioneSync({id: idCatalogo}).then((catalogo: Catalogo) => {
      if(!catalogo)
        return res.json(Resposta.erro("Não existe catálogo com o id" + idCatalogo))

      empresa.catalogo = catalogo

      mapeadorDeEmpresa.atualizeCatalogo(empresa).then((atualizou: any) => {
        if(!atualizou)
          return res.json(Resposta.erro ("A empresa já estava com o catálogo " + idCatalogo +  " configurado."))

        mapeadorDeEmpresa.removaListaDeCategoriasDaCache(empresa)

        res.json(Resposta.sucesso("O catálogo foi atualizado com sucesso"))
      })
    })
  })
})

router.delete('/:rede/:id', ehAdminRede, async(req: any, res: any) => {
  let rede = req.params.rede;
  let id = req.params.id;

  let usuario: any = req.user;

  if(usuario.adminRede !== rede)
    return res.json(Resposta.erro("Operação não permitida"))


  let mapeadorDeCatalogoDaRede = new MapeadorDeCatalogoDaRede();


  let catalogoDaRede = await mapeadorDeCatalogoDaRede.selecioneSync({id: id, rede: rede});

  if(!catalogoDaRede)
    return res.json(Resposta.erro("Não foi encontrado catálogo da rede com id " + id))

  mapeadorDeCatalogoDaRede.removaAsync(catalogoDaRede).then((removido: any) => {
    res.json(Resposta.sucesso(removido))
  })
})

router.post('/', ehAdminRede, async (req: any, res) => {
  const dados = req.body;
  let catalogo: Catalogo = new Catalogo()

  catalogo.nome = dados.nome
  catalogo.ativo = true
  catalogo.criacao = new Date()
  catalogo.atualizacao = catalogo.criacao
  catalogo.criador = req.user
  catalogo.disponibilidadePorEmpresa   = dados.disponibilidadePorEmpresa
  catalogo.precoPorEmpresa = dados.precoPorEmpresa
  catalogo.compartilhado = dados.compartilhado

  let mapeadorDeCatalogo = new MapeadorDeCatalogo()

  mapeadorDeCatalogo.insiraGraph(catalogo).then((inseriu: any) => {
    let catalogoDaRede = new CatalogoDaRede()
    catalogoDaRede.catalogo = catalogo
    catalogoDaRede.rede = req.empresa.dadosRede.grupo

    let mapeadorDeCatalogoDaRede = new MapeadorDeCatalogoDaRede()

    mapeadorDeCatalogoDaRede.insiraGraph(catalogoDaRede).then((inseriu2: any) => {
      res.json(Resposta.sucesso(catalogoDaRede))
    })
  })
})

router.put('/', ehAdminRede, async (req: any, res) => {
  const dados = req.body;

  let mapeadorDeCatalogo = new MapeadorDeCatalogo()
  let mapeadorDeCatalogoDaRede = new MapeadorDeCatalogoDaRede()

  mapeadorDeCatalogoDaRede.selecioneSync(dados.id).then((catalogoDaRede: CatalogoDaRede) => {
    if(!catalogoDaRede) {
      res.json(Resposta.erro("Não foi encontrado catálogo da rede com id " + dados.id))
      return
    }
    let catalogo: Catalogo = catalogoDaRede.catalogo
    catalogo.nome = dados.nome
    catalogo.atualizacao = new Date()
    catalogo.criador = req.user
    catalogo.disponibilidadePorEmpresa  = dados.disponibilidadePorEmpresa
    catalogo.precoPorEmpresa = dados.precoPorEmpresa
    catalogo.compartilhado = dados.compartilhado ? dados.compartilhado : false

    catalogoDaRede.catalogo = catalogo

    mapeadorDeCatalogo.atualizeSync(catalogo).then((inseriu2: any) => {
      res.json(Resposta.sucesso(catalogoDaRede))
    })
  })



})


router.get('/produtos-rede/:idCatalogo', async (req: any, res) => {
  let catalogoRede =    await new MapeadorDeCatalogoDaRede().selecioneSync(req.params.idCatalogo);

  let produtos = await new MapeadorDeProduto(catalogoRede.catalogo).listeAsync({agora: new Date().getTime(), sincronizarModelo: true});

  let categorias: any = [];

  produtos.forEach( (produto: Produto) => {
    if(!categorias.find( (categoria:  any) =>   produto.categoria.id === categoria.id  ))
      categorias.push(produto.categoria)
  })

  res.json(Resposta.sucesso({ produtos: produtos, categorias: categorias}))
});

router.get('/produtos-rede/me/novos', async (req: any, res) => {
  let empresa = req.empresa;
  let sincronizarPrecos = req.query.precos;
  let sincronizarImagens = req.query.imagens;

  if(!empresa.modeloCatalogoDaRede)
    return res.json(Resposta.erro('Nenhuma modelo de catalógo configurado'))

  let produtosErpIndisponiveis: any = [];

  if( empresa.integracaoDelivery){
    let service = empresa.integracaoDelivery.obtenhaService();

    produtosErpIndisponiveis = await service.listeProdutosIndisponiveis( ).catch( (err: any) => {
      res.json(Resposta.erro(err))
    })

  }

  if(produtosErpIndisponiveis){
    let produtosImportar =
      await new MapeadorDeProduto(empresa.modeloCatalogoDaRede.catalogo).listeAsync({ agora: new Date().getTime()});

    ImportadorProdutosUtils.removaIds(produtosImportar);

    let resposta: any =
      await ImportadorProdutosUtils.classifiqueProdutosImportar(empresa.catalogo, produtosImportar, produtosErpIndisponiveis,
        sincronizarPrecos, sincronizarImagens)

    res.json(Resposta.sucesso(resposta))
  }

});


router.get('/:idCatalogo/complementos', async (req: any, res) => {
  let dados: any = new FiltroTelaProdutos(req.query, req.empresa).toSqlQuery();

  dados.idCatalogo = Number( req.params.idCatalogo)

  let opcoes: Array<OpcaoDeAdicionalDTO> = await new MapeadorDeOpcaoDeAdicionalDeProduto().listeComplementos(dados);

  res.json(Resposta.sucesso({
    opcoes: opcoes,
    total: 0
  }));

});


router.get('/:idCatalogo/produtos', async (req: any, res) => {
  let dados: any = new FiltroTelaProdutos(req.query, req.empresa).toSqlQuery();

  let catalogo = await new MapeadorDeCatalogo().selecioneSync({id: req.params.idCatalogo})

  if((req.empresa.catalogo.id === catalogo.id) && !dados.rede)
    dados.idEmpresa = req.empresa.id

  new MapeadorDeProduto(catalogo).listeAsync(dados).then( (produtos: any[]) => {
    for(let produto of produtos) {
      let valorMinimo = produto.obtenhaValorMinimo()

      if(valorMinimo > produto.preco)
        produto.valorMinimo = valorMinimo

       produto.erroDisponivel =
          produto.obtenhaErroNaoDisponivel(req.empresa.estoqueVinculadoProduto)

      if(dados.termo && produto.camposAdicionais) {
        let termoDebusca = dados.termo.toLowerCase().replaceAll('+', '').replaceAll('*', '')
        let camposAdicionaisFiltrados = []

        for(let campoAdicional of produto.camposAdicionais) {
          let opcoes = campoAdicional.opcoesDisponiveis
          campoAdicional.opcoesDisponiveis = []

          if(!opcoes) continue;

          for(let opcao of opcoes)
            if(opcao.nome.toLowerCase().indexOf(termoDebusca) >= 0)
              campoAdicional.opcoesDisponiveis.push(opcao)

          if(campoAdicional.opcoesDisponiveis.length > 0)
            camposAdicionaisFiltrados.push(campoAdicional)
        }


        produto.camposAdicionais = camposAdicionaisFiltrados
      }

    }


    new MapeadorDeProduto(catalogo).obtenhaQtdeProdutos(dados).then( (qtde: any) => {
      res.json(Resposta.sucesso({
        produtos: produtos,
        total: qtde
      }));
    });
  });
});


router.get('/resumo/:id', async (req: any, res: any) => {
  let catalogo: any  = {id: Number(req.params.id)};

  let quantidadeProdutosDaLoja: number =
    await new MapeadorDeProduto(req.empresa.catalogo).obtenhaQtdeProdutos({idCatalogo: req.empresa.catalogo.id});

  let produtos = await new MapeadorDeProduto(catalogo).listeAsync({todos: true});
  let categorias: any = [];

  let resumo: any = { totalCategorias: 0, totalProdutos: 0, categorias: [], quantidadeProdutosDaLoja: quantidadeProdutosDaLoja};

  let mapCategorias: any = {};

  produtos.forEach( (produto: any) => {
     if(produto && produto.categoria){
       if(!mapCategorias[produto.categoria.id]){
         mapCategorias[produto.categoria.id] = { nome: produto.categoria.nome, produtos: 0};
         resumo.categorias.push(mapCategorias[produto.categoria.id])
       }

       mapCategorias[produto.categoria.id].produtos++;
     }
  })

  resumo.totalProdutos = produtos.length;
  resumo.totalCategorias =  Object.keys(mapCategorias).length;

  res.json(Resposta.sucesso(resumo))

});

router.get('/adicionais/ordem/corrija', async (req: any, res) => {

  let mapaPorCatalogo: any = {}, resposta: any = [];

  console.log('buscando produtos reeordenar...')


  let produtosReordenar: Array<Produto> = await new MapeadorDeProduto(req.empresa.catalogo).listeProdutosReordenarAdicionais();
  console.log('total produtos reordenar: ' + produtosReordenar.length)

  let catalogos: Array<number> = produtosReordenar.map((item: any) => item.catalogo.id);

  // Remover duplicatas
  catalogos = Array.from(new Set(catalogos));

  let compartilhados =  produtosReordenar.filter((item: any) => {
        const adicionalCompartilhado =  item.camposAdicionais.find((adicional: AdicionalDeProduto) => adicional.compartilhado);

        return adicionalCompartilhado != null;
  })

  console.log(compartilhados[0])

  for (const idCatalogo of catalogos) {
     let produtos: any = produtosReordenar.filter((item: any) => item.catalogo.id === idCatalogo);

     //if(idCatalogo === 657){
       console.log(`total produtos catalogo ${idCatalogo} reordenar: ${produtos.length}`)
       let catalogo: any  = {id: idCatalogo}
       let mapeador = await new MapeadorDeProduto(catalogo)

       for(let i = 0;  i <  produtos.length; i++){
         console.log(`reordenando adicionais do produto ${produtos[i].nome}`)
         await mapeador.reordeneAdicionaisProduto(produtos[i].id);
       }

       resposta.push({catalogo: idCatalogo, total: produtos.length, produtos: produtos.map( (item: any) => {
           return { id: item.id, nome: item.nome};
         }) });
   //  }

  }


  res.json(Resposta.sucesso(resposta))

})


router.get('/copie/de/:origem', usuarioAdmin, async(req: any, res: any) => {
  let idOrigem = req.params.origem;
  let empresaDestino = req.empresa;

  new MapeadorDeEmpresa().selecioneSync({id: idOrigem}).then((empresaOrigem: Empresa) => {
    if(!empresaOrigem)
      return res.json('Não localizamos a empresa com id:' + idOrigem)

    let importador = new ImportadorProduto();

    importador.copieProdutos(empresaOrigem.catalogo, empresaDestino.catalogo, req.user).then(async (resposta) => {
      await new MapeadorDeEmpresa().removaDasCaches(empresaDestino, true);

      return res.json(Resposta.sucesso(resposta))
    })
  })
})

export const CatalogosController: Router = router;
