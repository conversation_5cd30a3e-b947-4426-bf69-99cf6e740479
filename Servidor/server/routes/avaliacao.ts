import {Router} from "express";
import {AvaliacaoPedido} from "../domain/delivery/AvaliacaoPedido";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeAvaliacaoDePedido} from "../mapeadores/MapeadorDeAvaliacaoDePedido";
import {DTOPedido} from "../lib/dto/DTOPedido";
import {DateUtils} from "../lib/DateUtils";
let moment = require('moment');

const router: Router = Router();

router.post('/envie', async (req: any, res: any) => {
  const dados: any = req.body;
  const avaliacao: any = dados.avaliacao;
  const pedido = await new MapeadorDePedido().selecioneSync({id: dados.pedido.id});

  if( !pedido ) {
    res.json(Resposta.erro(`Pedido não encontrado.`));
    return;
  }

  let mapeadorDeAvaliacaoDePedido = new MapeadorDeAvaliacaoDePedido();

  let avaliacaoPedido: AvaliacaoPedido = await mapeadorDeAvaliacaoDePedido.selecioneSync({idPedido: pedido.id});

  if( !avaliacaoPedido ) {
    avaliacaoPedido = new AvaliacaoPedido();
  }

  avaliacaoPedido.pedido = pedido;
  avaliacaoPedido.nota = avaliacao.nota;
  avaliacaoPedido.gostouDaEntrega = avaliacao.gostouDaEntrega;
  avaliacaoPedido.comentario = avaliacao.comentario;

  if( !avaliacaoPedido.id ) {
    await mapeadorDeAvaliacaoDePedido.insiraGraph(avaliacaoPedido);
  } else {
    await mapeadorDeAvaliacaoDePedido.atualizeSync(avaliacaoPedido);
  }

  res.json(Resposta.sucesso(avaliacaoPedido));
});

router.get('/obtenha/:idp', async (req: any, res: any) => {
  const idPedido: any = req.params.idp;

  let mapeadorDeAvaliacaoDePedido = new MapeadorDeAvaliacaoDePedido();
  const avaliacaoPedido = await mapeadorDeAvaliacaoDePedido.selecioneSync({idPedido: idPedido});

  res.json(Resposta.sucesso(avaliacaoPedido));
});

router.get('/liste', async (req: any, res: any) => {
  const idPedido: any = req.params.idp;

  let mapeadorDeAvaliacaoDePedido = new MapeadorDeAvaliacaoDePedido();
  const avaliacoes = await mapeadorDeAvaliacaoDePedido.listeAsync({});

  for( let avaliacao of avaliacoes ) {
    avaliacao.pedido = new DTOPedido(avaliacao.pedido, req.empresa);

    let dataAvaliacao = moment(avaliacao.data);
    let dataPedido = moment(avaliacao.pedido.horarioVenda);

    let diferencaDias = dataAvaliacao.diff(dataPedido, 'days');

    let diferencaHumanizada = dataPedido.from(dataAvaliacao);

    //console.log(diferencaDias);
    avaliacao.tempoAvaliarDescricao = diferencaHumanizada;
    avaliacao.tempoAvaliar = diferencaDias;
  }


  let dataInicio = null;
  let dataFim = null;

  const notaMediaGeral = await mapeadorDeAvaliacaoDePedido.obtenhaNotaMedia(dataInicio, dataFim);
  const total = await mapeadorDeAvaliacaoDePedido.selecioneTotal({dataInicio: dataInicio, dataFim: dataFim});

  dataFim = moment().format('YYYY-MM-DD');
  dataInicio = moment().subtract(1, 'months').format('YYYY-MM-DD');
  const notaMediaUltimos30Dias = await mapeadorDeAvaliacaoDePedido.obtenhaNotaMedia(dataInicio, dataFim);

  res.json(Resposta.sucesso({
    avaliacoes: avaliacoes,
    notaMediaGeral: notaMediaGeral,
    notaMediaUltimos30Dias: notaMediaUltimos30Dias,
    total: total
  }));
});

export const AvaliacaoDePedidosController: Router = router;
