import { Router } from "express";
import { Resposta } from "../utils/Resposta";
import { TicketService } from "../service/TicketService";
import { StatusTicket, PrioridadeTicket, CategoriaTicket } from "../domain/suporte/Ticket";
import { MapeadorDeTicket } from "../mapeadores/MapeadorDeTicket";

const router: Router = Router();

// GET /suporte/tickets - Lista tickets com filtros
router.get('/tickets', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeTicket();
    
    const filtros: any = {};
    
    if (req.query.status) filtros.status = req.query.status;
    if (req.query.prioridade) filtros.prioridade = req.query.prioridade;
    if (req.query.categoria) filtros.categoria = req.query.categoria;
    if (req.query.usuarioAbertura) filtros.usuarioAbertura = req.query.usuarioAbertura;
    if (req.query.usuarioResponsavel) filtros.usuarioResponsavel = req.query.usuarioResponsavel;
    if (req.query.busca) filtros.busca = req.query.busca;
    
    if (req.query.dataInicio && req.query.dataFim) {
      filtros.dataInicio = new Date(req.query.dataInicio);
      filtros.dataFim = new Date(req.query.dataFim);
    }

    const tickets = await mapeador.listeComFiltros(filtros);
    
    res.json(Resposta.sucesso(tickets));
  } catch (error) {
    console.error('Erro ao listar tickets:', error);
    res.json(Resposta.erro('Erro ao listar tickets: ' + error.message));
  }
});

// GET /suporte/tickets/:id - Obtém ticket específico com detalhes
router.get('/tickets/:id', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const mapeador = new MapeadorDeTicket();
    
    const ticket = await mapeador.obtenhaComDetalhes(ticketId);
    
    if (!ticket) {
      return res.json(Resposta.erro('Ticket não encontrado'));
    }
    
    res.json(Resposta.sucesso(ticket));
  } catch (error) {
    console.error('Erro ao obter ticket:', error);
    res.json(Resposta.erro('Erro ao obter ticket: ' + error.message));
  }
});

// POST /suporte/tickets - Cria novo ticket
router.post('/tickets', async (req: any, res: any) => {
  try {
    const ticketService = new TicketService();
    const empresa = req.empresa;
    const usuario = req.user;
    
    if (!empresa) {
      return res.json(Resposta.erro('Empresa não identificada'));
    }
    
    if (!usuario) {
      return res.json(Resposta.erro('Usuário não identificado'));
    }

    const dadosTicket = req.body;
    
    const ticket = await ticketService.crieTicket(dadosTicket, empresa, usuario);
    
    res.json(Resposta.sucesso(ticket));
  } catch (error) {
    console.error('Erro ao criar ticket:', error);
    res.json(Resposta.erro('Erro ao criar ticket: ' + error.message));
  }
});

// PUT /suporte/tickets/:id - Atualiza ticket
router.put('/tickets/:id', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const ticketService = new TicketService();
    const usuario = req.user;
    
    if (!usuario) {
      return res.json(Resposta.erro('Usuário não identificado'));
    }

    const dadosAtualizacao = req.body;
    
    const ticket = await ticketService.atualizeTicket(ticketId, dadosAtualizacao, usuario);
    
    res.json(Resposta.sucesso(ticket));
  } catch (error) {
    console.error('Erro ao atualizar ticket:', error);
    res.json(Resposta.erro('Erro ao atualizar ticket: ' + error.message));
  }
});

// DELETE /suporte/tickets/:id - Remove ticket
router.delete('/tickets/:id', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const ticketService = new TicketService();
    
    await ticketService.removaTicket(ticketId);
    
    res.json(Resposta.sucesso({ mensagem: 'Ticket removido com sucesso' }));
  } catch (error) {
    console.error('Erro ao remover ticket:', error);
    res.json(Resposta.erro('Erro ao remover ticket: ' + error.message));
  }
});

// POST /suporte/tickets/:id/fechar - Fecha ticket
router.post('/tickets/:id/fechar', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const ticketService = new TicketService();
    const usuario = req.user;
    
    if (!usuario) {
      return res.json(Resposta.erro('Usuário não identificado'));
    }
    
    const ticket = await ticketService.feche(ticketId, usuario);
    
    res.json(Resposta.sucesso(ticket));
  } catch (error) {
    console.error('Erro ao fechar ticket:', error);
    res.json(Resposta.erro('Erro ao fechar ticket: ' + error.message));
  }
});

// POST /suporte/tickets/:id/reabrir - Reabre ticket
router.post('/tickets/:id/reabrir', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const ticketService = new TicketService();
    const usuario = req.user;
    
    if (!usuario) {
      return res.json(Resposta.erro('Usuário não identificado'));
    }
    
    const ticket = await ticketService.reabra(ticketId, usuario);
    
    res.json(Resposta.sucesso(ticket));
  } catch (error) {
    console.error('Erro ao reabrir ticket:', error);
    res.json(Resposta.erro('Erro ao reabrir ticket: ' + error.message));
  }
});

// GET /suporte/tickets/:id/comentarios - Lista comentários do ticket
router.get('/tickets/:id/comentarios', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const ticketService = new TicketService();
    const apenasPublicos = req.query.publicos === 'true';
    
    const comentarios = await ticketService.listeComentarios(ticketId, apenasPublicos);
    
    res.json(Resposta.sucesso(comentarios));
  } catch (error) {
    console.error('Erro ao listar comentários:', error);
    res.json(Resposta.erro('Erro ao listar comentários: ' + error.message));
  }
});

// POST /suporte/tickets/:id/comentarios - Adiciona comentário ao ticket
router.post('/tickets/:id/comentarios', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const ticketService = new TicketService();
    const usuario = req.user;
    
    if (!usuario) {
      return res.json(Resposta.erro('Usuário não identificado'));
    }

    const dadosComentario = req.body;
    
    const comentario = await ticketService.adicioneComentario(ticketId, dadosComentario, usuario);
    
    res.json(Resposta.sucesso(comentario));
  } catch (error) {
    console.error('Erro ao adicionar comentário:', error);
    res.json(Resposta.erro('Erro ao adicionar comentário: ' + error.message));
  }
});

// GET /suporte/tickets/:id/anexos - Lista anexos do ticket
router.get('/tickets/:id/anexos', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const ticketService = new TicketService();
    
    const anexos = await ticketService.listeAnexos(ticketId);
    
    res.json(Resposta.sucesso(anexos));
  } catch (error) {
    console.error('Erro ao listar anexos:', error);
    res.json(Resposta.erro('Erro ao listar anexos: ' + error.message));
  }
});

// POST /suporte/tickets/:id/anexos - Adiciona anexo ao ticket
router.post('/tickets/:id/anexos', async (req: any, res: any) => {
  try {
    const ticketId = parseInt(req.params.id);
    const ticketService = new TicketService();
    const usuario = req.user;
    
    if (!usuario) {
      return res.json(Resposta.erro('Usuário não identificado'));
    }

    const dadosAnexo = req.body;
    
    const anexo = await ticketService.adicioneAnexo(ticketId, dadosAnexo, usuario);
    
    res.json(Resposta.sucesso(anexo));
  } catch (error) {
    console.error('Erro ao adicionar anexo:', error);
    res.json(Resposta.erro('Erro ao adicionar anexo: ' + error.message));
  }
});

// GET /suporte/estatisticas - Obtém estatísticas do suporte
router.get('/estatisticas', async (req: any, res: any) => {
  try {
    const ticketService = new TicketService();
    
    const estatisticas = await ticketService.obtenhaEstatisticas();
    
    res.json(Resposta.sucesso(estatisticas));
  } catch (error) {
    console.error('Erro ao obter estatísticas:', error);
    res.json(Resposta.erro('Erro ao obter estatísticas: ' + error.message));
  }
});

// GET /suporte/enums - Retorna os enums disponíveis
router.get('/enums', async (req: any, res: any) => {
  try {
    const enums = {
      status: Object.values(StatusTicket),
      prioridades: Object.values(PrioridadeTicket),
      categorias: Object.values(CategoriaTicket)
    };
    
    res.json(Resposta.sucesso(enums));
  } catch (error) {
    console.error('Erro ao obter enums:', error);
    res.json(Resposta.erro('Erro ao obter enums: ' + error.message));
  }
});

export = router;
