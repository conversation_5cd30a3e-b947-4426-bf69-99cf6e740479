import {Router} from "express";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {NotificacaoDeliveryService} from "../service/NotificacaoDeliveryService";
import {NotificacaoDeliveryUber} from "../domain/integracoes/NotificacaoDeliveryUber";
const crypto = require('crypto');
const router: Router = Router();


function valideToken(req: any, res: any, next: any){
  console.log('validar token notificação uber direct')
  const payload = req.body;
  let hash = req.headers['x-postmates-signature']
  console.log('hash header: ' + hash)

  if(req.empresa.integracaoUberdirect){
    let apiSecret = req.empresa.integracaoUberdirect.signingKey;
    const hashCalculado = crypto.createHmac('sha256', apiSecret).update(JSON.stringify(payload)).digest('hex');
    console.log('hash calculado: ' + hashCalculado)
    console.log('hash valido: ' + hash === hashCalculado)
  }

  next();
}

router.post('/event', valideToken, async (req: any, res: any) => {
  console.log('notificou uber direct')
  const payload = req.body;
  console.log(payload)

  let  status  = payload.status,
    idNotificacao  = payload.id,   //evt_VrCKdPLMRBmPWwQ2szAgKA
    idDelivery = payload.delivery_id, //"del_Y1dy1_u5QsCJUuR364nkuw"
    tipo = payload.kind;

  if(!status) return sendErrorResponse(res, 'status is required')
  if(!tipo) return sendErrorResponse(res, 'kind is required')

  //Para diferenciar status de corridas originais para de retorno,
  // basta verificar se os primeiros caracteres do campo delivery_id se iniciam com "del_" ou com "ret
  let   corridaRetorno = idDelivery.startsWith('ret_');

  if(tipo === 'event.delivery_status'){
    const dados = payload.data, empresa = req.empresa;

    const idEmpresa = dados.external_id || empresa.id;
    const codigoPedido = dados.manifest.reference;

    let pedido: any =
      await new MapeadorDePedido().selecioneSync({ codigo: codigoPedido, idEmpresa: idEmpresa})

    if(pedido){
      let notificacao  = new NotificacaoDeliveryUber(pedido, dados)

      await notificacao.salve(true)

      ExecutorAsync.execute( async (cbAsync: any) => {
        await NotificacaoDeliveryService.executeNotificacao(notificacao, pedido)
        cbAsync();
      }, () => {});

      res.status(204).send();
    } else {
      sendErrorResponse(res, 'Pedido não encontrado: ' + codigoPedido)
    }
  } else {
    console.log('tipo não processar: ' +  tipo)
  }
})

function sendErrorResponse(res: any, err: any) {
  console.error(err);

  let erro =  err instanceof Error ? err.message : 'Unexpected error';

  if(typeof err === 'string')
    erro = err;

  if(Array.isArray(erro))
    erro = err.join(', ');

  res.status(400).json({
    title: erro,
    status: 400
  });
}

export const UberDirectApiHooksAppController: Router = router;
