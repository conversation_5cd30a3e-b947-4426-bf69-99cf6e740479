import {Router} from "express";
import {IFoodService} from "../service/integracoes/IFoodService";
import {IntegracaoIfood} from "../domain/integracoes/IntegracaoIfood";
import {Resposta} from "../utils/Resposta";
import {IFoodUtils} from "../service/integracoes/IFoodUtils";
import {PedidoService} from "../service/PedidoService";
import {IfoodUserCode} from "../domain/integracoes/IfoodUserCode";
import {MapeadorIfoodUserCode} from "../mapeadores/MapeadorIfoodUserCode";
import {FormaDePagamentoPdv} from "../domain/pdv/FormaDePagamentoPdv";
import {MapeadorDeFormaDePagamentoPdv} from "../mapeadores/MapeadorDeFormaDePagamentoPdv";
import {FormaDePagamento} from "../domain/delivery/FormaDePagamento";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeIntegracaoIfood} from "../mapeadores/MapeadorDeIntegracaoIfood.js";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";

import {Pedido} from "../domain/delivery/Pedido";
import {OrderEvent} from "../domain/opendelivery/OrderEvent";
import {MapeadorDeNotificacaoDelivery} from "../mapeadores/MapeadorDeNotificacaoDelivery";
import {NotificacaoDelivery} from "../domain/integracoes/NotificacaoDelivery";
import {Endereco} from "../domain/delivery/Endereco";
import {EnumStatusEntregaIfood} from "../lib/integracao/ifood/EnumStatusEntregaIfood";
import {NotificacaoIfoodDelivery} from "../domain/integracoes/NotificacaoIfoodDelivery";
import {NotificacaoIfood} from "../domain/integracoes/NotificacaoIfood";
import {NotificacaoPedidoService} from "../service/NotificacaoPedidoService";
import {MapeadorDeDeliveryPedido} from "../mapeadores/MapeadorDeDeliveryPedido";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {ContatoService} from "../service/ContatoService";
import {MapeadorDeNotificacaoIfood} from "../mapeadores/MapeadorDeNotificacaoIfood";

const router: Router = Router();

function integracaoAtiva(req: any, res: any, next: any){
  if(req.empresa.integracoesIfood.length) return next();

  res.json(Resposta.erro('Nenhuma integração ifood ativa'))
}

router.get('/events/polling/dispare', async (req: any, res ) => {
  let resposta: any = await IFoodUtils.executePollingLojas();

  res.json(resposta)

})

router.get('/events/polling/dispare/me', async (req: any, res ) => {
  let integracaoIfood = req.empresa.integracoesIfood[0];

  if(integracaoIfood){
    let service = new IFoodService(integracaoIfood);

    let resposta: any = await service.facaPollingEventos(integracaoIfood.idLoja);

    res.json(resposta)
  } else {
    res.json(Resposta.erro('Nenhum integração ativa'))
  }
})


router.get('/tarefas/notificacoes/execute', async (req: any, res ) => {
  if(req.empresa.integracoesIfood.length){
    let service = new IFoodService(req.empresa.integracoesIfood[0]);

    let resposta: any = await service.executeNotificacoesPendente();

    res.json(resposta)
  } else {
    res.json(Resposta.erro('Nenhum integração ativa'))
  }
})

router.post('/reimporte/order/:guid', async (req: any, res ) => {
  let pedidoFalhaImportacao: any = req.body;
  const empresa = req.empresa;
  let order: any = pedidoFalhaImportacao.payload;
  let pedido: any;
  try{
    let geradoForaIfood = order.salesChannel === 'POS';

    if(geradoForaIfood){
      pedido = await new MapeadorDePedido().selecioneSync({ referenciaExterna: order.id})

      await pedido.atualizeRetornoDeliveryIfood(order, order.merchant.id, empresa);
    } else {
      pedido =  await new PedidoService().salvePedidoIfood(order, empresa);
    }

    if(pedido){
      if(empresa.aceitarPedidoAutomatico){
        let integracao = empresa.obtenhaIntegracaoIfood(pedido.idLojaExterna);

        let confirmado = await new IFoodService( integracao).confirmePedido(pedido.referenciaExterna).catch((erro) => {
          console.log('Falha confirmar pedido ifood')
          console.error(erro)
        })

        if(confirmado)
          await new PedidoService().executeOperacoesPedidoAceito(pedido, empresa);

        res.json(Resposta.sucesso({ guid: pedido.guid }));
      }
    }
  } catch (error) {
    console.log(error)
    let erro = error.message || error;
    res.json(Resposta.erro(erro))
  }
})

router.get('/lojas/status', async (req: any, res ) => {
  if(!req.empresa.integracoesIfood.length) return res.status(500).json( 'Integraçao nao configurada');

  let respostas  = [];

  for(let i = 0 ; i < req.empresa.integracoesIfood.length; i++){
    let integracaoIfood = req.empresa.integracoesIfood[i];

    let infoLoja: any = {loja: integracaoIfood.nomeLoja, aberta: false, mensagem: null};

    if(integracaoIfood.desativado){
      infoLoja.desativado =  true;
      respostas.push(infoLoja);
      continue;
    }

    let lojaStatus: any = await new IFoodService(integracaoIfood).obtenhaStatusLoja(integracaoIfood.idLoja).catch((err) => {
      console.error(err)
      infoLoja.mensagem =  'Não foi possivel obter status da loja'
      respostas.push(infoLoja);
    })

    if(lojaStatus) {
      let state = lojaStatus.state, aberta = lojaStatus.available,
        message = lojaStatus.message;

      if(!aberta)   console.log(JSON.stringify(lojaStatus))

      let mensagem  =  (message.subtitle || message.title);

      if( message.description) message += ": " +  message.description;

      infoLoja.aberta = aberta;
      infoLoja.status = state;
      infoLoja.mensagem = mensagem;

      respostas.push(infoLoja)
    }
  }

  res.json(respostas)
})


router.get('/:idLoja/order/:guid',  integracaoAtiva, async (req: any, res ) => {
  let guid =  req.params.guid;
  let integracao: any = req.empresa.obtenhaIntegracaoIfood(req.params.idLoja);
  let pedido = await new IFoodService(integracao).obtenhaPedido(guid);

  res.json(pedido);
});


router.get('/order/:guid/razoesCancelamento/:idLoja',  integracaoAtiva, async (req: any, res ) => {
  let guid =  req.params.guid;

  let service = new IFoodService(req.empresa.obtenhaIntegracaoIfood(req.params.idLoja));

  let razoes = await service.listeRazoesCancelamentos(guid).catch((err) => {
       console.error(err)
      res.json(  Resposta.erro((err)))
  });

  if(razoes) res.json(Resposta.sucesso(razoes));
});


router.get('/produto/:code/:idLoja',  integracaoAtiva, async (req: any, res ) => {
  let code =  req.params.code;

  let service = new IFoodService(req.empresa.obtenhaIntegracaoIfood(req.params.idLoja));

  let produto = await service.obtenhaProduto(code);

  res.json(produto);
})

router.get('/lojas',  integracaoAtiva, async (req: any, res ) => {

  let respostas  = [];

  for(let i = 0 ; i < req.empresa.integracoesIfood.length; i++) {
    let integracaoIfood = req.empresa.integracoesIfood[i];

    let service = new IFoodService(integracaoIfood);

    let loja = await service.obtenhaLoja( integracaoIfood.idLoja);

    if(loja) respostas.push(loja)
  }

  res.json(respostas);
})


router.get('/loja/formaspagamento',  integracaoAtiva, async (req: any, res ) => {
  let service = new IFoodService(req.empresa.integracoesIfood[0]);

  let formas = await service.listeFormasPagamentos( );

  res.json(formas);

})

router.get('/imgem/:order/:imagemId/:tipo/:idLoja',  integracaoAtiva, async (req: any, res ) => {

  let service = new IFoodService(req.empresa.obtenhaIntegracaoIfood(req.params.idLoja));

  let data: any = await service.obtenhaImagem(req.params.order, req.params.imagemId);

  if(data){
    // Envie a imagem de volta como resposta
    if(req.params.tipo !== 'ni') //nao informado
      res.set('Content-Type', 'image/' + req.params.tipo);

    res.send(Buffer.from(data, 'binary'));
  } else {
    res.status(404).end()
  }
})


router.post('/autorizacao/confirme', async (req: any, res: any) => {
  let dados: any = req.body;
  console.log(dados)

  let codigoAutorizacao  = dados.codigoAutorizacao;
  let verificadorCodigoAutorizacao  = dados.authorizationCodeVerifier;

  let integracao = new IntegracaoIfood(dados.idLoja, req.empresa);

  integracao.codigoAutorizacao = codigoAutorizacao;
  integracao.verificadorCodigoAutorizacao = verificadorCodigoAutorizacao;

  let service =  new IFoodService(integracao);

  let token: any = await service.obtenhaTokenPorAutorizacao().catch((err) => {
    res.json(Resposta.erro(err))
  });

  if(token){
    let lojas: any = await service.obtenhaLojasDoToken().catch((err) => {
      res.json(Resposta.erro(err))
    });

    if(lojas){
      let loja: any = lojas[0];

      if(integracao.idLoja != null)
        loja = lojas.find((item: any) => item.id === integracao.idLoja)

      integracao.idLoja = loja.id;
      integracao.nomeLoja = loja.name;

      let erroSalvar;
      await integracao.salve(true).catch((erro) => {
         erroSalvar = erro;
      });

      if(!erroSalvar){
        await new MapeadorDeEmpresa().removaDasCaches(req.empresa);
        delete integracao.empresa;
        res.json(Resposta.sucesso(integracao))
      } else {
         res.json(Resposta.erro(erroSalvar))
      }
    }
  }
})

router.post('/autorizacao/gere', async (req: any, res: any) => {
  let dados: any = await new IFoodService(null).gereCodigoDeVinculo();

  let ifooodUserCode = new IfoodUserCode(req.empresa,
    dados.userCode, dados.authorizationCodeVerifier, dados.verificationUrlComplete,
      dados.expiresIn)

  await ifooodUserCode.salve(true)

  res.json(Resposta.sucesso(ifooodUserCode))
})

router.get('/autorizacao/:idEmpresa', async (req: any, res: any) => {
  let ifooodUserCodes =
    await new MapeadorIfoodUserCode().listeAsync({idEmpresa: req.params.idEmpresa, pendente: true})

  let ifooodUserCode = ifooodUserCodes.find((item: IfoodUserCode) => !item.expirou())

  if(ifooodUserCode ){
    res.json(Resposta.sucesso(ifooodUserCode))
  } else {
    res.json(Resposta.sucesso( ))
  }

})


router.post('/:id/integracao/ifood', async (req: any, res) => {
  let empresa = req.empresa;
  const { idLoja, shippingApi,  orderApi}  = req.body;
  try {
    if(!empresa.exibirBandeiras)
      throw Error('Somente empresas configuradas com as novas formas de pagamento podem ativar ifood')

    let integracaoIfood = new IntegracaoIfood(idLoja, empresa, orderApi, shippingApi);

    let ifoodService = new IFoodService(integracaoIfood)

    let erro: any = await ifoodService.valideEmpresa();

    if(!erro){
      new MapeadorDeEmpresa().transacao(async (conexao: any, commit: any) => {
        let ifoodOnlinePdv: FormaDePagamentoPdv = await IFoodUtils.obtenhaFormaPagamentoOnline();
        let ifoodLoja: FormaDePagamentoPdv = await   IFoodUtils.obtenhaFormaPagamentoLoja();

        await integracaoIfood.salve(true).catch((err) => {
          conexao.rollback(() => {
            res.json(Resposta.erro( err));
          })
        });

        if(integracaoIfood.id) {
          let formasPagamentoInserir: any = [];

          let existenteOnline =
            empresa.formasDePagamento.find((item:  FormaDePagamento) =>
              item.formaDePagamentoPdv && item.formaDePagamentoPdv.id === ifoodOnlinePdv.id)

          if(!existenteOnline)
            formasPagamentoInserir.push( FormaDePagamento.novaPdv(ifoodOnlinePdv, null, empresa))

          ifoodLoja.bandeiras.forEach((pdvBandeira: any) => {
            let existente = empresa.formasDePagamento.find((item:  FormaDePagamento) =>
              item.bandeira && item.bandeira.id === pdvBandeira.bandeira.id &&
              item.formaDePagamentoPdv && item.formaDePagamentoPdv.id === ifoodLoja.id)

            if(!existente) formasPagamentoInserir.push( FormaDePagamento.novaPdv(ifoodLoja, pdvBandeira.bandeira, empresa));
          });


          for(let i = 0; i < formasPagamentoInserir.length; i++){
            let formaInserir: any = formasPagamentoInserir[i];

            await FormaDePagamento.ativeNovaForma(formaInserir);
          }

          commit(async () => {
            await new MapeadorDeEmpresa().removaDasCaches(empresa);
            res.json(Resposta.sucesso(integracaoIfood));
          })
        }
      })
    } else {
      res.json(Resposta.erro( erro));
    }
  } catch (err){
    res.json(Resposta.erro( err.message || err));
  }
});

router.post('/integracao/ifood/remova/:id', async (req: any, res) => {
  let empresa = req.empresa;

  let integracaoIfood = empresa.integracoesIfood.find((item: any) => item.id === Number(req.params.id))

  if(integracaoIfood){
    await new MapeadorDeIntegracaoIfood().removaAsync(integracaoIfood);
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
    res.json(Resposta.sucesso());
  } else {
    res.json(Resposta.erro('Nenhum integração encontrada'));
  }
})


router.post('/integracao/ifood/desative/:id', async (req: any, res) => {
  let empresa = req.empresa;

  let integracaoIfood = empresa.integracoesIfood.find((item: any) => item.id === Number(req.params.id))

  if(integracaoIfood){
    integracaoIfood.desativado  = true;
    await new MapeadorDeIntegracaoIfood().atualizeAtiva(integracaoIfood);
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
    res.json(Resposta.sucesso());
  } else {
    res.json(Resposta.erro('Nenhum integração encontrada'));
  }
})

router.post('/integracao/ifood/ative/:id', async (req: any, res) => {
  let empresa = req.empresa;

  let integracaoIfood = empresa.integracoesIfood.find((item: any) => item.id === Number(req.params.id))

  if(integracaoIfood){
    integracaoIfood.desativado  = null;
    await new MapeadorDeIntegracaoIfood().atualizeAtiva(integracaoIfood);
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
    res.json(Resposta.sucesso());
  } else {
    res.json(Resposta.erro('Nenhum integração encontrada'));
  }
})


router.post('/formaspagamento/sincronize', async (req: any, res: any) => {
  let empresa: any = req.empresa;

  if(!empresa.integracoesIfood.length)
    return res.json(Resposta.erro('Nenhum integração ativa'))

  let ifoodLoja: FormaDePagamentoPdv = await IFoodUtils.obtenhaFormaPagamentoLoja();
  let ifoodOnlinePdv: FormaDePagamentoPdv = await IFoodUtils.obtenhaFormaPagamentoOnline();

  let formasPagamentoInserir: any = [];

  ifoodLoja.bandeiras.forEach((pdvBandeira: any) => {
    let existente = empresa.formasDePagamento.find((item:  FormaDePagamento) =>
      item.bandeira && item.bandeira.id === pdvBandeira.bandeira.id && item.formaDePagamentoPdv.id === ifoodLoja.id)

    if(!existente) formasPagamentoInserir.push( FormaDePagamento.novaPdv(ifoodLoja, pdvBandeira.bandeira, empresa))

  })

  let existenteOnline =
    empresa.formasDePagamento.find((item:  FormaDePagamento) =>
      item.formaDePagamentoPdv && item.formaDePagamentoPdv.id === ifoodOnlinePdv.id)

  if(!existenteOnline)
    formasPagamentoInserir.push( FormaDePagamento.novaPdv(ifoodOnlinePdv, null, empresa))


  for(let i = 0; i < formasPagamentoInserir.length; i++){
    let formaInserir: any = formasPagamentoInserir[i];

    await FormaDePagamento.ativeNovaForma(formaInserir);
  }

  await new MapeadorDeEmpresa().removaDasCaches(empresa);

  res.json(Resposta.sucesso( ))
})

router.get('/pedidos/feche/abertos', async (req: any, res: any) => {
  let resp = await IFoodService.fechePedidosEmAberto();

  res.json(Resposta.sucesso( resp))

});


router.post('/pedidos/:guid/respostaAlteracaoEndereco', async (req: any, res: any) => {
  let aceitou = req.body.aceitou === 1;
  let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

  if(pedido){
    let integracaoIfood = req.empresa.obtenhaIntegracaoIfood(pedido.idLojaExterna)


    if(aceitou){
      let novoEndereco = Endereco.novoIfood(pedido.contato, pedido.alteracaoEndereco.getEndereco())

      let reposta: any = await  new IFoodService(integracaoIfood).aceiteMudancaEnderecoEntrega(pedido.deliveryPedido.deliveryId).catch(
        (err) => {
          res.json(Resposta.erro(err))
      })

      if(reposta){
        await new PedidoService().altereEnderecoEntrega(pedido, novoEndereco, req.user);

        res.json(Resposta.sucesso(pedido.endereco))
      }

    } else {
      let reposta: any = await  new IFoodService(integracaoIfood).rejeiteMudancaEnderecoEntrega(pedido.deliveryPedido.deliveryId).catch(
        (err) => {
          res.json(Resposta.erro(err))
        })

      if(reposta){
        await new PedidoService().registreAlteracaoPedidoNegada(pedido, req.user)

        res.json(Resposta.sucesso(pedido.deliveryPedido.toDTO()))
      }
    }

  } else {
    res.json(Resposta.erro('Nenhum pedido encontrado'))
  }
})

router.post('/pedidos/:guid/novaCotacao', async (req: any, res: any) => {
  let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

  let idLoja = req.body.idLoja;

  let integracaoIfood = req.empresa.obtenhaIntegracaoIfood(idLoja)

  if(integracaoIfood){
    let loja: any  = {id: integracaoIfood.idLoja, nome: integracaoIfood.nomeLoja};

    let novaEntrega: any = await
      new IFoodService(integracaoIfood).facaCotacaoEntregaDoPedido(req.empresa, req.user, loja, pedido)
        .catch(async (err) => {
          console.error(err)
          pedido.erroExternoDelivery = err;
          await new MapeadorDePedido().atualizeErroExternoDelivery(pedido)
          res.json(Resposta.erro(err))
        });

    if(novaEntrega)
      res.json(Resposta.sucesso(novaEntrega.toDTO()))
  } else {
    res.json(Resposta.erro('Nenhum integração com loja ativa: ' + idLoja))
  }

})


router.post('/pedidos/:guid/canceleCotacao',  async (req: any, res: any) => {
  let pedido = await new MapeadorDePedido().selecioneSync({guid: req.params.guid});

  if(!pedido) return   res.json(Resposta.erro('Pedido inválido '))
  if(!pedido.deliveryPedido) return   res.json(Resposta.erro('Nenhum cotaçao vinculado ao pedido '))

  if(pedido.deliveryPedido.status === EnumStatusEntregaIfood.Cotado){
    await new MapeadorDeDeliveryPedido().removaAsync({idPedido: pedido.id, empresa: pedido.empresa});
    res.json(Resposta.sucesso({id: pedido.deliveryPedido.id, removido: true }))
  } else {
    res.json(Resposta.erro('Cotação não pode ser cancelada nesse status: ' + pedido.deliveryPedido.status))
  }

})


router.post('/pedidos/:guid/novaEntrega', async (req: any, res: any) => {
  let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

  let idLoja = req.body.idLoja;

  let integracaoIfood = req.empresa.obtenhaIntegracaoIfood(idLoja)

  if(integracaoIfood){
    let novaEntrega: any = await
      new IFoodService(integracaoIfood).notifiqueNovaEntregaPedido(req.empresa, req.user, integracaoIfood.idLoja, pedido)
        .catch(async (err) => {
          console.error(err)
          pedido.erroExternoDelivery = err;
          await new MapeadorDePedido().atualizeErroExternoDelivery(pedido)
          res.json(Resposta.erro(err))
        });

    if(novaEntrega)
      res.json(Resposta.sucesso(novaEntrega.toDTO()))
  } else {
    res.json(Resposta.erro('Nenhum integração com loja ativa: ' + idLoja))
  }
})



router.get('/pedidos/:id/notificacoes/delivery', async (req: any, res: any) => {
  let eventos = await new MapeadorDeNotificacaoDelivery().listeAsync({ idPedido: req.params.id, origem: 'ifood'})

  res.json(Resposta.sucesso(eventos.map((item: NotificacaoDelivery) => item.toDTO())))
})

router.post('/pedidos/:guid/delivery/cancele', async (req: any, res: any) => {

  let pedido: Pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});
  let integracaoIfood: any;

  if(pedido){
    integracaoIfood = req.empresa.obtenhaIntegracaoIfood(pedido.idLojaExterna)

    if(req.body.ignorarParceiro){
      await  pedido.deliveryPedido.atualizeRetorno(EnumStatusEntregaIfood.Cancelado,
        {operador: { id: req.user.id, nome: req.user.nome }}, 'Operador cancelou solicitação no Meucardapio')

      return res.json( Resposta.sucesso(pedido.deliveryPedido.toDTO()))
    }

    let delivery: any =  await new IFoodService(integracaoIfood).canceleEntregador(pedido.referenciaExterna).catch((erro) => {
      let retorno: any = { parceiroErro : erro}
      if(erro.indexOf('OrderStatusInvalid') >= 0) retorno.ignorarParceiro = true;
      res.json(Resposta.sucesso(retorno))
    })

    if(delivery){
      let orderEvent = await OrderEvent.novoCancelamentoEntregadorIfood(pedido,  'Entregador cancelado pela loja');

      let resposta: any = {  aguardarRetorno: true, idEvent: orderEvent.eventId};

      res.json(Resposta.sucesso( resposta) )
    }
  } else {
    res.json({erro : 'Pedido não encontrado'})
  }
})



router.post('/integracao/atualize', async (req: any, res: any) => {
  const {id, orderApi, shippingApi, confirmarCotacao, solicitarAutomatico} = req.body;

  let integracao: IntegracaoIfood = req.empresa.integracoesIfood.find((item: any) => item.id === id)

  if(integracao) {
    integracao.orderApi = orderApi;
    integracao.shippingApi = shippingApi;

    if(integracao.shippingApi){
      integracao.confirmarCotacao = confirmarCotacao
      integracao.solicitarAutomatico = solicitarAutomatico
      if(integracao.solicitarAutomatico)
        integracao.confirmarCotacao = false
    } else {
      integracao.confirmarCotacao = false
      integracao.solicitarAutomatico = false
    }

    if(!integracao.orderApi && !integracao.shippingApi)
      return   res.json(Resposta.erro('Obrigado ter pelo menos 1 modulo api ativo'))

    await new MapeadorDeIntegracaoIfood().atualizeApi(integracao);
    await new MapeadorDeEmpresa().removaDasCaches(req.empresa)
    delete integracao.empresa;
    res.json(Resposta.sucesso(integracao))
  } else {
    res.json(Resposta.erro('Integraão nao encontrada: ' + integracao.id))
  }

})

//codigo testar eventos ifood
router.get('/notificacao/:id/execute', async (req: any, res: any) => {

  let notificacao  = await  new MapeadorDeNotificacaoIfood().selecioneSync({id: req.params.id})

  if(notificacao){
    let service: IFoodService = new IFoodService(req.empresa.integracoesIfood[0]);

    await NotificacaoPedidoService.executeIfood(notificacao, service)
    res.json(notificacao)

  } else {
    res.json({ erro: 'nenhuma notificação com esse codigo: ' + req.params.id})
  }

})

//codigo testar eventos ifood
router.put('/events/teste', async (req: any, res: any) => {
   const { id , orderId, merchantId, code, fullCode, metadata} = req.body;

   let integracaoIfood = req.empresa.obtenhaIntegracaoIfood(merchantId);

   if(!integracaoIfood) return  res.json(Resposta.erro('Integraão nao encontrada: ' + merchantId))

  let service: IFoodService = new IFoodService(integracaoIfood);

  let notificacao = NotificacaoIfoodDelivery.listeTiposDelivery().indexOf(code) >= 0 ?
    new NotificacaoIfoodDelivery(null, req.body)
    : new NotificacaoIfood(req.body);

  if(service.ehUmEventoEsperado(code)){
    try{
      if(notificacao instanceof  NotificacaoIfoodDelivery){
        let pedido = await new MapeadorDePedido().selecioneSync({codigoExterno: orderId})
        notificacao.setPedido(pedido)
      }

      await NotificacaoPedidoService.executeIfood(notificacao, service)
      res.json(notificacao)
    } catch (err){
      console.error(err)
      res.json(Resposta.erro(err));
    }

  } else {
    res.json(Resposta.erro('Evento nao esperado: ' + code + "/" + fullCode))
  }
})

router.get('/notificacoes' , async (req: any, res: any) => {
  if(req.empresa.integracoesIfood.length){
    let merchantId = req.empresa.integracoesIfood[0].idLoja;
    let notificacoes = await new MapeadorDeNotificacaoIfood().listeAsync({ merchantId: merchantId, inicio: 0, total: 100})

    res.json(Resposta.sucesso(notificacoes))
  } else {
    res.json(Resposta.sucesso([]))
  }
})

router.get('/notificacoes/delivery', async (req: any, res: any) => {

  let notificacoes = await new MapeadorDeNotificacaoDelivery().listeAsync({ origem: 'ifood', inicio: 0, total: 25})

  res.json(Resposta.sucesso(notificacoes))
})


export const IfoodController: Router = router;
