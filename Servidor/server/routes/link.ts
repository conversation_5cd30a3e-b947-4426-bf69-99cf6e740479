import { Router, Request, Response } from 'express';
import {Resposta} from "../utils/Resposta";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {MapeadorDeLinkEncurtado} from "../mapeadores/MapeadorDeLinkEncurtado";
import {LinkEncurtado} from "../domain/LinkEncurtado";

const router: Router = Router();

router.post("/", (req: any, res) => {
  let url = req.query.url;

  if(!url)
    return res.json(Resposta.erro("É necessário informar a url de destino do link encurtado."));

  LinkEncurtado.encurte(url).then((linkEncurtado: LinkEncurtado) => {
    res.json(Resposta.sucesso(linkEncurtado))
  });
});

router.get("/:id", (req, res) => {
  let urlRaiz = (new VariaveisDeRequest()).obtenhaUrlRaizSemEmpresa();

  if(!req.params.id)
    return res.redirect(urlRaiz); //o correto é redirecionar para uma página 404

  LinkEncurtado.obtenha(req.params.id).then((linkEncurtado: LinkEncurtado) => {
    if(!linkEncurtado)
      return res.redirect(urlRaiz);

    linkEncurtado.desencurte().then(url => {
      if(!url) return res.redirect(urlRaiz);

      res.redirect(url);
    })
  });
});


export const LinkController: Router = router;
