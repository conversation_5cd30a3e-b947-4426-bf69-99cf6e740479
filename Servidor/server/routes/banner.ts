import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Resposta} from "../utils/Resposta";
import {Router} from "express";
import {MapeadorDeBanner} from "../mapeadores/MapeadorDeBanner";
import {Banner} from "../domain/delivery/Banner";
import {EnumDisponibilidadeProduto} from "../lib/emun/EnumDisponibilidadeProduto";
import {Empresa} from "../domain/Empresa";
import {Mesa} from "../domain/Mesa";
import {MapeadorDeMesa} from "../mapeadores/MapeadorDeMesa";

const router: Router = Router();

router.put('/capa', async (req: any, res: any) => {
  console.log('atualizada capa...')
  const dados: any = req.body;
  console.log(dados)

  const mapeador = new MapeadorDeEmpresa();

  if(!dados.id) return  res.json(Resposta.erro('Empresa inválida'))
  if(!dados.capa) return  res.json(Resposta.erro('Capa inválida'))

  let empresa = await mapeador.selecioneSync(dados.id)

  mapeador.desativeMultiCliente();

  empresa.capa = dados.capa;

  mapeador.atualizeCapa(empresa).then( () => {
    res.json(Resposta.sucesso())
  })
});

router.get('/liste', async(req: any, res: any) => {
  const mapeador = new MapeadorDeBanner();

  mapeador.listeAsync({}).then( (banners) => {
    res.json(Resposta.sucesso(banners));
  });
});

router.get('/:id', async(req: any, res: any) => {
  const idBanner = req.params.id;
  const empresa = req.empresa;
  const mapeador = new MapeadorDeBanner();

  mapeador.selecioneSync({id: idBanner}).then( (banner) => {
    res.json(Resposta.sucesso(banner));
  });
});

router.post('/', async(req: any, res: any) => {
  const dados: any = req.body;
  const mapeador = new MapeadorDeBanner();

  const banner = new Banner();
  Object.assign(banner, dados);

  mapeador.transacao(async (conexao: any, commit: Function) => {
    mapeador.obtenhaMaiorOrdem().then( (ordem: number) => {
      banner.ordem = ordem + 1;
      banner.disponibilidade = EnumDisponibilidadeProduto.SempreDisponivel;

      mapeador.insiraGraph(banner).then( (resposta: any) => {
        commit( () => {
          res.json(Resposta.sucesso({
            id: banner.id
          }));
        });
      });
    }).catch( (erro: Error) => {
      conexao.rollback();
    });
  });
});

router.put('/', async(req: any, res: any) => {
  const dados: any = req.body;

  const banner = new Banner();
  Object.assign(banner, dados);

  banner.disponibilidade = EnumDisponibilidadeProduto.SempreDisponivel;

  if(banner.validade)
    banner.validade = new Date(banner.validade)

  await new MapeadorDeBanner().atualizeSync(banner);
  await new MapeadorDeEmpresa().removaDasCaches(req.empresa)
  res.json(Resposta.sucesso({
    id: banner.id
  }));

});

router.delete('/:id', async(req: any, res: any) => {
  const id = req.params.id;
  const empresa: Empresa = req.empresa;

  const banner = new Banner();
  banner.id = id;
  banner.removido = true;

  const mapeadorDeBanner = new MapeadorDeBanner();

  mapeadorDeBanner.marqueComoRemovida(banner).then( () => {
    res.json(Resposta.sucesso('Banner removido com sucesso'));
  });
});

export const BannerController: Router = router;
