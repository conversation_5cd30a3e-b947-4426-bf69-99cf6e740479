// See https://github.com/dialogflow/dialogflow-fulfillment-nodejs
// for Dialogflow fulfillment library docs, samples, and to report issues
'use strict';

const {WebhookClient} = require('dialogflow-fulfillment');

function onRequest(request: any, response: any) {
  const agent = new WebhookClient({ request, response });
  console.log('Dialogflow Request headers: ' + JSON.stringify(request.headers));
  console.log('Dialogflow Request body: ' + JSON.stringify(request.body));
  console.log(agent.parameters);

  var dados = {};

  function welcome(agent) {
    agent.add(`Olá. Como posso ajudar?
Você pode fazer um pedido,
Fazer uma sugestão,
ou obter informações sobre o horário de funcionamento.`);
  }

  function NovoPedidoEscolheuTodosProdutos(agent) {
    var temRetirada = false;

    if( !temRetirada ) {
      agent.add(`Qual endereço de entrega? Informe o endereço completo incluindo o Bairro e número.`);
    } else {
      agent.add(`Você deseja fazer retirada ou receber em casa o seu pedido?

1. Retirada
2. Entrega em casa`);
    }

    agent.setContext({ name: 'EscolhiReceberEmCasa', lifespan: 1, parameters: { city: 'Rome' }});
  }

  function Endereco(agent) {
    var endereco = agent.parameters.endereco;

    var logradouro = endereco['street-address'];
    var cidade = endereco.city;
    var bairro = endereco['subadmin-area'];

    console.log('processando Intent Endereco');
    if( !logradouro ) {
      agent.add(`Informe o nome da sua rua, avenida, etc.`);
      agent.setContext({ name: 'EscolhiReceberEmCasa', lifespan: 1, parameters: {} });
      return;
    }

    console.log('pedir complemento');

    agent.add('Informe o número do local de entrega. Informe 0 se não tiver número:');
    agent.setContext({ name: 'EnderecoNumero', lifespan: 1, parameters: {} });
  }

  function EnderecoInformeBairro(agent) {
    var nomeBairro = agent.parameters.bairro;

    agent.add("Informe o complemento do seu endereço (ponto de referência): ");
    agent.setContext({ name: 'EnderecoComplemento', lifespan: 1, parameters: {} });
  }

  function EnderecoNumero(agent) {
    console.log('Processando intent numero', agent.contexts);

    console.log('contexto: ', agent.context);
    var contextoEndereco = agent.getContext('endereco-followup');
    console.log('achou', contextoEndereco);
    var bairro = contextoEndereco.parameters.endereco['subadmin-area'];

    console.log('bairro:' + bairro);
    if( !bairro ) {
      console.log('vai pedir o bairro');

      agent.add('Informe seu bairro');
      agent.setContext({ name: 'EnderecoBairro', lifespan: 1, parameters: {} });
    } else {
      agent.add("Informe o complemento do seu endereço (ponto de referência): ");
      agent.setContext({ name: 'EnderecoComplemento', lifespan: 1, parameters: {} });
    }
  }

  function EnderecoInformeComplemento(agent) {
    agent.add(`Confirma seu endereço? 1 - Sim 2 - Não`);
    agent.setContext({ name: 'ConfirmarEndereco', lifespan: 1, parameters: {  }});
  }

  function NovoPedido(agent) {
    agent.add('Segue o <<Pedido>>. Escolha o código do produto.');
  }

  function FormaDeEntrega(agent) {
    var opcaoEscolhida = agent.parameters.opcaoEscolhida;

    if( opcaoEscolhida === 1 ) {
      agent.add("Informe o nome da rua, avenida do local de entrega.");
      agent.setContext({ name: 'EscolhiReceberEmCasa', lifespan: 1, parameters: { }});
    } else {
      agent.add("Anotado. O pagamento vai ser em dinheiro ou cartão? 1 - Dinheiro 2 - Cartão");
      agent.setContext({ name: 'FormaDePagamento', lifespan: 1, parameters: { }});
    }

    console.log('Fim forma de entrega');
  }

  function ConfirmarPedidoNaoEndereco(agent) {
    agent.add('Ótimo. Você prefere receber em casa ou retirar aqui? 1 - Receber em Casa 2 - Retirar');
  }

  function EnderecoConfirmarNao(agent) {
    agent.add('Ótimo. Você prefere receber em casa ou retirar aqui? 1 - Receber em Casa 2 - Retirar');
  }

  function PagamentoDinheiroTrocoNao(agent) {
    agent.add('Segue seu Pedido. Pode Confirmar? 1 - Sim, 2 - Não');
  }

  function PagamentoDinheiroTrocoSimValor(agent) {
    agent.add('Segue seu Pedido. Pode Confirmar? 1 - Sim, 2 - Não');
  }

  function PagamentoCartao(agent) {
    agent.add('Segue seu Pedido. Pode Confirmar? 1 - Sim, 2 - Não');
  }

  function fallback(agent) {
    agent.add(`I didn't understand`);
    agent.add(`I'm sorry, can you try again?`);
  }

  // // Uncomment and edit to make your own intent handler
  // // uncomment `intentMap.set('your intent name here', yourFunctionHandler);`
  // // below to get this function to be run when a Dialogflow intent is matched
  // function yourFunctionHandler(agent) {
  //   agent.add(`This message is from Dialogflow's Cloud Functions for Firebase editor!`);
  //   agent.add(new Card({
  //       title: `Title: this is a card title`,
  //       imageUrl: 'https://developers.google.com/actions/images/badges/XPM_BADGING_GoogleAssistant_VER.png',
  //       text: `This is the body text of a card.  You can even use line\n  breaks and emoji! 💁`,
  //       buttonText: 'This is a button',
  //       buttonUrl: 'https://assistant.google.com/'
  //     })
  //   );
  //   agent.add(new Suggestion(`Quick Reply`));
  //   agent.add(new Suggestion(`Suggestion`));
  //   agent.setContext({ name: 'weather', lifespan: 2, parameters: { city: 'Rome' }});
  // }

  // // Uncomment and edit to make your own Google Assistant intent handler
  // // uncomment `intentMap.set('your intent name here', googleAssistantHandler);`
  // // below to get this function to be run when a Dialogflow intent is matched
  // function googleAssistantHandler(agent) {
  //   let conv = agent.conv(); // Get Actions on Google library conv instance
  //   conv.ask('Hello from the Actions on Google client library!') // Use Actions on Google library
  //   agent.add(conv); // Add Actions on Google library responses to your agent's response
  // }
  // // See https://github.com/dialogflow/fulfillment-actions-library-nodejs
  // // for a complete Dialogflow fulfillment library Actions on Google client library v2 integration sample

  // Run the proper function handler based on the matched Dialogflow intent name

  let intentMap = new Map();
  intentMap.set('Default Welcome Intent', welcome);
  intentMap.set('Default Fallback Intent', fallback);
  intentMap.set('NovoPedido - EscolheuTodosProdutos', NovoPedidoEscolheuTodosProdutos);
  intentMap.set('Endereco - Informe Bairro', EnderecoInformeBairro);
  intentMap.set('Endereco - Informe Complemento', EnderecoInformeComplemento);
  intentMap.set('Endereco - Numero', EnderecoNumero);
  intentMap.set('Forma de Entrega', FormaDeEntrega);
  intentMap.set('NovoPedido', NovoPedido);
  intentMap.set('Confirmar-Pedido - Nao - Endereco', ConfirmarPedidoNaoEndereco);
  intentMap.set('Endereco - Confirmar Não', EnderecoConfirmarNao);
  intentMap.set('Pagamento Dinheiro - Troco Não', PagamentoDinheiroTrocoNao);
  intentMap.set('Pagamento Dinheiro - TrocoSim - Valor', PagamentoDinheiroTrocoSimValor);
  intentMap.set('Pagamento Cartao', PagamentoCartao);
  intentMap.set('Endereco', Endereco);

  agent.handleRequest(intentMap);
}

exports.onRequest = onRequest;
