import {Router} from "express";
import {MapeadorDeTipoDePontuacao} from "../mapeadores/MapeadorDeTipoDePontuacao";
import {TipoDePontuacaoPorValor} from "../domain/TipoDePontuacaoPorValor";
import {TipoDePontuacaoQtdFixa} from "../domain/TipoDePontuacaoQtdFixa";

const router: Router = Router();

router.get('/liste/:idEmpresa', async( req, res) => {
  const idEmpresa = req.params.idEmpresa;

  const mapeadorDeTipoDePontuacao = new MapeadorDeTipoDePontuacao();

  mapeadorDeTipoDePontuacao.listeAsync({idEmpresa: idEmpresa}).then( (dados) => {
    res.json({
      sucesso: true,
      dados: dados
    });
  });
});

router.post('/insira', async(req, res) => {
  const dados = req.body;

  let tipoDePontuacao = null;

  if( dados.tipo === 'por-valor' ) {
    tipoDePontuacao = new TipoDePontuacaoPorValor();
  } else {
    tipoDePontuacao = new TipoDePontuacaoQtdFixa();
  }

  Object.assign(tipoDePontuacao, dados);

  new MapeadorDeTipoDePontuacao().insiraSync(tipoDePontuacao).then( (atualize: any) => {
    res.json({
      sucesso: true
    });
  }).catch( (erro: any) => {
    res.json({
      sucesso: false
    });
  });
});

router.post('/atualize', async(req, res) => {
  const dados = req.body;

  new MapeadorDeTipoDePontuacao().atualizeSync(dados).then( (atualize: any) => {
    res.json({
      sucesso: true
    });
  });
});



export const TiposDePontuacaoController: Router = router;
