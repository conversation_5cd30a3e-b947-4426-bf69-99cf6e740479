import {Router} from "express";
import SocketServer from "../service/SocketService";
import SocketService from "../service/SocketService";

const router: Router = Router();

router.get('/index', async (req, res) => {
  let clientes = await SocketServer.getInstance(3000).getConnectedClients();
  let rooms = await SocketService.getInstance(3000).getAllRooms();

  res.json({
    sucesso: true,
    rooms: rooms.length
  });
});

router.get('/atualizeAbas', async (req: any, res: any) => {
  const io = SocketServer.getInstance(3000);
  const empresa = req.empresa;

  //io.to('empresa_1').emit('reload');
  console.log('disparando evento para: ' + 'empresa_' + empresa.id);
  io.dispare('empresa_' + empresa.id, 'reload');

  res.json({
    sucesso: true
  });
});


export const SocketsController: Router = router;
