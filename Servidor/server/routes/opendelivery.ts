import {Router} from "express";
import {IntegracaoOpendelivery} from "../domain/integracoes/IntegracaoOpendelivery";
import {ClienteApi} from "../domain/api/ClienteApi";
import {MapeadorDeClienteApi} from "../mapeadores/MapeadorDeClienteApi";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {OpenDeliveryMerchantService} from "../service/integracoes/OpenDeliveryMerchantService";
import {Resposta} from "../utils/Resposta";
import {Empresa} from "../domain/Empresa";
import {MapeadorDeOrderEvent} from "../mapeadores/MapeadorDeOrderEvent";
import {BearerToken} from "../domain/api/BearerToken";
import {MapeadorDeBearerToken} from "../mapeadores/MapeadorDeBearerToken";
import {IntegracaoOpendeliveryLogistica} from "../domain/integracoes/IntegracaoOpendeliveryLogistica";
import {OpenDeliveryLogisticService} from "../service/integracoes/OpenDeliveryLogisticService";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {DeliveryPedido} from "../domain/integracoes/DeliveryPedido";
import {Pedido} from "../domain/delivery/Pedido";
import {EnumStatusEntregaLogistic} from "../lib/integracao/opendelivery/EnumStatusEntregaLogistic";
import {DTOPedidoOpenDeliveryLogistica} from "../lib/integracao/opendelivery/DTOPedidoOpenDeliveryLogistica";
import {MapeadorDeNotificacaoDelivery} from "../mapeadores/MapeadorDeNotificacaoDelivery";
import {NotificacaoDelivery} from "../domain/integracoes/NotificacaoDelivery";
import {EnumTipoDeCobranca} from "../domain/delivery/EnumTipoDeCobranca";

const router: Router = Router();


function sendErrorResponse(res: any, err: any) {
  console.error(err);

  let erro =  err instanceof Error ? err.message : 'Unexpected error';

  if(typeof err === 'string')
    erro = err;

  if(Array.isArray(erro))
    erro = err.join(', ');

  res.status(400).json({
    title: erro,
    status: 400
  });
}

router.post('/oauth/token', async (req: any, res: any) => {
  let empresa = req.empresa;

  if(empresa.integracaoOpendelivery){
    let client_id = req.body.client_id,
      client_secret = req.body.client_secret,
      grant_type = req.body.grant_type;

    if(grant_type !== 'client_credentials')
      return sendErrorResponse(res, 'grant_type inválido')

    let integracao: IntegracaoOpendelivery = empresa.integracaoOpendelivery;

    if(integracao.cliente && integracao.cliente.id   === client_id){
      if(client_secret === integracao.cliente.segredo ){
        //Pass in a null for user id since there is no user with this grant type
        let bearerToken = new BearerToken(integracao.cliente);

        new MapeadorDeBearerToken().insiraGraph(bearerToken).then((idToken: any) => {
          res.json({
            "access_token": bearerToken.getToken(),
            "token_type": "bearer",
            "expires_in": 0
          })
        })
      } else {
        sendErrorResponse(res, 'client_secret inválido')
      }
    } else {
      sendErrorResponse(res, 'client_id inválido')
    }
  } else {
    sendErrorResponse(res, 'Empresa não está ativa no OpenDelivery')
  }
})

router.post('/merchant/ative', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(empresa.integracaoOpendelivery){
    await empresa.integracaoOpendelivery.ative();
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
  }
  res.json(Resposta.sucesso())
})

router.post('/merchant/desative', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(empresa.integracaoOpendelivery){
    await empresa.integracaoOpendelivery.desative();
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
  }
  res.json(Resposta.sucesso())
})

router.post('/logistica/ative', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(empresa.integracaoUberAtiva())
    return res.json( Resposta.erro('Integração com Uber já esta ativa'))

  if(empresa.integracaoOpendeliveryLogistica){
    await empresa.integracaoOpendeliveryLogistica.ative();
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
    res.json(Resposta.sucesso())
  } else {
    res.json(Resposta.erro('Nenhuma integração salva'));
  }
})


router.post('/logistica/desative', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(empresa.integracaoOpendeliveryLogistica){
    if(empresa.cobraPorApi(EnumTipoDeCobranca.POR_APIOPENDELIVERY))
      return res.json(Resposta.erro('Alteração nao permitida, desative forma de entrega "Receber em casa" por API OpenDelivery'))

    await empresa.integracaoOpendeliveryLogistica.desative();
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
  }

  res.json(Resposta.sucesso())
})

router.post('/logistica/params', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(empresa.integracaoUberAtiva())
    return res.json( Resposta.erro('Integração com Uber já esta ativa'))

  if(empresa.integarcaoComFoodyDeliveryAtiva())
    return res.json( Resposta.erro('Integração com Foody Delivery já esta ativa'))


  let integracaoOpendeliveryLogistica: any =
    Object.assign( new IntegracaoOpendeliveryLogistica(req.user), req.body)

  if(empresa.integracaoOpendeliveryLogistica)
    integracaoOpendeliveryLogistica.id =  empresa.integracaoOpendeliveryLogistica.id;


  let service: OpenDeliveryLogisticService = new OpenDeliveryLogisticService(integracaoOpendeliveryLogistica)

  service.obtenhaToken( ).then(async (dados: any) => {
    console.log(dados)
    integracaoOpendeliveryLogistica.salve(true);
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
    res.json(integracaoOpendeliveryLogistica)

  }).catch((erro) => {
    res.json(Resposta.erro(erro))
  })

})

router.post('/merchant/params', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(!empresa.integracaoOpendelivery)
    empresa.integracaoOpendelivery = new IntegracaoOpendelivery(null, req.user);


  empresa.integracaoOpendelivery.setParansMerchant(req.body)

  let service: OpenDeliveryMerchantService =
    new OpenDeliveryMerchantService( empresa.integracaoOpendelivery.obtenhaCredencialMerchant());


  service.obtenhaMerchant().then( async (merchant: any) => {

    if(merchant && merchant.id){
      new MapeadorDeEmpresa().transacao(async (conexao: any, commit: any) => {
        if(!empresa.integracaoOpendelivery.id)
          await empresa.integracaoOpendelivery.salve(true);

        empresa.integracaoOpendelivery.setMerchant(merchant)
        await  empresa.integracaoOpendelivery.atualizeMerchant();
        await new MapeadorDeEmpresa().removaDasCaches(empresa);

        commit( () => {
          delete empresa.integracaoOpendelivery.empresa;
          res.json(empresa.integracaoOpendelivery)
        })

      })
    } else {
      console.log(merchant)
      res.json(Resposta.erro('Não foi possivel ober dados do estabelecimento'))
    }
  }).catch((erro: any) => {
    res.json(Resposta.erro(erro))
  })
})

router.post('/orderapp/keys', async (req: any, res: any) => {
   let empresa = req.empresa;

  new MapeadorDeEmpresa().transacao(async (conexao: any, commit: any) => {
    let cliente = new ClienteApi('OpenDelivery api', 'opendeliveryapi',  null, '1', true)

    cliente.gereIdESegredo();

    await new MapeadorDeClienteApi().insiraGraph(cliente);

    if(!empresa.integracaoOpendelivery)
      empresa.integracaoOpendelivery = new IntegracaoOpendelivery(cliente, req.user);

    await empresa.integracaoOpendelivery.salve(true);
    await new MapeadorDeEmpresa().removaDasCaches(empresa);

    commit( () => {
      delete cliente.empresa;
      delete empresa.integracaoOpendelivery.empresa;
      res.json(Resposta.sucesso(empresa.integracaoOpendelivery))
    })

  })

})

router.get('/cartoes/bandeiras/:tipo', async (req: any, res: any) => {
  let tipo = req.params.tipo;

  let service: OpenDeliveryMerchantService =  new OpenDeliveryMerchantService(  {});

  let bandeiras = await service.listeBandeiras(tipo)

  res.json(Resposta.sucesso(bandeiras))
})

router.get('/pedidos/:guid/events', async (req: any, res: any) => {
   let eventos = await new MapeadorDeOrderEvent().listeAsync({ orderId: req.params.guid})

   res.json(Resposta.sucesso(eventos))
})


router.get('/pedidos/:id/events/logistic', async (req: any, res: any) => {
  let eventos = await new MapeadorDeNotificacaoDelivery().listeAsync({ idPedido: req.params.id, origem: 'opendelivery'})

  res.json(Resposta.sucesso(eventos.map((item: NotificacaoDelivery) => item.toDTO())))
})



router.post('/pedidos/:guid/novaEntrega', async (req: any, res: any) => {
  let integracao: IntegracaoOpendeliveryLogistica = req.empresa.integracaoOpendeliveryLogistica;

  if(integracao && integracao.ativa){
     let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

     //se ja tem, verificar se foi aceita lá antes de pedir um nova
     if(pedido.deliveryPedido){
       let delivery: any = await integracao.obtenhaService().obtenhaEntrega(pedido.guid, req.empresa).catch((erro: any) => {
          console.error(erro)
       });

       if(delivery){
         let event = delivery.events[delivery.events.length - 1]

         if(event.type && pedido.deliveryPedido.ehStatusValido(event.type)){
           if(event.type !== pedido.deliveryPedido.status )
             await pedido.deliveryPedido.atualizeRetorno(event.type, delivery)

           if(pedido.deliveryPedido.foiAceita)
             return res.json(Resposta.sucesso(pedido.deliveryPedido.toDTO()))
         }
       }
     }


     let novaEntrega: DeliveryPedido =
       await integracao.obtenhaService().notifiqueNovaEntrega(pedido , req.empresa, req.user);

     if(novaEntrega){
       res.json(Resposta.sucesso(novaEntrega.toDTO()))
     } else {
       res.json(Resposta.erro(pedido.erroExternoDelivery))
     }
  } else {
    res.json(Resposta.erro('Nenhuma integração ativa'))
  }
})

router.post('/pedidos/:guid/delivery/cancele', async (req: any, res: any) => {
  let integracao: IntegracaoOpendeliveryLogistica = req.empresa.integracaoOpendeliveryLogistica;

  let motivo = req.body.motivo,
    acao = req.body.acao,
    razao = req.body.razao;

  if(integracao){
    let pedido: Pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

    let erro: any;
    await integracao.obtenhaService().canceleEntrega(pedido, req.empresa, razao, acao, motivo)
      .catch((_erro: string) => {
        erro = _erro
    })

    if(!erro){
      await  pedido.deliveryPedido.atualizeRetorno(EnumStatusEntregaLogistic.Cancelado, null, 'Loja cancelou entrega')

      res.json(Resposta.sucesso(pedido.deliveryPedido.toDTO()))
    } else {
      res.json({erro : erro})
    }
  } else {
    res.json(Resposta.erro('Nenhum integração ativa'))
  }
})


router.get('/delivery/:guid', async (req: any, res: any) => {
  let integracao: IntegracaoOpendeliveryLogistica = req.empresa.integracaoOpendeliveryLogistica;

  if(integracao){
    let delivery: any = await integracao.obtenhaService().obtenhaEntrega(req.params.guid, req.empresa).catch((erro: any) => {
      res.json(Resposta.erro(erro));
    });

    if(delivery)
      res.json(delivery)
  } else {
    res.json(Resposta.erro('Nenhuma integração ativa'));
  }
})

router.get('/pedidos/:guid/delivery/sincronize', async (req: any, res: any) => {
  let integracao: IntegracaoOpendeliveryLogistica = req.empresa.integracaoOpendeliveryLogistica;

  if(integracao){
    let pedido = await new MapeadorDePedido().selecioneSync({guid: req.params.guid});

    if(!pedido) return    res.json(Resposta.erro('Pedido inválido'))
    if(!integracao) return    res.json(Resposta.erro('integracao nao configurada'))


    let delivery: any = await integracao.obtenhaService().obtenhaEntrega(pedido.guid, req.empresa).catch((erro: any) => {
      res.json(Resposta.erro(erro));
    });

    if(delivery){
      let event = delivery.events[delivery.events.length - 1]

      if(!event.type ||  !pedido.deliveryPedido.ehStatusValido(event.type))
        return     res.json(Resposta.erro('Status inválido: ' + event.type));

      if(event.type !== pedido.deliveryPedido.status )
        await pedido.deliveryPedido.atualizeRetorno(event.type, delivery)

      res.json(Resposta.sucesso(pedido.deliveryPedido.toDTO()))
    }
  }else {
    res.json(Resposta.erro('Nenhuma integração ativa'));
  }
})

router.get('/pedidos/:guid/delivery/payload', async (req: any, res: any) => {
  let integracao: IntegracaoOpendeliveryLogistica = req.empresa.integracaoOpendeliveryLogistica;

  let pedido = await new MapeadorDePedido().selecioneSync({guid: req.params.guid});

  if(!pedido) return    res.json(Resposta.erro('Pedido inválido'))
  if(!integracao) return    res.json(Resposta.erro('integracao nao configurada'))

  try {
    let payload = new DTOPedidoOpenDeliveryLogistica(pedido, req.empresa);

    res.json(payload)
  } catch (err){
    res.json(err.message)
  }
})


export const OpenDeliveryController: Router = router;
