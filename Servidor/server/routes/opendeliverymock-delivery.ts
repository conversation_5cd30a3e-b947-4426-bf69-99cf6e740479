import {Router} from "express";
import uuid = require("uuid");
import * as moment from "moment";

const router: Router = Router();

const tokenFixo = 'xx+xxxx+xx+x00+0+00', client_id = '318d5b703ed311fa5264a046c0b057cc', client_secret = 'afa4f82970a9b521c132533ce9940f7d'

router.post('/oauth/token', (req: any, res: any) => {
  // Verifica o cabeçalho da solicitação
  if (req.headers['content-type'] !== 'application/x-www-form-urlencoded')
    return res.status(400).json({ error: 'Content-Type inválido. Apenas application/x-www-form-urlencoded é aceito.' });


  let clientId = req.body.client_id,
    clientSecret = req.body.client_secret,
    grantType = req.body.grant_type;

  if(grantType !== 'client_credentials')
    return sendErrorResponse(res, 'grant_type inválido')

  if(clientId === client_id && clientSecret === client_secret){
    res.json({
      "access_token": tokenFixo,
      "token_type": "bearer",
      "expires_in": 0
    })
  } else{
    res.status(401).json({
      title: "Bad credencials",
      status: 401
    });
  }
})

router.get('/logistics/delivery/:guid' , (req: any, res: any) => {
  res.json({})
})

router.post('/logistics/orderPicked/:guid' , (req: any, res: any) => {
  console.log('notificou mock orderPicked')
  res.json({})
})

router.post('/logistics/finishDelivery/:guid' , (req: any, res: any) => {
  console.log('notificou mock finishDelivery')
  res.json({})
})

router.post('/logistics/readyForPickup/:guid' , (req: any, res: any) => {
  console.log('notificou mock readyForPickup')
  res.json({})
})


router.post('/logistics/availability' , (req: any, res: any) => {
  let dados: any = req.body;
  console.log(req.body)
  if(!dados.pickupAddress || !dados.deliveryAddress)
    return res.status(400).json({erro: 'Dados inválidos'})

  res.json({
    "deliveryPrice": {
      "price": {
        "value": 40,
        "currency": "BRL"
      },
      "pricingList": "NORMAL",
      "additionalPricePercentual": 0
    },
    "vehicles": {
      "availableVehicles": 0,
      "vehiclesAvailable": {
        "type": [
          "MOTORBIKE_BAG"
        ],
        "container": "NORMAL",
        "containerSize": "SMALL",
        "instruction": "string"
      },
      "nextAvailableVehicle": 0
    },
    "ETAs": {
      "updateMethod": "OFFLINE",
      "pickupEtaInMinutes": 0,
      "pickupEtaDatetime": "2019-08-24T14:15:22Z",
      "deliveryEtaInMinutes": 0,
      "deliveryEtaDatetime": "2019-08-24T14:15:22Z",
      "returnToMerchantEtaInMinutes": 0,
      "returnToMerchantEtaDatetime": "2019-08-24T14:15:22Z",
      "maxDeliveryTime": "2019-08-24T14:15:22Z"
    }
  })
})

router.get('/logistics/delivery/:id' , (req: any, res: any) => {
  let dados = req.body;

  console.log(dados)

  res.json({
    "deliveryId": req.params.id,
    "orderId": "string",
    "orderDisplayId": "96b41025",
    "merchant": {
      "id": "22815773000169-dbc7e35a-c936-4665-9e13-eb55eb8b6824",
      "name": "Pizza Plaza"
    },
    "customerName": "string",
    "events": [
      {
        "type": "ACCEPTED",
        "message": "string",
        "datetime": new Date(),
        "rejectionInfo": {
          "reason": "PRICE_EXCEEDED"
        }
      }
    ],
    "problem": [
    ],
    "vehicle": {
      "type": "MOTORBIKE_BAG",
      "container": "NORMAL",
      "containerSize": "SMALL",
      "instruction": "string"
    },
    "deliveryPrice": {
      "price": {
        "value": 40,
        "currency": "BRL"
      },
      "pricingList": "NORMAL",
      "additionalPricePercentual": 0
    },
    "times": {
      "deliveryEtaDate": "2019-08-24T14:15:22Z",
      "maxDeliveryTime": "2019-08-24T14:15:22Z",
      "isDeliveryFinished": true,
      "deliveryFinishDate": "2019-08-24T14:15:22Z"
    },
    "deliveryPerson": {
      "id": "1233",
      "name": "Jose silva",
      "pictureURL": "string",
      "phone": "string"
    },
    "combinedOrdersIds": [
      "string"
    ],
    "externalTrackingURL": "http://trackingteste.com.br/orders/12311312"
  })

})

router.post('/logistics/delivery' , (req: any, res: any) => {
   let dados = req.body;

   console.log(dados)

  res.json({
     "deliveryId": uuid(),
     "event": "PENDING",
     "completion": {
       "estimate": moment().add(10, 'minutes').toDate().toISOString(),
       "rejectAfter": moment().add(15, 'minutes').toDate().toISOString()
     },
     "deliveryDetailsURL": "http://yourApiBaseUrl.com/v1/logistics/delivery/*************-11ed-9022-0242ac120002",
     "notifyReadyForPickup": true,
     "notifyPickup": true,
     "notifyConclusion": true,

   })

})


function sendErrorResponse(res: any, err: any) {
  console.error(err);

  let erro =  err instanceof Error ? err.message : 'Unexpected error';

  if(typeof err === 'string')
    erro = err;

  if(Array.isArray(erro))
    erro = err.join(', ');

  res.status(400).json({
    title: erro,
    status: 400
  });
}


export const OpenDeliveryMockDeliveryController: Router = router;
