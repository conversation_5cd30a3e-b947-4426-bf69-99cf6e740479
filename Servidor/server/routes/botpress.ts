import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {TraducaoMensagemBot} from "../domain/chatbot/TraducaoMensagemBot";
import {MapeadorDeTraducaoMensagemChatBot} from "../mapeadores/MapeadorDeTraducaoMensagemChatBot";
import {ProcessadorDeMensagens} from "../utils/mensagens/ProcessadorDeMensagens";
import {Empresa} from "../domain/Empresa";
import {Contato} from "../domain/Contato";
import {NotificacaoService} from "../service/NotificacaoService";
import {Ambiente} from "../service/Ambiente";
import {Usuario} from "../domain/Usuario";
const axios = require('axios');
const randtoken = require('rand-token');
let redis = require("redis");
let client = redis.createClient();

const router: Router = Router();

async function crieLinkCardapio(empresa: Empresa, usuario: Usuario, nome: string, codigoPais: string, telefone: string) {
  return new Promise( (resolve, reject) => {
    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

    notificacaoService.obtenhaLinkCardapio(empresa, nome, codigoPais, telefone, usuario).then( (contexto) => {
      resolve(contexto);
    });
  });
}

async function processeMensagem(empresa: Empresa, usuario: Usuario, nome: string, tel: any, codigoPais: string, mensagem: any) {
  return new Promise( (resolve, reject) => {
    if( true ) {
      resolve(Resposta.sucesso(false));
      return;
    }

    if( !nome ) {
      resolve(Resposta.erro('Nome é obrigatório'));
      return;
    }

    if( !tel ) {
      resolve(Resposta.erro('Telefone é obrigatório'));
      return;
    }

    if( !mensagem ) {
      resolve(Resposta.erro('Mensagem é obrigatório'));
      return;
    }

    const chaveAtendente = 'botativo:' + empresa.id + '_' + tel;

    client.get(chaveAtendente, async (erro: Error, valor: string) => {
      if (valor !== 'true') {
        console.log('bot está ativo: ' + tel);
        return resolve(Resposta.erro('Mia está desativada!'));
      }

      let contato = new Contato(null, nome, tel, null, null, null, null);
      contato.setToken(empresa, randtoken);

      const dados = {
        "type": "text",
        "text": mensagem,
        "includedContexts": ["global"],
        "metadata": {}
      };

      const tokenStr = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.I3rF6LrFFay9-R3XD11PrtefOf0Hp7PY0ooHtxAzg74';
      const url = 'http://localhost:3000/api/v1/bots/meucardapio/converse/' +
        tel + '/secured?include=nlu,state,suggestions,decision';

      axios.post(url, dados, {
        headers: {
          "Authorization": `Bearer ${tokenStr}`,
          'Content-Type': 'application/json'
        }
      }).then(async (resposta: any) => {
        console.log(resposta.data);
        const responses = resposta.data.responses;

        const mensagens = [];

        let emitirBeep = false;
        let marcarComoNaoLida = false;
        const traducaoMensagemBots: Array<TraducaoMensagemBot> = await new MapeadorDeTraducaoMensagemChatBot().listeAsync({});

        const mapTraducoes = traducaoMensagemBots.reduce((acc, traducao: TraducaoMensagemBot) => {
          acc[traducao.template] = traducao;
          return acc;
        }, {} as { [key: string]: TraducaoMensagemBot });

        const originais = [];
        const ignoradas = [];
        for (let response of responses) {
          const objTraducao = mapTraducoes[response.text];

          let msg = '';

          if (response.text === '[ATENDENTE]') {
            emitirBeep = true;
            await ativeDesativeBot(empresa, tel, false); //desativar o bot
          }

          let respostaEncurtarLinks = null;
          if (objTraducao) {
            let contexto = {};
            if (objTraducao.mensagem.indexOf('[Link_Cardapio]') !== -1) {
              contexto = await crieLinkCardapio(empresa, usuario, nome, codigoPais, tel);
            }
            respostaEncurtarLinks = await new ProcessadorDeMensagens(objTraducao.mensagem, false).processe(empresa, contato, contexto);
            msg = respostaEncurtarLinks.mensagemFinal;
          } else {
            ignoradas.push(response.text);
          }

          originais.push(response.text);
          mensagens.push(msg);
        }

        const textoResposta = mensagens.join('[Nova_Mensagem]');

        const objResposta = {
          telefone: tel,
          originais: originais,
          ignoradas: ignoradas,
          mensagem: textoResposta,
          beep: emitirBeep,
          marcarComoNaoLida: marcarComoNaoLida,
          tipo: 'CHAT'
        }

        resolve({
          sucesso: true,
          dados: {
            resposta: objResposta
          }
        });
      }).catch((erro2: any) => {
        console.log(erro2);
        resolve(Resposta.erro(erro2.message));
      });
    });
  });
}


router.get('/conversa', async(req: any, res: any) => {
  const mensagem = req.query.m;
  const telefone = req.query.tel;
  const codigoPais = req.query.codigoPais;
  const nome = req.query.n;
  const empresa = req.empresa;
  const usuario = req.user;

  const resposta = await processeMensagem(empresa, usuario, nome, telefone, codigoPais, mensagem);

  res.json(resposta);
});

router.post('/conversa', async(req: any, res: any) => {
  const mensagem: string = req.body.m;
  const telefone: string = req.body.tel;
  const codigoPais: string = req.body.codigoPais
  const nome: string = req.body.n;
  const empresa = req.empresa;
  const usuario = req.user;

  const resposta = await processeMensagem(empresa, usuario, nome, telefone, codigoPais, mensagem);

  res.json(resposta);
});

async function ativeDesativeBot(empresa: Empresa, telefone: string, ativar: boolean) {
  return new Promise( (resolve, reject) => {
    const chaveAtendente = 'botativo:' + empresa.id + '_' + telefone;

    client.setex(chaveAtendente, 30 * 60 * 1, ativar, (erro: any) => {
      resolve(null);
    });
  });
}

router.post('/ativar', async(req: any, res) => {
  const telefone = req.body.tel;
  const empresa = req.empresa;

  if( !telefone ) {
    return res.json({
      sucesso: false
    });
  }

  const ativar = req.body.ativar;

  console.log('ativando: ' + ativar);

  await ativeDesativeBot(empresa, telefone, ativar);

  res.json(Resposta.sucesso(''));
});

export const BotpressController: Router = router;
