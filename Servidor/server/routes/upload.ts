  import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {Ambiente} from "../service/Ambiente";
import * as async from "async";
import {UploadUtils} from "../utils/UploadUtils";
  import {Empresa} from "../domain/Empresa";

const router: Router = Router();

let path = require('path');
let fs = require('fs');
const uuidv1 = require('uuid/v1');

function erroUpload(res: any , err: any){
  console.log(err)
  res.status(500)
  res.json(err.message || err);
}



router.post('/imagem/logoempresa', async (req: any, res: any) => {
    if(!req.files)
      return erroUpload(res, 'Nenhum arquivo foi enviado.')

    const file: any = req.files.files;
    let extensao: any = file.mimetype.split('/')[1];

    const uiid: string = uuidv1();

    let diretorio = Ambiente.Instance.config.caminhoImagens,
      nomeArquivoFull = String(`${uiid}${Empresa.SUFIXOFULL}.${extensao}`),
      arquivoFull: string = path.join(diretorio, 'empresa', nomeArquivoFull),
      nomeArquivo200: string = String(`${uiid}.${extensao}`),
      arquivoLogo200: string = path.join(diretorio, 'empresa', nomeArquivo200);

    if(!fs.existsSync(diretorio)) {
      console.log(diretorio)
      return erroUpload(res, "Diretório upload de imagens não existe");
    }


    console.log(arquivoFull)
    console.log('mimetype: ' + file.mimetype)

    let err = await file.mv(arquivoFull);

    if(!err){
      UploadUtils.gereImagemMenor(file, 200, arquivoLogo200, (errgerarmenor: string) => {
        if(!errgerarmenor){
          if(!Ambiente.Instance.producao){
            // destination.txt will be created or overwritten by default.
            const arquivoDestino = arquivoFull.replace('/server/', '/distServer/');

            if( arquivoFull !== arquivoDestino ) {
              fs.copyFile(arquivoFull, arquivoDestino, (errCopy: any) => {
                if (errCopy) console.log(errCopy)
              });
            }
          }
          res.json(Resposta.sucesso({file: nomeArquivoFull, extensao: extensao }))
        } else {
          erroUpload(res, errgerarmenor)
        }
      })
    }else {
      erroUpload(res, err)
    }
})


router.post('/imagem/:maxsize', async (req, res) => {
  if(!req.files)
    return erroUpload(res, 'Nenhum arquivo foi enviado.')

  let maxsize = Number(req.params.maxsize)

  const file: any = req.files.files,
    extensao: any = file.mimetype.split('/')[1];

  let diretorio = Ambiente.Instance.config.caminhoImagens,
    nomeArquivo = String(`${uuidv1()}.${extensao}`),
    arquivo: string = path.join(diretorio, 'empresa', nomeArquivo);

  console.log(diretorio);

  if(!fs.existsSync(diretorio)) {
    console.log(diretorio)
    return erroUpload(res, "Diretório upload de imagens não existe");
  }

  console.log(arquivo)
  console.log('mimetype: ' + file.mimetype)

  UploadUtils.redimensioneImagem(file, maxsize, arquivo, (err: any) => {
    if(!err){
      let sizeInBytes = fs.statSync(arquivo).size;
      let sizeInMB = (sizeInBytes / (1024 * 1024));
      console.log(String(`'tamanho máximo upload: ${sizeInMB}MB`))
      if(!Ambiente.Instance.producao){
        // destination.txt will be created or overwritten by default.
        fs.copyFile(arquivo, arquivo.replace('/server/', '/distServer/'), ( errCp: any ) => {
          if (errCp)  console.log(errCp)
        });
      }
      res.json(Resposta.sucesso({file: nomeArquivo, extensao: extensao }))
    } else {
      erroUpload(res, err)
    }
  })
});

router.post('/imagem', async (req: any, res: any) => {
  if(!req.files)
    return erroUpload(res, 'Nenhum arquivo foi enviado.')

  const file: any = req.files.files;
  let extensao: any = file.mimetype.split('/')[1];

  if( extensao === 'x-icon' ) {
    extensao = 'ico';
  }
  let diretorio = Ambiente.Instance.config.caminhoImagens,
    nomeArquivo = String(`${uuidv1()}.${extensao}`),
    arquivo: string = path.join(diretorio, 'empresa', nomeArquivo);

  if(!fs.existsSync(diretorio)) {
    console.log(diretorio)
    return erroUpload(res, "Diretório upload de imagens não existe");
  }

  console.log(arquivo)
  console.log('mimetype: ' + file.mimetype)

  file.mv(arquivo).then( (err: any) => {
    if(!err){
      if(!Ambiente.Instance.producao){
        // destination.txt will be created or overwritten by default.
        const arquivoDestino = arquivo.replace('/server/', '/distServer/');

        if( arquivo !== arquivoDestino ) {
          fs.copyFile(arquivo, arquivoDestino, (errCopy: any) => {
            if (errCopy) console.log(errCopy)
          });
        }
      }

      res.json(Resposta.sucesso({file: nomeArquivo, extensao: extensao }))
    }else {
      erroUpload(res, err)
    }
  })
})

router.post('/imagens/:maxsize', async (req, res) => {
  if(!req.files)
    return erroUpload(res, 'Nenhum arquivo foi enviado.')

  let files: any = req.files.files
  let maxsize = Number(req.params.maxsize)

  if(files.name) files = [files]

  let diretorio = Ambiente.Instance.config.caminhoImagens

  if(!fs.existsSync(diretorio)) {
    console.log(diretorio)
    return erroUpload(res, "Diretório upload de imagens não existe");
  }

  let uploadedFiles: any[] = []

  async.eachSeries(files,
    (file: any, cb) => {
      const extensao: any = file.mimetype.split('/')[1];

      let nomeArquivo = String(`${uuidv1()}.${extensao}`),
        arquivo: string = path.join(diretorio, 'empresa', nomeArquivo);

      console.log(arquivo)
      console.log('mimetype: ' + file.mimetype)

      let i = 0;

      UploadUtils.redimensioneImagem(file, maxsize, arquivo, (err: any) => {
        if(!err){
          if(!Ambiente.Instance.producao){
            // destination.txt will be created or overwritten by default.
            fs.copyFile(arquivo, arquivo.replace('/server/', '/distServer/'),
              ( er: any ) => {
              if (er)  console.log(er)
            });
          }
          uploadedFiles.push({linkImagem: nomeArquivo, ordem: i++ })
          cb()
        }else {
          cb(err)
        }
      })
    },
    (erro: any) => {
      if(!erro)
        return res.json(Resposta.sucesso(uploadedFiles));

      erroUpload(res, erro)
    });

})

router.post('/imagens/crop/:maxsize', async (req: any, res: any) => {
    const { files } = req;

    if (!files || Object.keys(files).length === 0)
      return res.status(400).json({ success: false, message: 'Nenhum arquivo enviado.' });


    const image: any = files.image;
    const maxsize = Number(req.params.maxsize)

    //const base64Image = image.data.toString('base64');
    const diretorio = Ambiente.Instance.config.caminhoImagens;

    if(!fs.existsSync(diretorio)) {
      console.log(diretorio)
      return erroUpload(res, "Diretório upload de imagens não existe");
    }

    const extensao: any = image.mimetype.split('/')[1];

    let nomeArquivo = String(`${uuidv1()}.${extensao}`),
      arquivo: string = path.join(diretorio, 'empresa', nomeArquivo);

    console.log(arquivo);
    console.log('mimetype: ' + image.mimetype)

    fs.writeFileSync(arquivo, image.data);

    if(!Ambiente.Instance.producao){
      // destination.txt will be created or overwritten by default.
      fs.copyFile(arquivo, arquivo.replace('/server/', '/distServer/'), ( errCp: any ) => {
        if (errCp)  console.log(errCp)
      });
    }

    res.json(Resposta.sucesso({linkImagem: nomeArquivo}))
  })

router.post('/imagem/bandeira/svg', (req: any, res: any) => {
  if(!req.files)
    return erroUpload(res, 'Nenhum arquivo foi enviado.')

  const file: any = req.files.files;
  const extensao: any = file.mimetype.split('/')[1];

  console.log('mimetype: ' + file.mimetype)

  if (!file.mimetype.startsWith('image/'))
    return erroUpload(res, 'O arquivo não é uma imagem.');


  let svg = extensao.indexOf('svg') >= 0;

  if(!svg){
    const base64Data = file.data.toString('base64');
    const base64Image = `data:${file.mimetype};base64,${base64Data}`;

    return res.json(Resposta.sucesso(base64Image))
  }

  let diretorio = Ambiente.Instance.config.caminhoImagens,
    nomeArquivo = String(`${uuidv1()}.${extensao}`),
    tempFilePath: string = path.join(diretorio, 'empresa', nomeArquivo);

  if(!fs.existsSync(diretorio)) {
    console.log(diretorio)
    return erroUpload(res, "Diretório upload de imagens não existe");
  }

  file.mv(tempFilePath, (err: any) => {
    if (err)   return erroUpload(res, 'Erro ao salvar o arquivo: ' + err.message);
    if(svg){
      // Leia o SVG resultante
      const svgData = fs.readFileSync(tempFilePath, 'utf-8');
      res.json(Resposta.sucesso(svgData))
      // Limpe os arquivos temporários
      fs.unlinkSync(tempFilePath);
    } else {
      erroUpload(res, 'Imagem não é um svg ou um webp')
    }
  });
})

router.post('/contatos', async (req, res) => {
  const file: any = req.files.files,
    tipo: any = file.mimetype;

  console.log('tipo: ' + tipo)

  let csvData = file.data.toString('utf8');
  let linhas = csvData.split('\n');
  let resposta =  linhas.map( (item: string) => item.split(','))

  if(resposta[0].length === 1)
    resposta =  linhas.map( (item: string) => item.split(';'))

  if(resposta[0].length === 1)
    resposta =  linhas.map( (item: string) => item.split('|'))

  let ultima = resposta.length - 1;

  //remover ultima linha em branco
  if(resposta[ultima].length === 0 || (resposta[ultima].length === 1 && resposta[ultima][0] === ''))
    resposta.splice(ultima, 1);

  res.json(resposta)

})

export const UploadController: Router = router;
