import {Router} from "express";

import {Resposta} from "../utils/Resposta";
import {PedidoService, ProcessadorReciboTexto} from "../service/PedidoService";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {DTOPedido} from "../lib/dto/DTOPedido";
import {Pedido} from "../domain/delivery/Pedido";
import {Modulo} from "../domain/Modulo";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {Ambiente} from "../service/Ambiente";
import {ApiSuperLinkService} from "../service/ApiSuperLinkService";
import * as _ from "underscore";
import {EnumMeioDePagamento} from "../domain/delivery/EnumMeioDePagamento";
import {Transacao} from "../lib/pagseguro-rest";
import {PagamentoPedido} from "../domain/delivery/PagamentoPedido";
import {
  EnumStatusPagamento,
  StatusChargeDeParaPagseguro,
  StatusPagamentoDeParaCielo,
  StatusPagamentoDeParaMercadoPago,
  StatusPagamentoDeParaPagarme,
  StatusPagamentoDeParaRede, StatusPaymentDeParaTunaPay, StatusPaymentDeParaTunaPayWebhook
} from "../lib/emun/EnumStatusPagamento";
import {MapeadorDeAdicionalDeProduto} from "../mapeadores/MapeadorDeAdicionalDeProduto";
import {MapeadorDeComanda} from "../mapeadores/MapeadorDeComanda";
import {DTOComanda} from "../lib/dto/DTOComanda";
import {ComandaService} from "../service/ComandaService";
import {Comanda} from "../domain/comandas/Comanda";
import {FormatadorUtils} from "../lib/FormatadorUtils";
import {IServiceIntegracaoExternaERP} from "../domain/integracoes/IServiceIntegracaoExternaERP";
import {CacheService} from "../service/CacheService";
import {MercadoPagoService} from "../service/meiospagamentos/MercadoPagoService";
import {DTOTransacaoPagSeguro} from "../lib/pagseguro/DTOTransacaoPagSeguro";
import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";
import {CartaoService} from "../service/CartaoService";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {FiltroTelaPedidos} from "../utils/FiltroTelaPedidos";
import {CieloEcommerceService} from "../service/meiospagamentos/CieloEcommerceService";
import {DTOTransacaoCielo} from "../lib/cielo/DTOTransacaoCielo";
import {NotificacaoMeioPagamentoService} from "../service/NotificacaoMeioPagamentoService";
import {DTOPedidoPagarme} from "../lib/pagarme/DTOPedidoPagarme";
import {PagarmeApiService} from "../service/PagarmeApiService";
import {MapeadorDePagamentoPedido} from "../mapeadores/MapeadorDePagamentoPedido";
import {RotaGuard} from "../lib/permissao/RotaGuard";
import {MultipedidoService} from "../service/MultiPedidoService";
import {PedidoGenerico} from "../domain/delivery/PedidoGenerico";
import {MultiPedido} from "../domain/delivery/MultiPedido";
import {IntegradorUtils} from "../utils/IntegradorUtils";
import {EnumOrigemPedido} from "../lib/emun/EnumOrigemPedido";
import {DTODadosPedido} from "../lib/dto/DTODadosPedido";
import {Empresa} from "../domain/Empresa";
import {MapeadorDeAvaliacaoDePedido} from "../mapeadores/MapeadorDeAvaliacaoDePedido";
import {MapeadorDeAvisoDeSistema} from "../mapeadores/MapeadorDeAvisoDeSistema";
import {NotificacaoService} from "../service/NotificacaoService";
import {MapeadorDeHistoricoPedido} from "../mapeadores/MapeadorDeHistoricoPedido";
import {OrderEvent} from "../domain/opendelivery/OrderEvent";
import {EventType} from "../lib/integracao/opendelivery/EventType";
import {APIConversoesFacebookService} from "../service/facebook/APIConversoesFacebookService";
import {IFoodService} from "../service/integracoes/IFoodService";
import {EnumResultadoDisputa} from "../domain/integracoes/PedidoDisputa";
import {MapeadorPedidoFalhaIntegracao} from "../mapeadores/MapeadorPedidoFalhaIntegracao";
import {EnumServicoHorarioFuncionamento} from "../lib/emun/EnumServicoHorarioFuncionamento";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {DTOOrderPagseguro} from "../lib/pagseguro/DTOOrderPagseguro";
import {PagBankConnectAPI} from "../lib/PagBankConnectAPI";
import {MapeadorDeRequestParceiro} from "../mapeadores/MapeadorDeRequestParceiro";
import {RequestParceiro} from "../domain/integracoes/RequestParceiro";
import {MapeadorPedidoDisputa} from "../mapeadores/MapeadorPedidoDisputa";
import {MapeadorPedidoAlteracaoEndereco} from "../mapeadores/MapeadorPedidoAlteracaoEndereco";
import {DTOTransacaoERede} from "../lib/erede/DTOTransacaoERede";
import {ERedeItauApi} from "../lib/ERedeItauApi";
import {EnumStatusPagamentoRede} from "../lib/emun/EnumStatusPagamentoRede";
import {IMeioPagamentoService} from "../service/meiospagamentos/IMeioPagamentoService";
import {FactoryServicosPagamento} from "../service/integracoes/FactoryServicosPagamento";
const puppeteer = require('puppeteer');
const ejs = require('ejs');
// @ts-ignore
import moment = require("moment");
// @ts-ignore
import crypto = require('crypto');
import ErrnoException = NodeJS.ErrnoException;
import {createClient} from "redis";
import {MapeadorDeFormaDePagamento} from "../mapeadores/MapeadorDeFormaDePagamento";
import {DTOPagamentoTunaPay} from "../lib/tunapay/DTOPagamentoTunaPay";
import {TunaPayApi} from "../lib/TunaPayApi";
import {TarefaMensagemPagamentoPendente} from "../domain/TarefaMensagemPagamentoPendente";
const Redlock = require('redlock');
// Crie o cliente Redis
const redisClient = createClient();
// Crie o Redlock
// @ts-ignore
const redlock = new Redlock(
  // @ts-ignore
  [redisClient],
  {
    retryCount: 30,
    retryDelay: 500, // tempo em ms
  }
);

let fs = require('fs');
let path = require('path');
const tempoDaRequest = 60 * 1000; //60s 60000

function tahLogado(req: any, res: any, next: any){
  if(req.user) return next();

  res.status(401).json('')
}



const router: Router = Router();


async function retornePedidos(req: any, res: any){
  let formatHU = 'YYYYMMDDHHmmss', dados: any = req.query;

  let query: any = new FiltroTelaPedidos(dados, req.empresa, req.user).toSqlQuery();

  let pedidos = await new MapeadorDePedido().listeAsync(query);

  //dark principal nao retornar pedidos internos do multipedido
  pedidos = pedidos.filter((pedido: any) => !pedido.multipedido || req.empresa.id !== pedido.multipedido.empresa.id);


  let resposta: any = {
    pedidos: pedidos.map( (pedido: any) => new DTOPedido(pedido, req.empresa)),
    pedidosPagos: []
  };

  const empresa: any  = req.empresa;

  if(empresa && empresa.integracoesIfood.length){
    //pinga so se tiver dentro horario funcionamento loja
    empresa.setHorariosFuncionamento(EnumServicoHorarioFuncionamento.Site);

    for(let i = 0; i < empresa.integracoesIfood.length; i++){
      let integracaoIfood = empresa.integracoesIfood[i];

      if(integracaoIfood.desativado) continue;

      if( integracaoIfood.distribuida()){
        if(empresa.estaRecebendoPedidos)
          await new IFoodService(integracaoIfood).facaPollingDaLoja()
      } else {
        CacheService.pingMerchant(empresa);
      }
    }

    let contestados: any = await new MapeadorPedidoDisputa().listeAsync({  });

    resposta.contestados = contestados.map( (disputa: any) => new DTOPedido(disputa.pedido, req.empresa));

    let pedidosEnderecoAlterado: any = await new MapeadorPedidoAlteracaoEndereco().listeAsync({  });

    resposta.pedidosEnderecoAlterado = pedidosEnderecoAlterado.map( (item: any) => new DTOPedido(item.pedido, req.empresa));

    let naoIntegrados = await new MapeadorPedidoFalhaIntegracao().listeAsync({naoLidos: true});

    resposta.falhasNaImportacao = naoIntegrados.map((item: any) => item.toDTO());
  }


  if( dados.ni ){ // nao impressos
    resposta.naoImpressos = await  new MapeadorDePedido().listePedidoCompletos( {naoImpressosHoje: true, emAberto: true} );

    //nao retornar online nao pagos
    if(empresa.configImpressao && !empresa.configImpressao.imprimirOnlineNaoPago)
      resposta.naoImpressos = resposta.naoImpressos.filter((pedido: any) => !pedido.pagarOnline()  || pedido.pago)

    resposta.naoImpressos =   resposta.naoImpressos.map( (pedido: any) => new DTOPedido(pedido, req.empresa))
  }


  if(dados.onids ){
    let ids = dados.onids.split(',').map((item: any) => Number(item));

    let pedidosPagosOnline =  await new MapeadorDePedido().listeAsync({ ids: ids , pagos: true});

    pedidosPagosOnline.forEach((pedido: Pedido) => {
      resposta.pedidosPagos.push( new DTOPedido(pedido, req.empresa));
    })
  }

  if(!query.orderByDesc){
    if(!query.filtroAgendados)
      resposta.pedidos =   _.sortBy( resposta.pedidos, (dtoPedido: DTODadosPedido) => dtoPedido.horario);
    else
      resposta.pedidos = _.sortBy(resposta.pedidos, (dtoPedido: DTODadosPedido) => dtoPedido.horarioEntregaAgendada)
  }

  if(dados.av) {
    let mapeadorAvisos = new MapeadorDeAvisoDeSistema()

    resposta.avisos = await mapeadorAvisos.obtenhaPendentes()

    await mapeadorAvisos.marqueLidos(req.empresa, resposta.avisos);
  }

  let ultimoPedido: DTODadosPedido =
    _.sortBy(   resposta.pedidos, (pedido: DTODadosPedido) => -moment(pedido.horarioAtualizacao).format(formatHU))[0];

  resposta.ultimaAtualizacao =  ultimoPedido ? moment(ultimoPedido.horarioVenda).format(formatHU) : null

  res.json(Resposta.sucesso(resposta))
}


router.get('/',  tahLogado, retornePedidos)

router.get('/novos',  tahLogado, retornePedidos)

router.get('/atualizados',  tahLogado, retornePedidos)

router.get('/me', async(req: any, res: any) => {
  let contatoLogado = req.session.contatoLogado;

  if(contatoLogado && contatoLogado.id){
    console.log('buscar de pedidos: ' + contatoLogado.nome)
    let dados = req.query;
    let query: any = { inicio: dados.i || 0, total: dados.t || 10 , idContato: contatoLogado.id, orderByDesc: true};

    query.inicio = Number(query.inicio);
    query.total = Number(query.total);


    let pedidos = await new MapeadorDePedido().listeAsync(query);

    res.json(Resposta.sucesso(pedidos.map( (pedido: any) => new DTOPedido(pedido, req.empresa))))
  } else {
    res.json(Resposta.sucesso([]))
  }
})

router.get('/integrados/importacao/falhas',   async (req: any, res: any) => {
  let query: any = { inicio: Number(req.query.i), total: Number( req.query.t)};

  if(req.params.no === 1)
    query.naoLidos = true;

  let errosImportacao = await new MapeadorPedidoFalhaIntegracao().listeAsync(query);

  errosImportacao = errosImportacao.map((item: any) => item.toDTO());

  let total = await new MapeadorPedidoFalhaIntegracao().selecioneTotal({});

  res.json(Resposta.sucesso( { errosImportacao: errosImportacao, total: total}));

})

router.put('/integrados/importacao/falhas/:id/lida',    async (req: any, res: any) => {
  await new MapeadorPedidoFalhaIntegracao().atualizeLido({id: req.params.id});

  res.json(Resposta.sucesso());
})

router.get('/integrados/erros',  RotaGuard.ehAdmin, async (req: any, res: any) => {
  let mapeador =   new MapeadorDePedido();

  let filtro: any = req.query;

  mapeador.desativeMultiCliente();

  let query: any = {
    inicio: req.query.i != null ? Number(req.query.i) : 0,
    total:  req.query.t != null ? Number(req.query.t) : 25,
    dataRealizacao: moment().add(-7, 'd').format('YYYY-MM-DD 00:00:00'),
    orderByDesc : true, erroIntegracao: true, todos: true
  }

  if(filtro.ultimoPedido)
     query.idUltimo = Number(filtro.ultimoPedido)


  let pedidos = await new MapeadorDePedido().listeAsync(query);

  //setar nao notificaod no dto
  if(!req.empresa.integracaoDelivery) req.empresa.integracaoDelivery = {}

  pedidos = pedidos.map( (pedido: any) => new DTOPedido(pedido, req.empresa))


  res.json(Resposta.sucesso(pedidos))
})

router.get('/integrados/erros/ultimo/:tempo', async (req: any, res: any) => {
  let tempoAtras = Number(req.params.tempo);
  let filtro: any = { erroIntegracao: true, todos: true};

  let mapeador =   new MapeadorDePedido();

  mapeador.desativeMultiCliente();

  let pedidos = await mapeador.listeUltimos(filtro);
  let ultimo;

  for(let i = pedidos.length - 1; i >= 0 ; i--){
    if(moment().diff(moment(pedidos[i].horario), 's')  >= tempoAtras)
      ultimo = pedidos[i];
  }

  let resposta: any = { ultimo: ultimo }


  res.json(Resposta.sucesso(resposta));
})

router.get('/adicionais-pedido', async(req: any, res) => {
  const empresa = req.empresa;

  let mapeadorDeAtributo = new MapeadorDeAdicionalDeProduto();

  const dados = {
    catalogo: {
      id: empresa.catalogo.id
    },
    empresa: {
      id: empresa.id
    },
    objeto: {
      id: empresa.catalogo.id
    }
  };

  mapeadorDeAtributo.listeAsync(dados).then( (adicionais) => {
    adicionais.forEach((adicional: any) => {
      if(adicional.campoOrdenar && adicional.opcoesDisponiveis )
        adicional.opcoesDisponiveis = _.sortBy( adicional.opcoesDisponiveis, (opcao: any) => opcao[adicional.campoOrdenar])
    })

    res.json(Resposta.sucesso(adicionais));
  });
});

router.get('/:guid/valide/pedirnovamente', async (req: any, res: any) => {
  let pedido = await Pedido.get({guid: req.params.guid });
  let empresa: any = req.empresa;
  let erro: string;

  erro = await new PedidoService().validePedirNovamente(empresa, pedido);

  if(!erro){
    let dtoPedido = new DTOPedido(pedido, req.empresa);

    dtoPedido.itens.forEach((item: any) => {
       item.produto.empresa = {id: empresa.id, nome: empresa.nome, dominio: empresa.dominio, logo: empresa.logo}
    })

    res.json(Resposta.sucesso(dtoPedido));
  } else {
    res.json(Resposta.erro(erro))
  }
});

router.get('/chave/:guid', async (req: any, res: any) => {
  let pedido = await Pedido.get({guid: req.params.guid });
  let operador  = req.user;

  if( !pedido ){
    pedido = await Pedido.get({codigo:  req.params.guid});
    if(!pedido)
       return res.json(Resposta.erro("Não encontrado!"));
  }

  let dtoPedido: DTODadosPedido  = new DTOPedido(pedido, req.empresa);

  if(req.query.imprimir && operador)
     dtoPedido.operador = { nome: operador.nome}

  res.json(Resposta.sucesso(dtoPedido))
});

router.get('/:guid/historico', async (req: any, res: any) => {

  let lista = await new MapeadorDeHistoricoPedido().listeAsync({ guid: req.params.guid})


  res.json(Resposta.sucesso(lista))
});

router.get('/:id/request/historico', async (req: any, res: any) => {

  let lista = await new MapeadorDeRequestParceiro().listeAsync({ idPedido: req.params.id})

  lista.forEach((requestParceiro: RequestParceiro) => {
    requestParceiro.setDados();
  })

  res.json(Resposta.sucesso(lista))
});

router.get('/:guid/pagamento/:id/sincronize/:status', async (req: any, res: any) => {
  let pedido: Pedido , lock: any, idPagamento = Number( req.params.id), statusEnviado = req.params.status;

  try{
    const resourceLockKey = `monitorar:${req.params.guid}`;
    // lock morrerr depois de 30s se nao liberado
    lock = await redlock.acquire([resourceLockKey], tempoDaRequest);
    pedido = await Pedido.get({guid: req.params.guid });

    if(!pedido) return res.json(Resposta.erro("Pedido não encontrado: " +  req.params.guid))

    let pagamentoPrincipal: PagamentoPedido = pedido.pagamentos.find((item: any) => item.id === idPagamento)

    if(!pagamentoPrincipal) return res.json(Resposta.erro('Pagamento nao encontrado'))

    if(pagamentoPrincipal.status.toString() === statusEnviado){
      if(pagamentoPrincipal && pagamentoPrincipal.aguardandoAprovar()){
        let sincronizou = await sincronizePagamentoOnline(pedido, pagamentoPrincipal, req.empresa);

        if(sincronizou){
          let dtoPedido: DTODadosPedido = new DTOPedido(pedido, req.empresa)

          res.json(Resposta.sucesso(dtoPedido))
        } else {
          res.json(Resposta.sucesso())
        }
      } else {
        res.json(Resposta.sucesso())
      }
    } else {
      //ja sincronou retornar aqui
      let dtoPedido: DTODadosPedido = new DTOPedido(pedido, req.empresa)

      res.json(Resposta.sucesso(dtoPedido))
    }
  } catch (err){
    console.log(err)
    res.status(500).json({ erro: err})
  }  finally {
    if(lock) {
      try {
        await lock.unlock()
      } catch (er) {
        console.log('falha ao liberar lock')
        console.error(er)
      }
    }
  }
});

router.get('/monitoramento/:guid', async (req: any, res: any) => {
  let pedido: Pedido , lock: any;
  try{
    const resourceLockKey = `monitorar:${req.params.guid}`;
    // lock morrerr depois de 60s se nao liberado
    lock = await redlock.acquire([resourceLockKey], tempoDaRequest );
    pedido = await Pedido.get({guid: req.params.guid });
    if(!pedido)
      return res.json(Resposta.erro("Pedido não encontrado: " +  req.params.guid))

    let pagamentoPrincipal: PagamentoPedido;

    let dtoPedido: DTODadosPedido = new DTOPedido(pedido, req.empresa)
    pagamentoPrincipal = pedido.obtenhaPagamentoOnline() as PagamentoPedido;

    let avaliacao = await new MapeadorDeAvaliacaoDePedido().selecioneSync({idPedido: pedido.id});

    if(!dtoPedido.pagamentoOnline){
      return res.json(Resposta.sucesso({
        pedido: dtoPedido,
        avaliacao: avaliacao,
        pagamento: {
          online: false,
          pago: dtoPedido.pago
        }   }
      ))
    }

    pagamentoPrincipal.status = parseInt(pagamentoPrincipal.status.toString(), 10)

    if(pagamentoPrincipal.aguardandoAprovar())
      await sincronizePagamentoOnline(pedido, pagamentoPrincipal, req.empresa);

    let dadosResposta: any = {
      pedido: dtoPedido,
      pagamento: {
        online: true,
        codigoQrCode: pagamentoPrincipal.codigoQrCode,
        pago: pagamentoPrincipal.foiAprovado(),
        status: pagamentoPrincipal.status,
        tipoOnline: pedido.gatewayPagamentoOnline(),
        mensagem: pagamentoPrincipal.motivoReprovacao || pagamentoPrincipal.obtenhaMensagemStatus(),
        formaDePagamento: pagamentoPrincipal.formaDePagamento,
        urlAutenticar: !pagamentoPrincipal.codigoTransacao ? pagamentoPrincipal.urlAutenticar : null
      }
    }

    if( pagamentoPrincipal.status.toString() === EnumStatusPagamento.Negado.toString() ||
      pagamentoPrincipal.status.toString() === EnumStatusPagamento.Cancelado.toString() ||
      pagamentoPrincipal.naoGerouPix()
    ) {
      dadosResposta.pagamento.falhaNoPagamentoOnline = true
      dadosResposta.pagamento.mensagemFalhaPagamento = dadosResposta.pagamento.mensagem
    }
    else if(pagamentoPrincipal.status.toString() === EnumStatusPagamento.Reembolsado.toString()) {
      dadosResposta.pagamento.foiReembolsado = true
      dadosResposta.pagamento.mensagemFalhaPagamento = dadosResposta.pagamento.mensagem
    }

    res.json(Resposta.sucesso(dadosResposta))

  } catch (err){
    console.log(err)
    res.status(500).json({ erro: err})
  }  finally {
    if(lock) {
      try {
        await lock.unlock()
      } catch (er) {
        console.log('falha ao liberar lock')
        console.error(er)
      }
    }
  }
})


async function  sincronizePagamentoOnline(pedido: Pedido, pagamento: PagamentoPedido, empresa: any ){
  let formaDePagamento = await
    new MapeadorDeFormaDePagamento().selecioneSync({id: pagamento.formaDePagamento.id});

  let meio: string = formaDePagamento && formaDePagamento.configMeioDePagamento ?
    formaDePagamento.configMeioDePagamento.meioDePagamento : null;

  if(meio){
    let config =  pagamento.integradoComGateway()  ? empresa.integracaoGatewayPagamento : formaDePagamento.configMeioDePagamento;

    let service: IMeioPagamentoService = FactoryServicosPagamento.factory(meio, config);

    let statusAntigo = pagamento.status.toString()

    await service.sincronizePagamento(pedido, pagamento, empresa )


    return statusAntigo !== pagamento.status.toString()

  }



}
router.get('/:codigo', async (req: any, res: any) => {
  let pedido = await Pedido.get({codigo: req.params.codigo });

  if(pedido){
    let dtoPedido = new DTOPedido(pedido, req.empresa)
    res.json(Resposta.sucesso(dtoPedido))
  } else {
    res.json(Resposta.sucesso({}))
  }
});

router.get('/:codigo/txt', async (req: any, res: any) => {
  let pedido = await Pedido.get({codigo: req.params.codigo });

  let recibo = new ProcessadorReciboTexto(42).obtenhaRecibo(pedido);

  res.set('Content-Type', 'text/plain');
  res.send(recibo);
})

router.get('/ultimo/:tempo', async (req: any, res: any) => {
  let tempoAtras = Number(req.params.tempo);
  let filtro: any = {};
  let empresa: Empresa = req.empresa;

  //hops nao filtrar origem
  if(req.query.o && empresa && !empresa.ehHops())
    filtro.origens = req.query.o.split(',');

  let pedidos = await  new MapeadorDePedido().listeUltimos(filtro);
  let ultimo;

  for(let i = pedidos.length - 1; i >= 0 ; i--){
    if(moment().diff(moment(pedidos[i].horario), 's')  >= tempoAtras)
      ultimo = pedidos[i];
  }

  let resumoMesas: any =  { totalNovo: 0, mesas: []}

  resumoMesas.mesas = await new MapeadorDePedido().listeTotaisDePedidosMesasAbertos();

  resumoMesas.totalNovo = resumoMesas.mesas.reduce( (sum: any, mesa: any) => sum + mesa.totalNovo, 0);

  let resposta: any = { ultimo: ultimo, resumoMesas: resumoMesas}

  if(empresa.vendeOnline()){
    let pedidosAguardando =   await new MapeadorDePedido().listeAguardandoPagamento();

    resposta.pedidosMonitorarPagamento = pedidosAguardando.map((pedido: any) => pedido.id);
  }

  res.json(Resposta.sucesso(resposta));
})

router.get('/comanda/:id', async(req: any, res: any) => {
  const id = req.params.id;
  const completo = req.query.completo === 'true';
  const cobrarTaxa = req.empresa.cobrarTaxaServico;
  const mapeadorDeComanda = new MapeadorDeComanda();

  let comanda = await  mapeadorDeComanda.obtenhaComanda({id: id}, completo);


  if(!comanda.estaFechada()){ // imprimir sem taxa
    if((comanda.taxaServico && !cobrarTaxa)){
      comanda.cobrarTaxaServico = false;
      comanda.atualizeValor();
    }
  }

  res.json(Resposta.sucesso(new DTOComanda(comanda,  req.empresa)));
});

router.post('/comanda/garcom', async (req: any, res: any) => {
  let dadosComanda = req.body;

  new ComandaService().vinculeGarcomOuCodigoComanda(dadosComanda).then( (comanda: any) => {
   res.json(Resposta.sucesso(new DTOComanda(comanda,  req.empresa)))
  } ).catch( (erro: any) => {

    res.json(Resposta.erro(erro));
  })
})

router.post('/comanda/feche', async (req: any, res: any) => {
  let dados = req.body;
  let empresa = req.empresa;

  dados.empresa = empresa;
  dados.operador = req.user;

  const comandaService: ComandaService = new ComandaService();

  comandaService.feche(dados, empresa).then( (comanda: Comanda) => {
    let resposta: any = { comanda: new DTOComanda(comanda,  empresa)};

    res.json(Resposta.sucesso(resposta));
  }).catch((erro) => {
    console.error(erro)
    res.json(Resposta.erro(erro.message || erro))
  })
});


router.post('/comanda/taxa', async (req: any, res: any) => {
  const mapeadorDeComanda = new MapeadorDeComanda();

  const comanda: Comanda = await mapeadorDeComanda.obtenhaComanda({id: req.body.id}, true)

  if(comanda){
    if(comanda.cobrarTaxaServico !==  req.body.cobrarTaxaServico){
      comanda.cobrarTaxaServico = req.body.cobrarTaxaServico;
      await comanda.atualizeValor();
      await mapeadorDeComanda.atualizeSync(comanda);
    }
  }

  res.json(Resposta.sucesso());
});


router.put('/',  RotaGuard.editarPedido, async (req: any, res: any) => {
  let dados = req.body;
  let empresa = req.empresa;

  dados.empresa = req.empresa;
  dados.operador = req.user;

  let pedidoService = new PedidoService();

  let pedidoAntigo: Pedido = await new MapeadorDePedido().selecioneSync(dados.id);

  if(pedidoAntigo.doIfood())
    return res.json(Resposta.erro(String(`Pedidos do ifood não podem ser alterados`)))

  if(pedidoAntigo.pago)
    return res.json(Resposta.erro(String(`Pedido #${pedidoAntigo.codigo} já foi pago, alteração não permitida.`)))

  if(pedidoAntigo.foiCanceladoOuDevolvido())
    return res.json(Resposta.erro(String(`Pedido #${pedidoAntigo.codigo} está cancelado", alteração não permitida.`)))

  if(pedidoAntigo.multipedido)
    return res.json(Resposta.erroMultipedido)

  dados.totalAntigo = pedidoAntigo.obtenhaTotal();

  let mapeadorDeAtributo = new MapeadorDeAdicionalDeProduto()
  let adicionaisPedido = await mapeadorDeAtributo.listeAsync({ catalogo: req.empresa.catalogo, objeto: req.empresa.catalogo, entidade: 'pedido'})

  dados.definicaoAdicionais = adicionaisPedido


  let novoPedido: any = await pedidoService.obtenhaPedidoPelosDados(empresa, dados, false).catch( e => {
    console.log('Erro obter novo pedido');
    console.log(e);
    res.json(Resposta.erro(e.message ? e.message : e))
  })

  if(novoPedido){
    let resposta = { id: pedidoAntigo.id, codigo: pedidoAntigo.codigo, guid: pedidoAntigo.guid  },
      erroNotificar: string;

    if(pedidoAntigo.comanda && pedidoAntigo.comanda.id)
       novoPedido.comanda = pedidoAntigo.comanda;

    if(novoPedido.erroDePagamento())
      return   res.json(Resposta.erro(novoPedido.erroDePagamento()))

    if(empresa.integracaoPDVParceiroAtiva() && pedidoAntigo.referenciaExterna){
      let service: IServiceIntegracaoExternaERP = empresa.integracaoDelivery.obtenhaService();

      let uuid: any =  await service.alterePedido(novoPedido, pedidoAntigo, empresa).catch( (erro) => {
        console.log('Erro alterar pedido existente');
        console.log(erro);
        erroNotificar = erro;
      })

      novoPedido.referenciaExterna = uuid;
    }

    if(!erroNotificar){
      pedidoService.atualizePedido(novoPedido,  dados.totalAntigo, req.user).then( async () => {
        if(pedidoAntigo.pagarOnline() && empresa.notificarNovoPedidoAoSistemaIntegrado(novoPedido))
          IntegradorUtils.notifiqueNovoPedidoAsync(empresa, novoPedido);

        res.json(Resposta.sucesso(resposta));
      }).catch((e: any) => {
        console.error(e)
        res.json(Resposta.erro(e.message ? e.message : e))
      })
    } else {
      res.json(Resposta.erro(erroNotificar))
    }
  }
})

router.put('/:guid/impresso' , async (req: any, res: any) => {
  let pedido: any = await Pedido.get({ guid: req.params.guid})
  let operador = req.user;

  console.log(req.body);

  if(!pedido) return res.json(Resposta.erro('Pedido inválido'))

  await  new PedidoService().marquePedidoImpresso(pedido, operador, req.body.impressaoAutomatica);

  res.json(Resposta.sucesso())

})

router.put('/:guid/cancele', RotaGuard.alterarPedido, async(req: any, res: any) => {
  let motivo = req.body.motivo || {};
  let guid =  req.params.guid;
  let notificar: boolean = req.body.notificar != null;
  const empresa = req.empresa

  console.log(`Cancelar o pedido ${guid} - ${empresa.nome}`);
  let pedido: Pedido = await Pedido.get({guid: guid});

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'));

  if(pedido.deliveryPedido  && pedido.deliveryPedido.foiAceita){
    if(pedido.deliveryPedido.podeCancelar())
      return  res.json(Resposta.erro("Existe um entregador alocado, cancele primeiro a entrega"));
    else
      return res.json(Resposta.erro("Entregador está com o pedido, pedido não pode ser cancelado"));

  }

  if(pedido.doIfood()){
    let service = new IFoodService(req.empresa.obtenhaIntegracaoIfood(pedido.idLojaExterna));

    let erroCancelarIfood: string;

    let orderEvent: any =  await service.notifiqueCancelamentoPedido(pedido, motivo).catch((erroIfood: any) => {
      erroCancelarIfood = erroIfood
    });

    if(orderEvent && orderEvent.id){
      return res.json(Resposta.sucesso({ aguardandoPdv: true, idEvent: orderEvent.eventId }))
    } else {
      return res.json(Resposta.erro(erroCancelarIfood));
    }
  }

  if(!empresa.integracaoOpenDeliveryComercianteAtiva()){
    let resposta: any =
      await new PedidoService().operadorCancelou(pedido, req.empresa, motivo  , notificar,  req.user);

    if(!resposta.erro){
      res.json(Resposta.sucesso(resposta));
    } else {
      res.json(Resposta.erro(resposta.erro));
    }
  } else {
    let orderEvent = new OrderEvent(pedido.guid,  EventType.ORDER_CANCELLATION_REQUEST, empresa);

    orderEvent.reason = motivo;

    await orderEvent.salve(true);

    res.json(Resposta.sucesso({aguardandoPdv: true, idEvent: orderEvent.eventId}));
  }
})

router.get('/:guid/cancelamento/status/:eventId', async (req: any, res: any) => {
  const eventId: string = req.params.eventId;
  const delivery = req.query.de != null;

  let resposta: any = {
    pdvReconheceu: true,
    tempoPressamento: null, rejeitouCancelamento: null, motivo: null, cancelado: null
  }

  let orderEvent = await OrderEvent.obtenha({ eventId: eventId })

  if(orderEvent ){
    resposta.pdvReconheceu = orderEvent.reconhecido;
    resposta.tempoPressamento = moment().diff(orderEvent.createdAt, 'seconds')

    if(orderEvent.reasonDeny){
      resposta.rejeitouCancelamento = true;
      resposta.motivo = orderEvent.reasonDeny;
    } else {
      let pedido = await new MapeadorDePedido().selecioneSync({guid: req.params.guid , semItens: true})

      if(pedido){
        if(delivery && pedido.deliveryPedido.foiCancelado()){
          resposta = pedido.deliveryPedido.toDTO();
          resposta.cancelado = true;
        } else if(pedido.foiCanceladoOuDevolvido()) {
          resposta.cancelado = true ;
        }
      }
    }

    res.json(Resposta.sucesso(resposta))
  } else {
    res.json( Resposta.erro('Event inválido'))
  }
});

router.put('/:guid/cancele/loja' , async (req: any, res: any) => {
  let pedido: any = await Pedido.get({ guid: req.params.guid})
  let empresa = req.empresa;
  let motivo = 'Pedido cancelado pelo cliente';

  if(!pedido) return res.json(Resposta.erro('Pedido inválido'))

  let pagamentoOnline =  pedido.obtenhaPagamentoOnline();

  if(pagamentoOnline && pagamentoOnline.foiAprovado())
    return res.json(Resposta.erro('Cancelamento nao permitido, pagamento já foi aprovado.'))

  let erro: any = await
    new PedidoService().cancelePedidoAguardando(pedido, empresa, null, false, motivo);

  if(!erro){
    if(empresa.integracaoPDVParceiroAtiva() && pedido.referenciaExterna){
      let service: IServiceIntegracaoExternaERP = empresa.integracaoDelivery.obtenhaService();

       await service.cancelePedido(pedido, motivo);
    }

    res.json(Resposta.sucesso(new DTOPedido(pedido, empresa)));
  }  else{
    res.json(Resposta.erro(erro))
  }

})

async function facaCheckoutPix(empresa: any, pedido: PedidoGenerico, resposta: any){
  const pagamento = pedido.obtenhaPagamentoOnline() as PagamentoPedido;
  const configMeioDePagamento: any = pagamento.formaDePagamento.configMeioDePagamento;
  let transacao: any;

  if(pagamento.gatewayTunaPay()) {
    let dtocheckout: DTOPagamentoTunaPay  = new DTOPagamentoTunaPay(pedido, { email: pagamento.email, cpf: pagamento.cpf});
    const taxaPix = pedido.empresa.meucardapioPay.contratoMeucardapioPay.taxaPix;
    const pixTaxaMinima = pedido.empresa.meucardapioPay.pixTaxaMinima;

    let descricaoPagador: string = configMeioDePagamento.nomeFaturaCartao;

    if(!descricaoPagador) descricaoPagador = empresa.dominio;

    dtocheckout.setPagamentoPix(taxaPix, pixTaxaMinima, descricaoPagador);

    let servie: TunaPayApi = new TunaPayApi();

     transacao = await servie.criePagamento(dtocheckout).catch(async (erroPix: any) => {
      resposta.falhaNoPagamentoOnline = true;
      resposta.mensagemFalhaPagamento = erroPix;
      pagamento.motivoReprovacao = 'Falha ao gerar Pix: ' + erroPix
      await new MapeadorDePagamentoPedido().atualizeDados(pagamento)
    });

    if(transacao){
      transacao.dataExpiracao = dtocheckout.obtenhaDataExpiracao();
      await pagamento.atualizeRetornoTunaPay(transacao, dtocheckout.getValorTaxaSplitMeucadapio(taxaPix, pixTaxaMinima));
      if(pedido.feitoNoIframe())
          resposta.gerarMensagemlinkPagamento = true
    }

  } else if(pagamento.gatewayERede()) {
    let dtocheckout: DTOTransacaoERede  = new DTOTransacaoERede(pedido);
    dtocheckout.setPagamentoPix();

    let serviceERede: ERedeItauApi = new ERedeItauApi(configMeioDePagamento);

    transacao = await serviceERede.crieTransacao(dtocheckout).catch(async (erroPix: any) => {
      resposta.falhaNoPagamentoOnline = true;
      resposta.mensagemFalhaPagamento = erroPix;
      pagamento.motivoReprovacao = 'Falha ao gerar Pix: ' + erroPix
      await new MapeadorDePagamentoPedido().atualizeDados(pagamento)
    });

    if(transacao){
      //salvar codigo pedido na 1x para futuras consultas/sincronização por codigo transação criado e nao depender de notificação erede
      pagamento.codigo = dtocheckout.reference;
      pagamento.dataExpiracao = dtocheckout.obtenhaDataExpiracao();
      await pagamento.atualizeRetornoRede(transacao);
    }

  } else if(pagamento.hubPagarme()){
    let dtocheckoutpagarme =
      new DTOPedidoPagarme(pedido,  {email: pagamento.email, cpf: pagamento.cpf}, null, null,
        "meucardapio_ai");

    console.log(JSON.stringify(dtocheckoutpagarme))

    let pagarmeApiService = new PagarmeApiService(empresa.integracaoGatewayPagamento.privateKey,  pagamento.hubPagarme());

    transacao = await   pagarmeApiService.criePedido(dtocheckoutpagarme).catch(async (erroPix: any) => {
      resposta.falhaNoPagamentoOnline = true;
      resposta.mensagemFalhaPagamento = erroPix;
      pagamento.motivoReprovacao = 'Falha ao gerar Pix: ' + erroPix
      await new MapeadorDePagamentoPedido().atualizeDados(pagamento)
    });

    if(transacao){
      transacao.dataExpiracao = dtocheckoutpagarme.obtenhaDataVencimento();
      await pagamento.atualizePixPagarme(transacao);
    }


  } else {
    transacao =
      await  new MercadoPagoService(configMeioDePagamento.token).
      criePagamentoQrCodePix(pagamento.email, pedido).catch( async (erroPix: any) => {
        resposta.falhaNoPagamentoOnline = true;
        resposta.mensagemFalhaPagamento = erroPix;
        pagamento.motivoReprovacao = 'Falha ao gerar Pix: ' + erroPix
        await new MapeadorDePagamentoPedido().atualizeDados(pagamento)
      });

    if(transacao){
      // Mercado Pago define um prazo padrão de 1 hora (60 minutos)
      transacao.dataExpiracao =  moment().add(60, 'm').toDate();
      await pagamento.atualizeRetornoMercadoPagoPix(transacao);
    }
  }

  if(transacao){
    await new PedidoService().mudouStatusPagamento(pedido as Pedido, pagamento, EnumStatusPagamento.Gerado,
      pagamento.codigoTransacao, transacao.motivoReprovacao);

    if(pagamento.gatewayTunaPay())
      await new TarefaMensagemPagamentoPendente(pagamento, pedido).salve(true);

    pagamento.pedido = pedido;
    await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamento, empresa);

    resposta.statusPagseguro = pagamento.status
    resposta.codigoTransacao = pagamento.codigoTransacao
    resposta.qrCode = pagamento.codigoQrCode;
  }
}

function facaCheckoutCartaoTuna(empresa: any, dados: any, pedido: PedidoGenerico, resposta: any){
  return new Promise(  async (resolve) => {

    try{
      const pagamento = pedido.obtenhaPagamentoOnline() as PagamentoPedido;

      let configMeioPagamento: any = pagamento.formaDePagamento.configMeioDePagamento

      let descricaoCartao = configMeioPagamento.nomeFaturaCartao;

      if(!descricaoCartao) descricaoCartao = empresa.dominio; //"MeuCar tuna"

      let pagamentoVindoDaTela: any = dados.pagamentos.find( (pagamentoTela: any) => pagamentoTela.dadosCartao);

      let dtocheckout = new DTOPagamentoTunaPay(pedido, pagamentoVindoDaTela.dadosCartao);

      const taxaCartao = pedido.empresa.meucardapioPay.contratoMeucardapioPay.taxaCartao;
      const taxaMinimaCartao   = 0;

      let dadosCartao: any =  pagamentoVindoDaTela.dadosCartao;

      if(dadosCartao && dadosCartao.deviceInfo){
        dadosCartao.deviceInfo.userAgent = dados.userAgent;
        dadosCartao.deviceInfo.ip = dados.ip;
      }

      let servie: TunaPayApi = new TunaPayApi();

      //validar token google pay
      if(dadosCartao.externalProvider === 'GooglePay'){
          let erroValidarToken: any;
          console.log('token google', dadosCartao.token)
          let tokenValidado: any  = await servie.valideGoogleToken(dadosCartao.tokenSession, dadosCartao.token).catch((errToken) => {
            erroValidarToken = errToken
          });

          if(erroValidarToken)    throw   Error(erroValidarToken);

          console.log('token google validado: ', tokenValidado)
         // dadosCartao.token  = tokenValidado.messageId;
      }

      if(!dadosCartao.endereco &&  pedido.endereco)
        dadosCartao.endereco = pedido.endereco;

      dtocheckout.setPagamentoCartao(dadosCartao, descricaoCartao,   taxaCartao, taxaMinimaCartao)

      console.log(JSON.stringify(dtocheckout))

      let transacao: any = await servie.criePagamento(dtocheckout).catch(async (errCheckout: any) => {
        throw   Error(errCheckout);
      });

      if(transacao){


        await pagamento.atualizeRetornoTunaPay(transacao,
          dtocheckout.getValorTaxaSplitMeucadapio(taxaCartao, taxaMinimaCartao), dadosCartao);

        const novoStatus = StatusPaymentDeParaTunaPayWebhook.get(transacao.status)

        await fezCheckoutOnline(pedido, pagamento, empresa, novoStatus, resposta);

        resolve(pagamento);
      }
    } catch(erroCheckout: any) {
      await registreErroCheckout(pedido, erroCheckout, resposta, 'tunapay');
      resolve(resposta)
    }
  })
}

function facaCheckoutPagarme(empresa: any, dados: any, pedido: PedidoGenerico, resposta: any){
  return new Promise(  async (resolve) => {
    try{
      const pagamento = pedido.obtenhaPagamentoOnline() as PagamentoPedido;

      let configMeioPagamento: any = pagamento.formaDePagamento.configMeioDePagamento

      let descricaoCartao = configMeioPagamento.nomeFaturaCartao;

      if(!descricaoCartao) descricaoCartao = empresa.dominio;

      let pagamentoVindoDaTela: any = dados.pagamentos.find( (pagamentoTela: any) => pagamentoTela.dadosCartao);

      let plataforma  = pagamento.hubPagarme() ?  "meucardapio_ai" : null;

      let dtocheckoutpagarme = new DTOPedidoPagarme(pedido,  pagamentoVindoDaTela.dadosCartao, descricaoCartao,
        configMeioPagamento.antifraudeDesabilitado, plataforma);

      console.log(JSON.stringify(dtocheckoutpagarme))

      let token = pagamento.hubPagarme() ? empresa.integracaoGatewayPagamento.privateKey : configMeioPagamento.token;

      let pagarmeApiService = new PagarmeApiService(token,  pagamento.hubPagarme());

      let order: any = await   pagarmeApiService.criePedido(dtocheckoutpagarme).catch((errCheckout: any) => {
        throw   Error(errCheckout);
      });

      if(order){
        let charge = order.charges[0];
        await pagamento.atualizeRetornoPagarme(charge);
        let novoStatus = StatusPagamentoDeParaPagarme.get(order.status);
        if(!pedido.contato.codigoPagarme){
          pedido.contato.codigoPagarme = order.customer.id
          await new MapeadorDeContato().atualizeCodigoPagarme(  pedido.contato)
        }
        await fezCheckoutOnline(pedido, pagamento, empresa, novoStatus, resposta);

        resolve(pagamento);
      }

    } catch(erroCheckout) {
      await registreErroCheckout(pedido, erroCheckout, resposta, 'pagarme');
      resolve(resposta)
    }
  })
}

function facaCheckoutCartaoMercadoPago(empresa: any, dados: any, pedido: PedidoGenerico, resposta: any){
  return new Promise(  async (resolve) => {
    const pagamento = pedido.obtenhaPagamentoOnline() as PagamentoPedido;
    const configMeioDePagamento = pagamento.formaDePagamento.configMeioDePagamento;
    let pagamentoVindoDaTela = dados.pagamentos.find( (pagamentoTela: any) => pagamentoTela.dadosCartao);
    let dadosCartao = pagamentoVindoDaTela ? pagamentoVindoDaTela.dadosCartao : null

    try{
      let transacao: any = await new MercadoPagoService(configMeioDePagamento.token).criePagamentoCartao(dadosCartao, pedido as Pedido)
        .catch((errCheckout) => { console.log(errCheckout);   throw   Error(errCheckout.message || errCheckout);  });

      if(transacao){
        console.log('Foi obtida a transação no mercado pago: ' + transacao.status);
        await pagamento.atualizeMercadoPago(transacao);
        await new PedidoService().mudouStatusPagamento(pedido as Pedido, pagamento, StatusPagamentoDeParaMercadoPago.get(transacao.status),
          transacao.codigo, transacao.motivoReprovacao);
        pagamento.pedido = pedido;
        await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamento, empresa);
        resposta.statusPagseguro = pagamento.status;
        resposta.codigoTransacao = pagamento.codigoTransacao;

        if(transacao.motivoReprovacao){
          resposta.mensagemFalhaPagamento = transacao.motivoReprovacao;
          resposta.falhaNoPagamentoOnline = true;
        }

        resolve(pagamento);
      }
    } catch(erroCheckout) {
      await registreErroCheckout(pedido, erroCheckout, resposta, 'mercaodopago');
      resolve(resposta)
    }
  })
}

function facaCheckoutCielo(session: any, empresa: any, dados: any, pedido: PedidoGenerico, resposta: any ){
  return new Promise(  async (resolve) => {
    try{
      const pagamento: PagamentoPedido = pedido.obtenhaPagamentoOnline() as PagamentoPedido;

      let pagamentoVindoDaTela: any = dados.pagamentos.find( (pagamentoTela: any) => pagamentoTela.dadosCartao)

      if(!pagamentoVindoDaTela){
        console.log('Dados cartao nao informado: ')
        console.log(dados.pagamentos)
        throw Error('Dados cartao nao informado');
      }


      let dadosCartaoSensiveis: any = session[pagamentoVindoDaTela.dadosCartao.token];

      if(!dadosCartaoSensiveis){
        console.log('token não encontrado: ' +  pagamentoVindoDaTela.dadosCartao.token)
        throw Error('Informações do cartão não encontradas');
      }

      pagamentoVindoDaTela.dadosCartao.codigoSeguranca = dadosCartaoSensiveis.codigoSeguranca;
      pagamentoVindoDaTela.dadosCartao.bandeira = dadosCartaoSensiveis.bandeira;

      let configMeioPagamento: any = pagamento.formaDePagamento.configMeioDePagamento;
      let descricaoCartao = configMeioPagamento.nomeFaturaCartao;

      let dtoTransacaoCielo = new DTOTransacaoCielo(pedido as Pedido, pagamentoVindoDaTela, descricaoCartao)

      let cieloCheckoutService = new CieloEcommerceService(configMeioPagamento.merchantId, configMeioPagamento.merchantKey );

      let transacao = await   cieloCheckoutService.crieTransacao(dtoTransacaoCielo).catch((errCheckout: any) => {
         throw   Error(errCheckout);
      });

      if(transacao){
        await pagamento.atualizeRetornoCielo(transacao );
        let novoStatus = StatusPagamentoDeParaCielo.get(Number(transacao.Status));
        await fezCheckoutOnline(pedido, pagamento, empresa, novoStatus, resposta);
        resolve(pagamento);
      }
    } catch(erroCheckoutCielo) {
      await registreErroCheckout(pedido, erroCheckoutCielo, resposta, 'cielo');
      resolve(resposta)
    }
  })

}

async function registreErroCheckout(pedido: any, erroCheckout: any, resposta: any, meio: string){
  console.log('Houve um erro no pagamento do ' + meio)
  console.error(erroCheckout)
  let pagamentoPedido: PagamentoPedido = pedido.obtenhaPagamentoPrincipal() as PagamentoPedido;
  //Caso ocorra um erro no checkout, o pedido já foi salvo. É necessário exibir a tela de pedidos com a mensagem do erro
  await new PedidoService().mudouStatusPagamento(pedido as Pedido, pagamentoPedido,
    EnumStatusPagamento.Negado, null, (erroCheckout.message || erroCheckout));

  //console.log('Houve um erro no pagamento do pagseguro: ' + erroCheckout)
  resposta.falhaNoPagamentoOnline = true;
  resposta.mensagemFalhaPagamento =  pagamentoPedido.motivoReprovacao;
}


async function  fezCheckoutOnline(pedido: any, pagamento: any, empresa: any, novoStatus: any, resposta: any){
  await new PedidoService().mudouStatusPagamento(pedido as Pedido, pagamento, novoStatus);

  pagamento.pedido = pedido;
  await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamento, empresa)
  resposta.statusPagseguro = pagamento.status;
  resposta.codigoTransacao = pagamento.codigoTransacao;
}

function facaCheckoutERede(dados: any, pedido: PedidoGenerico, resposta: any, empresa: any ){
  return new Promise( async (resolve, reject) => {
    console.log('Iniciando o checkout novo erede')

    const pagamento = pedido.obtenhaPagamentoOnline() as PagamentoPedido;
    const configMeioDePagamento = pagamento.formaDePagamento.configMeioDePagamento;


    let pagamentoVindoDaTela = dados.pagamentos.find( (pagamentoTela: any) => pagamentoTela.dadosCartao)
    if(!pagamentoVindoDaTela)
      return reject('Não foram enviados dados da forma de pagamento ' + EnumMeioDePagamento.ERede)

    let dadosCartao: any =  pagamentoVindoDaTela.dadosCartao;

    if(dadosCartao && dadosCartao.deviceInfo){
      dadosCartao.deviceInfo.userAgent = dados.userAgent;
      dadosCartao.deviceInfo.ip = dados.ip;
    }

    let dtocheckout: DTOTransacaoERede  = new DTOTransacaoERede(pedido);

    pagamentoVindoDaTela.dadosCartao.key = pagamento.formaDePagamento.id;

    let enderecoCobranca: any =  dadosCartao.endereco || pedido.endereco;

    enderecoCobranca.telefone = pedido.contato.telefone;
    let descricaoCartao = configMeioDePagamento.nomeFaturaCartao || 'Meucardapio';
    dtocheckout.setPagamentoCartao(dadosCartao,  enderecoCobranca, pedido.id, descricaoCartao);

    if(pagamento.jaTentouPagar()){
      dtocheckout.gereCodigoAleatorioPedido();
      await pagamento.atualizaNovaTentativa(dtocheckout.reference);
    }


    let serviceERede: ERedeItauApi = new ERedeItauApi(configMeioDePagamento);

    if(dtocheckout.gerarCriptograma){
      let erroProcessoTokenizar: any;
      const tokenizationId: string = dtocheckout.cardNumber;
      let respostaToken: any = await serviceERede.obtenhaTokenCartao(tokenizationId).catch(async (errObtertoken) => {
        erroProcessoTokenizar = errObtertoken;
      })

      if(erroProcessoTokenizar || respostaToken.erro){
        if(respostaToken && respostaToken.erro)
          erroProcessoTokenizar = respostaToken.erro

        await registreErroCheckout(pedido, erroProcessoTokenizar, resposta, 'erede');
        return resolve(resposta);
      }

      if(respostaToken.pendente){
        //tokenização ainda nao terminou, vamos esperar retorno  do processo
        await pagamento.atualizeRetornoRede({tokenizationId: tokenizationId})

        let umDia = 86400;
        CacheService.insiraJson(`tokid:${tokenizationId}`, { dadosCartao: dadosCartao,  enderecoCobranca: enderecoCobranca}, umDia)

        resposta.aguardandoTokenizar = true;
        resposta.statusPagseguro = pagamento.status;

        return resolve(resposta);
      }

      let criptograma: any = await serviceERede.gereCriptogramaToken(tokenizationId).catch( async (errGerarCriptograma) => {
        erroProcessoTokenizar = errGerarCriptograma;
      })

      if(criptograma) dtocheckout.setToken(respostaToken.code, criptograma)
    }

    let transacao: any = await serviceERede.crieTransacao(dtocheckout).catch(async (erroCheckout: any) => {
      await registreErroCheckout(pedido, erroCheckout, resposta, 'erede');
      resolve(resposta);
    });

    if(transacao){
      console.log(transacao)
      //salvar codigo pedido na 1x para futuras consultas/sincronização por codigo transação criado e nao depender de notificação erede
      if(!pagamento.codigo) pagamento.codigo = dtocheckout.reference;
      await  pagamento.atualizeRetornoRede(transacao)
      let statusTransacao = transacao.authorization ? transacao.authorization.status :  EnumStatusPagamentoRede.Pendente;
      let novoStatus = StatusPagamentoDeParaRede.get(statusTransacao);
      await fezCheckoutOnline(pedido, pagamento, empresa, novoStatus, resposta);
      resolve(resposta);
    }
  })

}

function facaCheckoutPagBankConnect(dados: any, pedido: PedidoGenerico, resposta: any, empresa: any){
  return new Promise( async (resolve, reject) => {
    console.log('Iniciando o checkout novo pagseguro')
    const pagamento = pedido.obtenhaPagamentoOnline() as PagamentoPedido;
    const configMeioDePagamento = pagamento.formaDePagamento.configMeioDePagamento;

    let pagamentoVindoDaTela = dados.pagamentos.find( (pagamentoTela: any) => pagamentoTela.dadosCartao)
    if(!pagamentoVindoDaTela)
      return reject('Não foram enviados dados da forma de pagamento ' + EnumMeioDePagamento.PagBankConnect)

    if(!empresa.integracaoGatewayPagamento)
      return reject('Integração com Pagseguro nao configurada')

    let orderApi: PagBankConnectAPI  = new PagBankConnectAPI(empresa.integracaoGatewayPagamento);

    let dadosCartao = pagamentoVindoDaTela.dadosCartao
    let descricaoCartao = configMeioDePagamento.nomeFaturaCartao || 'Meucardapio';

    let dadosDoPagamento: any = new DTOOrderPagseguro(pedido as Pedido)

    dadosDoPagamento.setPagamentoCartao(pedido, pagamento, dadosCartao, descricaoCartao)

    let order: any = await orderApi.crieOrder(dadosDoPagamento).catch( async (erroCheckout: any) => {
      await registreErroCheckout(pedido, erroCheckout, resposta, 'pagseguro');
      resolve(resposta);
    });

    if(order){
      console.log(order);
      let charge = order.charges[0];
      if(pagamentoVindoDaTela.dadosCartao.codigoAutenticacao)
         charge.codigoAutenticacao = pagamentoVindoDaTela.dadosCartao.codigoAutenticacao

      await  pagamento.atualizeRetornoPagseguro(charge)
      let novoStatus = StatusChargeDeParaPagseguro.get(charge.status);
      await fezCheckoutOnline(pedido, pagamento, empresa, novoStatus, resposta);
      resolve(resposta);
    }
  })
}


function facaCheckoutPagSeguro(dados: any, pedido: PedidoGenerico, resposta: any, empresa: any) {
  return new Promise( async (resolve, reject) => {
    console.log('Iniciando o checkout no pagseguro')
    const pagamento = pedido.obtenhaPagamentoOnline() as PagamentoPedido;
    const configMeioDePagamento = pagamento.formaDePagamento.configMeioDePagamento;

    let pagamentoVindoDaTela = dados.pagamentos.find( (pagamentoTela: any) => pagamentoTela.dadosCartao)
    if(!pagamentoVindoDaTela)
      return reject('Não foram enviados dados da forma de pagamento ' + EnumMeioDePagamento.Pagseguro)

    let   modoTeste = !Ambiente.Instance.producao;

    const transacao = new Transacao(configMeioDePagamento);

    let dadosDoPagamento: any = new DTOTransacaoPagSeguro(pedido as Pedido, pagamentoVindoDaTela, modoTeste)

    dadosDoPagamento.receiverEmail = transacao.email;

    console.log( 'Preparando para invocar o pagseguro');
    console.log(dadosDoPagamento);

    transacao.crie(dadosDoPagamento, (respostaPagseguro: any) => {
      console.log('respostaPagseguro.erro: ' + respostaPagseguro.erro +
        ' - respostaPagseguro.transaction: ' + respostaPagseguro.transaction)

      if(respostaPagseguro.erro)
        return reject(respostaPagseguro.erro)

      console.log('Código da transação gerado - ' + respostaPagseguro.transaction.code[0] +
        ' com status ' + respostaPagseguro.transaction.status[0] + ' para o pedido #' + pedido.codigo)

      pagamento.atualizeCodigoPagseguro({
        codigo: respostaPagseguro.transaction.code[0],
        status: respostaPagseguro.transaction.status[0]
      }).then(() => {
        console.log('Foi obtida a transação no pagseguro: ' + pagamento.codigoTransacao)

        resposta.statusPagseguro = pagamento.status
        resposta.codigoTransacao = pagamento.codigoTransacao

        resolve(pagamento);
      }).catch((erroCheckout: any) => {
        console.log('Houve um erro no pagamento do pagseguro: ' + erroCheckout)
        //Caso ocorra um erro no checkout, o pedido já foi salvo. É necessário exibir a tela de pedidos com a mensagem do erro
        new PedidoService().mudouStatusPagamento(pedido as Pedido, pedido.obtenhaPagamentoPrincipal() as PagamentoPedido,
          EnumStatusPagamento.Negado,
          null, erroCheckout).then( () => {

          //console.log('Houve um erro no pagamento do pagseguro: ' + erroCheckout)
          resposta.falhaNoPagamentoOnline = true;
          resposta.mensagemFalhaPagamento = erroCheckout;

          resolve(resposta)
        })
      });
    });
  });
}

router.post('/cartao/novoPix', async (req: any, res: any) => {
  let dados = req.body;
  let pedido: Pedido;
  dados.empresa = req.empresa;
  dados.operador = req.user;
  pedido = await Pedido.get({codigo: dados.codigo})
  pedido.empresa = req.empresa;

  pedido.setTamanhosProduto();

  let pagamento = pedido.obtenhaPagamentoPrincipal();

  if(pagamento.foiPorPix()){
    let resposta: any = { id: pedido.id, codigo: pedido.codigo, guid: pedido.guid};

    await facaCheckoutPix(req.empresa, pedido, resposta);
    res.json(Resposta.sucesso(resposta));
  } else {
    res.json(Resposta.erro('O pedido informado não possui pagamento pix'))
  }
})

router.post('/:guid/cartao/novoPagamento', async (req: any, res: any) => {
  await executeNovaTentativaPagamento(req, res)
})
router.post('/cartao/novoPagamento', async (req: any, res: any) => {
  await executeNovaTentativaPagamento(req, res)
})

async function executeNovaTentativaPagamento(req: any, res: any){
  let dados = req.body;
  let pedido: Pedido;
  dados.empresa = req.empresa;
  dados.operador = req.user;
  pedido = await Pedido.get({codigo: dados.codigo})
  pedido.empresa = req.empresa;

  const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
  const userAgent  = req.headers['user-agent'];

  dados.ip = ip;
  dados.userAgent = userAgent;

  if(pedido) {
    let resposta: any = { id: pedido.id, codigo: pedido.codigo, guid: pedido.guid};

    if(pedido.pagarOnline() ) {
      resposta.meioDePagamento = pedido.gatewayPagamentoOnline();

      if(resposta.meioDePagamento === EnumMeioDePagamento.TunaPay){
        await facaCheckoutCartaoTuna(req.empresa, dados, pedido, resposta);
        res.json(Resposta.sucesso(resposta));
      } else if( resposta.meioDePagamento === EnumMeioDePagamento.Pagarme || resposta.meioDePagamento === EnumMeioDePagamento.PagarmeHub) {
        await facaCheckoutPagarme(req.empresa, dados, pedido, resposta);
        res.json(Resposta.sucesso(resposta));
      } else if( resposta.meioDePagamento === EnumMeioDePagamento.CieloCheckout ) {
        await facaCheckoutCielo(req.session, req.empresa, dados, pedido, resposta);
        res.json(Resposta.sucesso(resposta))
      } else  if( resposta.meioDePagamento === EnumMeioDePagamento.MercadoPago ) {
        await facaCheckoutCartaoMercadoPago(req.empresa, dados, pedido, resposta);
        res.json(Resposta.sucesso(resposta));
      } else if( resposta.meioDePagamento === EnumMeioDePagamento.PagBankConnect ) {
        await facaCheckoutPagBankConnect(dados, pedido, resposta, pedido.empresa);
        res.json(Resposta.sucesso(resposta));
      } else if(resposta.meioDePagamento === EnumMeioDePagamento.ERede){
        await facaCheckoutERede(dados, pedido, resposta, pedido.empresa );
        res.json(Resposta.sucesso(resposta));
      } else if( resposta.meioDePagamento === EnumMeioDePagamento.Pagseguro ) {
        facaCheckoutPagSeguro(dados, pedido, resposta, pedido.empresa).then((pagamento: PagamentoPedido) => {
          res.json(Resposta.sucesso(resposta));
        }).catch((erro) => {
          //Caso ocorra um erro no checkout, o pedido já foi salvo. É necessário exibir a tela de pedidos com a mensagem do erro
          new PedidoService().mudouStatusPagamento(pedido, pedido.obtenhaPagamentoPrincipal() as PagamentoPedido,
            EnumStatusPagamento.Negado, null,
            erro).then(() => {
            resposta.falhaNoPagamentoOnline = true;
            resposta.mensagemFalhaPagamento = erro;
            res.json(Resposta.sucesso(resposta))
          })
        });
      } else  {
        res.json(Resposta.erro('O pedido informado não possui pagamento online pendente do tipo: ' +  resposta.meioDePagamento ))
      }
    }
    else
      res.json(Resposta.erro('O pedido informado não possui pagamento online pendente'))
  }
  else
    res.json(Resposta.erro('Não foi possível criar/obter o pedido com os dados enviados'))
}

router.post('/:guid', async (req: any, res: any) => {
  let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

  if(!pedido){
    await crieNovoPedido(req, res);
  } else {
    console.log('Pedido já foi criado: ' + pedido.guid);
    let resposta: any = await obtenhaRespostaPedido(pedido, req.body, req);

    res.json(Resposta.sucesso(resposta))
  }
})

async function obtenhaRespostaPedido(pedido: PedidoGenerico, dados: any, req: any) {
  const variaveisDeRequest = new VariaveisDeRequest();
  const link = variaveisDeRequest.obtenhaUrlRaiz(Ambiente.Instance.contexto().empresa);
  let urlAbrir = String(`${link}/whatsapp/pedido/mensagem/${pedido.guid}`);
  if (dados.op) {
    urlAbrir += '?op=' + dados.op;
  }

  let resposta: any = {id: pedido.id, codigo: pedido.codigo, urlAbrir: urlAbrir, guid: pedido.guid};

  if (pedido.feitoNaLoja() && !pedido.contato.ehConsumidorNaoIndentificado()) {
    if (!req.session.contatoLogado || dados.contato.completarCadastro) {
      resposta.fezLoginGuest = true;
      req.session.contatoLogado = await pedido.contato.obtenhaDTOContatoLogado(req.empresa, true);
    }
  }

  if (Modulo.somenteCardapio(pedido.empresa.modulos))
    resposta.abrirUrl = true;

  return resposta;
}



async function processePagamentoOnline(pedido: PedidoGenerico, dados: any, empresa: any, session: any, resposta: any){
  if( pedido.pagarViaPix() ) {
    await facaCheckoutPix(empresa, pedido, resposta);
  } else if(pedido.pagarOnline() ) {
    resposta.meioDePagamento = pedido.obtenhaMeioDePagamento();

    if(resposta.meioDePagamento === EnumMeioDePagamento.TunaPay){
      await facaCheckoutCartaoTuna(empresa, dados, pedido, resposta);
    } else if( resposta.meioDePagamento === EnumMeioDePagamento.Pagarme || resposta.meioDePagamento === EnumMeioDePagamento.PagarmeHub ) {
      await facaCheckoutPagarme(empresa, dados, pedido, resposta);
    } else if( resposta.meioDePagamento === EnumMeioDePagamento.CieloCheckout ) {
      await facaCheckoutCielo(session, empresa, dados, pedido, resposta);
    } else if( resposta.meioDePagamento === EnumMeioDePagamento.PagBankConnect ) {
      //chamar o pagseguro connect
      await facaCheckoutPagBankConnect(dados, pedido, resposta, pedido.empresa);
    }else  if(resposta.meioDePagamento === EnumMeioDePagamento.ERede){
      await facaCheckoutERede(dados, pedido, resposta, pedido.empresa);
    } else if( resposta.meioDePagamento === EnumMeioDePagamento.Pagseguro ) {
      //chamar o pagseguro
      await facaCheckoutPagSeguro(dados, pedido, resposta, pedido.empresa);
    } else if(resposta.meioDePagamento === EnumMeioDePagamento.MercadoPago) {
      await  facaCheckoutCartaoMercadoPago(empresa, dados, pedido, resposta);

    } else if(resposta.meioDePagamento === EnumMeioDePagamento.CieloSuperlink) {
      resposta.gerarLinkPagamento = true;
    }
  }
}

async function  crieNovoPedido(req: any, res: any){
  let dados = req.body;
  let pedido: any;
  let erro ;
  let mapeadorDeAtributo = new MapeadorDeAdicionalDeProduto()
  let adicionaisPedido = await mapeadorDeAtributo.listeAsync({ catalogo: req.empresa.catalogo, objeto: req.empresa.catalogo, entidade: 'pedido'})

  dados.definicaoAdicionais = adicionaisPedido

  let usuario = req.user, contatoLogado =  req.session.contatoLogado;

  if( dados.op ) {
    usuario = await new MapeadorDeUsuario().selecioneSync({id: dados.op});

    await new VariaveisDeRequest().salveOperador(usuario);
  }

  dados.operador = usuario;

  if(!dados.codigo){
    if(dados.origem === EnumOrigemPedido.Multiloja ){ //
      pedido = await new MultipedidoService().salveMultipedido(dados, req.empresa).catch(e => {
        console.log(e)
        erro = e.message ? e.message : e;
      })
    } else {
      pedido = await  new PedidoService().salvePedido(dados, req.empresa).catch( e => {
        console.log(e)
        erro = e.message ? e.message : e;
      })
    }
  } else {
    pedido = await Pedido.get({codigo: dados.codigo})
  }
  if(erro)
    return res.json(Resposta.erro(erro))

  if(pedido && pedido.id) {
    let resposta: any = await obtenhaRespostaPedido(pedido, dados, req);

    if(contatoLogado && !contatoLogado.cpf && pedido.contato && pedido.contato.cpf  )
      req.session.contatoLogado.cpf = pedido.contato.cpf

    // Removido envio do evento de compra para o Facebook Pixel - agora é feito pelo front-end

    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const userAgent  = req.headers['user-agent'];

    dados.ip = ip;
    dados.userAgent = userAgent;

    await processePagamentoOnline(pedido, dados, req.empresa, req.session, resposta );

    res.json(Resposta.sucesso(resposta))
  }  else
    res.json(Resposta.erro('Não foi possível criar/obter o pedido com os dados enviados'))
}

router.post('/', async (req: any, res: any) => {
  await crieNovoPedido(req, res);
})

router.post('/:codigo/pagamento/online', async (req: any, res: any) => {
  let pedido: any = await Pedido.get({ codigo: req.params.codigo });

  new ApiSuperLinkService(req.empresa).crieLinkPagamento(pedido).then( (dadosPagamento: any) => {

    req.session.idEmpresa = pedido.empresa.id;
    req.session.codigoPedido = pedido.guid;
    console.log(console.log('Link gerado: ' + dadosPagamento.link))
    res.json(Resposta.sucesso(dadosPagamento));
  }).catch((erro: string) => {
    res.json(Resposta.erro(erro))
  })

})

router.put('/:id/pago', async(req: any, res: any) => {
  let pago = req.body.pago;
  let formaDePagamento = req.body.formaDePagamento;
  let idPedido = req.params.id;
  let empresa = req.empresa;

  let pedido: Pedido = await Pedido.get(Number(idPedido));

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  console.log(`Marcar pedido como pago ${idPedido} - ${empresa.nome}`);

  let erro: any = await new PedidoService().usuarioAlterouFoiPago(pedido, empresa, pago, formaDePagamento);

  if(!erro){
    let redirectAdicionarPontos =  false;

    if(empresa.temFidelidade() && !pedido.pontosGanhos && !pedido.deBalcao())
      redirectAdicionarPontos = await new MapeadorDePlano().existeSync({ ativo: true})

    res.json(Resposta.sucesso({ redirectAdicionarPontos: redirectAdicionarPontos}))
  }

  else
    res.json(Resposta.erro(erro))
})

router.put('/:id/visualizado', async(req: any, res: any) => {
  let idPedido = req.params.id;

  let pedido: Pedido = await Pedido.get(Number(idPedido));

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  pedido.visualizado = true;

  await new MapeadorDePedido().atualizeVisualizado(pedido);

  res.json(Resposta.sucesso())
})

router.put('/:id/finalize-mesa', async(req: any, res: any) => {
  const dadosPagamento = req.body;
  const idPedido =  req.params.id;
  const empresa = req.empresa

  let pedido: Pedido = await Pedido.get(Number(idPedido));

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  let erro: any = await new PedidoService().finalizePedidoMesa(pedido, req.empresa, dadosPagamento.formaDePagamento, req.user);

  if(!erro){
    let resposta  = { redirectAdicionarPontos:  empresa.temFidelidade() && !pedido.pontosGanhos };

    if(empresa.integracaoPDVParceiroAtiva() && !pedido.referenciaExterna){
      let service: IServiceIntegracaoExternaERP = empresa.integracaoDelivery.obtenhaService();

      service.adicionePedido(pedido, empresa).then(   (uuid: string) => {
        pedido.referenciaExterna = uuid;
        new MapeadorDePedido().atualizeReferenciaExterna(pedido).then( () => {
          res.json(Resposta.sucesso(resposta));
        })
      } ).catch( (erroNotificar: any) => {
        pedido.erroExterno = erroNotificar
        new MapeadorDePedido().atualizeErroExterno(pedido).then( () => {
          res.json(Resposta.sucesso({ erroNotificar: erroNotificar }));
        })
      })

    } else{
      res.json(Resposta.sucesso(resposta));
    }
  } else {
    res.json(Resposta.erro(erro));
  }
})

router.put('/:guid/entregador', RotaGuard.alterarPedido, async(req: any, res: any) => {
  let novoEntregador = req.body.entregador;
  let guid =  req.params.guid;
  const empresa = req.empresa

  console.log(`Mudar o entregador pedido ${guid} - ${empresa.nome}`);

  if(!novoEntregador) return res.json(Resposta.erro('Entregador não informado!'))

  let pedido: Pedido = await Pedido.get({guid: guid});

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  if(pedido.entregador && pedido.entregador.id === novoEntregador.id)
    return res.json(Resposta.sucesso())

  let resposta: any = await new PedidoService().usuarioAlterouEntregador(pedido, req.empresa, novoEntregador, req.user);

  if(!resposta.erro){
    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

    notificacaoService.envieNotificacaoPedidoAssociadoAoEntregador(pedido).then(() => {
      res.json(Resposta.sucesso(resposta));
    }).catch((erro: any) => {
      console.log("***Ocorreu um erro ao enviar mensagem ao entregador do pedido " + pedido.id)
      console.log(erro)
      res.json(Resposta.sucesso(resposta));
    })


  } else {
    res.json(Resposta.erro(resposta.erro));
  }
})

router.put('/:guid/status', RotaGuard.alterarPedido, async(req: any, res: any) => {
  let novoStatus = req.body.status;
  let guid =  req.params.guid;
  let notificar: boolean = req.body.notificar != null;
  const empresa = req.empresa

  console.log(`Mudar o status pedido ${guid} - ${empresa.nome}`);

  if(!novoStatus && novoStatus !== 0) return res.json(Resposta.erro('Status não informado!'))

  let pedido: Pedido = await Pedido.get({guid: guid});

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'));

  let resposta: any = await new PedidoService().operadorAlterouStatus(pedido, req.empresa, novoStatus, notificar, false, req.user);

  if(!resposta.erro){
    res.json(Resposta.sucesso(resposta));
  } else {
    res.json(Resposta.erro(resposta.erro));
  }
})

router.put('/:guid/rejeitar', async(req: any, res: any) => {
  let guid =  req.params.guid;
  let motivo: any = req.body.motivo;
  let pedido: Pedido = await Pedido.get({guid: guid});
  const empresa = req.empresa

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  if(pedido.doIfood()){
    let service = new IFoodService(empresa.obtenhaIntegracaoIfood(pedido.idLojaExterna));
    let erroCancelarIfood: string;

    let orderEvent: any =  await service.notifiqueCancelamentoPedido(pedido, motivo).catch((erroIfood: any) => {
      erroCancelarIfood = erroIfood
    });

    if(orderEvent && orderEvent.id){
      return res.json(Resposta.sucesso({ aguardandoPdv: true, idEvent: orderEvent.eventId  }))
    } else {
      return res.json(Resposta.erro(erroCancelarIfood));
    }
  }

  let erro: any = await new PedidoService().rejeitePedido(pedido, empresa, req.user);

  if(!erro){
    let dto = new  DTOPedido(pedido, req.empresa.camposAdicionais);

    res.json(Resposta.sucesso({status: dto.status, statusOrdem: dto.statusOrdem}));
  } else{
    res.json(Resposta.erro(erro));
  }
})

router.put('/:guid/aceitar', async(req: any, res: any) => {
  let guid =  req.params.guid;
  let pedido: Pedido = await Pedido.get({guid: guid});
  const empresa = req.empresa

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  let erro: any = await new PedidoService().aceitePedido(pedido, empresa, req.user);

  if(!erro){
    if(empresa.integracaoPDVParceiroAtiva() &&  !pedido.mesa){
      await   new IntegradorUtils().notifiqueNovoPedido(empresa, pedido);
      if( pedido.referenciaExterna){
        res.json(Resposta.sucesso({ referenciaExterna: pedido.referenciaExterna}));
      } else {
        res.json(Resposta.sucesso({ erroNotificar:  pedido.erroExterno  }));
      }
    } else {
      res.json(Resposta.sucesso({}));
    }
  } else {
    res.json(Resposta.erro(erro));
  }
});


async function  validePedidoEmDisputa(req: any, res: any, next: any){
  let pedido: Pedido = await Pedido.get({guid: req.params.guid});

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  if(!pedido.disputa)
    return res.json(Resposta.erro('Nenhum disputa registrada para o pedido ' + pedido.codigo))

  if(pedido.disputa.foiFinalizada())
    return res.json(Resposta.erro('Prazo para responder foi até: ' + moment(pedido.disputa.dataExpiracao).format('DD/MMM  [ás] HH:mm')))

  req.pedido = pedido;

  next();
}

router.put('/:guid/disputa/proposta', validePedidoEmDisputa, async(req: any, res: any) => {
  const pedido =  req.pedido,
    acao =  req.body.acao, valor =  req.body.valor;

  if(pedido.doIfood()){
    let integracaoIfood = req.empresa.obtenhaIntegracaoIfood(pedido.idLojaExterna);

    if(!integracaoIfood) return    res.json(Resposta.erro('Nenhum integração ativa'));

    let service = new IFoodService(integracaoIfood);
    let erro: any;

    erro = await service.facaProposta(pedido.disputa.guid, acao, valor);

    if(!erro){
      await pedido.disputa.atualizeAguardandoPelaPorposta();

      res.json(Resposta.sucesso({}));
    } else {
      res.json(Resposta.erro(erro));
    }
  }

})

router.put('/:guid/disputa/aceitar', validePedidoEmDisputa, async (req: any, res: any) => {
  let pedido: Pedido = req.pedido;

  if(pedido.doIfood()){
    let integracaoIfood = req.empresa.obtenhaIntegracaoIfood(pedido.idLojaExterna);

    if(!integracaoIfood) return    res.json(Resposta.erro('Nenhum integração ativa'));

    let service = new IFoodService(integracaoIfood);
    let erro: any;

    if(!pedido.disputa.legado){
      erro = await service.aceiteDisputa(pedido.disputa.guid)
    } else {
      erro = await service.aceiteCancelamento(pedido.referenciaExterna)
    }

    if(!erro){
      await pedido.disputa.atualizeFinalizada(EnumResultadoDisputa.ACEITOU);

      res.json(Resposta.sucesso({}));
    } else {
      res.json(Resposta.erro(erro));
    }
  }
})

router.put('/:guid/disputa/rejeitar', validePedidoEmDisputa, async(req: any, res: any) => {
  let pedido: Pedido = req.pedido;
  let motivo = req.body.motivo;

  if(pedido.doIfood()){
    let integracaoIfood = req.empresa.obtenhaIntegracaoIfood(pedido.idLojaExterna);

    if(!integracaoIfood) return    res.json(Resposta.erro('Nenhum integração ativa'));

    let service = new IFoodService(integracaoIfood);

    let erro: any;

    if(!pedido.disputa.legado){
      erro = await service.rejeiteDisputa(pedido.disputa.guid, motivo );
    } else {
      erro = await service.rejeiteCancelamento(pedido.referenciaExterna );
    }

    if(!erro){
      await pedido.disputa.atualizeFinalizada(EnumResultadoDisputa.REJEITOU, motivo);
      res.json(Resposta.sucesso({ }));
    } else {
      res.json(Resposta.erro(erro));
    }
  }
})

router.post('/integracao/notifique', async(req: any, res: any) => {
  const  idPedido = req.body.id;
  const  empresa = req.empresa;

  if(!empresa.integracaoPDVParceiroAtiva())
    return res.json(Resposta.erro('Nenhum integração ativa '));

  let pedido: PedidoGenerico = await Pedido.get(Number(idPedido));

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'));

  if((pedido as Pedido).multipedido)
    return res.json(Resposta.erroMultipedido);

  await new IntegradorUtils().notifiqueNovoPedido(empresa, pedido);

  if(pedido.referenciaExterna){
    res.json(Resposta.sucesso( pedido.referenciaExterna));
  } else {
    res.json(Resposta.erro(pedido.erroExterno));
  }
});

router.get('/impressao/assine', (req: any, res) => {
  let toSign = req.query.request;

  let caminho = path.join(__dirname, '../../server/key.pem'  )

  fs.readFile(caminho, 'utf-8', (err: ErrnoException, privateKey: string) => {
    let sign = crypto.createSign('SHA512');

    sign.update(toSign);
    let signature = sign.sign({ key: privateKey }, 'base64');

    res.set('Content-Type', 'text/plain');
    res.send(signature);
  });
})

router.get('/:codigo/comprovante', async(req: any, res) => {
  let pedido: any = await Pedido.get({ codigo: req.params.codigo });

  if(pedido){
    let resumido = req.query.resumido === 'true'

    let dtoPedido: DTOPedido = new DTOPedido(pedido, req.empresa)

    res.render('comprovante-pedido.ejs', {pedido: dtoPedido, modoComanda: false, resumido: resumido, formatador: FormatadorUtils});
  } else {
    res.end(String(`Codigo pedido inválido: ${req.params.codigo}`), 'utf8')
  }
});

router.get('/:codigo/comprovante/a4', async(req: any, res) => {
  let pedido: any = await Pedido.get({ codigo: req.params.codigo });

  if(pedido){
    let dtoPedido = new DTOPedido(pedido, req.empresa);

    // Inicializar Puppeteer
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();

    // Renderizar o EJS como HTML
    const html = await ejs.renderFile(
      path.join(__dirname, '../views/comprovante-pedido-a4.ejs'),
      { pedido: dtoPedido, formatador: FormatadorUtils }
    );

    // Carregar o HTML na página do Puppeteer
    await page.setContent(html, { waitUntil: 'networkidle0' });

    // Calcular o número total de páginas após o conteúdo ser carregado
    const totalPages = await page.evaluate(() => {
      return Math.ceil(document.body.scrollHeight / window.innerHeight);
    });

    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '10mm',
        bottom: '10mm', // Aumenta o espaço para o rodapé
        left: '10mm',
        right: '10mm',
      },
    });

    await browser.close();

    // Configurar o cabeçalho para PDF e enviar como resposta
    res.contentType('application/pdf');
    res.send(pdf);

  } else {
    res.end(String(`Codigo pedido inválido: ${req.params.codigo}`), 'utf8')
  }
});

router.get('/comanda/:id/comprovante', async(req: any, res) => {
  let comandaCompleta: any = await new MapeadorDeComanda().obtenhaComanda({ id: req.params.id}, true );

  if(comandaCompleta){
    let resumido = req.query.resumido === 'true'

    let comanda: any = new DTOComanda(comandaCompleta, req.empresa )
    comanda.codigo = comanda.id
    comanda.empresa = comanda.pedidos[0].empresa;
    comanda.cliente = comanda.pedidos[0].cliente;
    comanda.contato = comanda.cliente;

    const lista = [];

    let subValor = 0
    let total = 0

    for(let i = 0; i < comanda.pedidos.length; i++ ) {
      const pedido = comanda.pedidos[i];

      if(!pedido.cancelado){
        for(let j = 0; j < pedido.itens.length; j++ ) {
          const item = pedido.itens[j];

          item.pedido = pedido;

          lista.push(item);
        }

        subValor += pedido.subvalor
        total += pedido.total
      }
    }

    comanda.itens = lista;
    comanda.horarioDescricao = comanda.pedidos[0].horarioDescricao;
    comanda.taxaEntrega = 0
    comanda.subvalor = subValor;
    comanda.total = total - comanda.desconto;


    res.render('comprovante-pedido.ejs', {pedido: comanda, modoComanda: true, resumido: resumido, formatador: FormatadorUtils});
  } else {
    res.end(String(`Codigo comanda inválido: ${req.params.codigo}`), 'utf8')
  }
});

router.get('/execute/calcularPontos', async(req: any, res) => {
  let pedidos: any = await new MapeadorDePedido().listeAsync({ calcularPontos: true});
  let empresa = req.empresa;

  console.log('Total pedidos processar: ' + pedidos.length)

  let erros: any = [], pedidosExecutados: any = [], ignorados = [];

  for(let i = 0 ; i < pedidos.length; i++){
    let pedido: any = pedidos[i];
    if(!pedido.pontosGanhos   ){
      console.log('carregando pedido: ' + pedido.id)
      await new MapeadorDePedido().carregueItensPedido(pedido, {});
      pedido.calculePontuacaoFidelidade(empresa.integracaoPedidoFidelidade)
      if(pedido.pontosGanhos > 0){
        await new MapeadorDePedido().atualizePontosGanhos(pedido);
        pedidosExecutados.push({id: pedido.id, pontos: pedido.pontosGanhos})
      } else {
          ignorados.push(pedido.id)
      }
    }
  }

  let resposta = { pedidos: pedidosExecutados, ignorads: ignorados, erros: erros}

  console.log('ignorados: ' + ignorados.length)
  console.log(ignorados)

  console.log('erros: ' + erros.length)
  console.log(erros)

  console.log('atualizados: ' + pedidosExecutados.length)
  console.log(pedidosExecutados)

  res.json(Resposta.sucesso(resposta))

});
router.get('/execute/pontosganhos', async(req: any, res) => {
  let pedidos: any = await new MapeadorDePedido().listeAsync({ pagosNaoGerouPontos: true});
  let empresa = req.empresa;

  console.log('Total pedidos processar: ' + pedidos.length)

  let erros: any = [], pedidosExecutados: any = [];
  const cartaoService = new CartaoService();

  for(let i = 0 ; i < pedidos.length; i++){
    let pedido: any = pedidos[i];
    if(pedido.pontosGanhos > 0 && pedido.pago){
      console.log('pontuando pedido: ' + pedido.id)
      let erroPontuar: any = await cartaoService.salvePontuacaoAutomatica(pedido, empresa)
      if(!erroPontuar){
        pedidosExecutados.push(pedido.id)
      } else{
        erros.push( { id: pedido.id, erro: erroPontuar})
      }
    }
  }

  let resposta = { pedidos: pedidosExecutados, erros: erros}

  res.json(Resposta.sucesso(resposta))
});

router.get('/detalhes/:guid/historico',  async (req: any, res ) => {
  let historicos = await new MapeadorDeHistoricoPedido().listeAsync({ guid : req.params.guid})

  res.json(historicos)
});

router.get('/detalhes/:guid/payload',  async (req: any, res ) => {
  try {
    let pedido: PedidoGenerico = await new MapeadorDePedido().selecioneSync({ guid : req.params.guid});

    if(!pedido) return res.json(Resposta.erro('Nenhum pedido encontrado: ' + req.params.guid));

    if(!req.empresa.integracaoDelivery)
      return res.json(Resposta.erro('Nenhuma integração ativa'));

    let service = req.empresa.integracaoDelivery.obtenhaService();

    let resposta: any = {
      sistema: req.empresa.integracaoDelivery.sistema
    }

    await pedido.carregueDeParaTamanhoSabores();

    if(!pedido.ehMultipedido()){
      let dados: any = service.obtenhaDTOPedido(pedido, req.empresa);
      resposta.payload = dados;
    } else {
      resposta.payload = [];
      let pedidos = (pedido as MultiPedido).obtenhaPedidos() ;

      pedidos.forEach((_pedido: Pedido) => {
        let dados: any = service.obtenhaDTOPedido(_pedido, req.empresa);
        resposta.payload.push(dados)
      })
    }
    res.json(resposta)
  } catch (error){
    console.log(error)
    res.json(error.message)
  }
})

export const PedidosController: Router = router;
