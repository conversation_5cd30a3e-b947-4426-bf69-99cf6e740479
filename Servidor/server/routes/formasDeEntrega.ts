import { MapeadorFormaDeEntregaEmpresa } from './../mapeadores/MapeadorFormaDeEntregaEmpresa';
import { EnumTipoDeCobranca } from './../domain/delivery/EnumTipoDeCobranca';
import { MapeadorDeEmpresa } from './../mapeadores/MapeadorDeEmpresa';
import { Alcance } from './../domain/delivery/Alcance';
import { Resposta } from './../utils/Resposta';
import { Empresa } from './../domain/Empresa';
import { Router } from "express";
import { MapeadorDeAlcance } from '../mapeadores/MapeadorDeAlcance';
import {RotaGuard} from "../lib/permissao/RotaGuard";

const router: Router = Router();

router.get("/index", (req: any, res) => {
  const empresa: Empresa = req.empresa;

  res.json(Resposta.sucesso(empresa));
});

router.get('/alcances', (req: any, res) => {
  const empresa: Empresa = req.empresa;

  const forma = empresa.obtenhaFormaReceberEmCasa();

  res.json(Resposta.sucesso(forma.alcances));
});

router.post('/',  RotaGuard.alterarLoja, async (req: any, res) => {
  const empresa: Empresa = req.empresa;
  const dados: any = req.body;

  const alcance: Alcance = new Alcance(dados.alcance, dados.taxa, empresa.obtenhaFormaReceberEmCasa(),  dados.desativado);

  const formaDeEntregaEmpresa = empresa.obtenhaFormaReceberEmCasa();

  if(!alcance.desativado){
    if( formaDeEntregaEmpresa.possuiAlcance(dados.alcance) ) {
      res.json(Resposta.erro(`Você já tem esse alcance ${dados.alcance}KM cadastrado.`));
      return;
    }
  }

  formaDeEntregaEmpresa.tipoDeCobranca = EnumTipoDeCobranca.POR_RAIO;

  const mapeadorDeFormaDeEntrega = new MapeadorFormaDeEntregaEmpresa();

  await mapeadorDeFormaDeEntrega.atualizeSync(formaDeEntregaEmpresa);
  const mapeadorDeAlcance = new MapeadorDeAlcance();

  await mapeadorDeAlcance.insiraGraph(alcance);
  await new MapeadorDeEmpresa().removaDasCaches(empresa);

  res.json(Resposta.sucesso(alcance));
});

router.put('/', async (req: any, res) => {
  const empresa: Empresa = req.empresa;
  const dados: any = req.body;

  const alcance: Alcance = new Alcance(dados.alcance, dados.taxa, empresa.obtenhaFormaReceberEmCasa(), dados.desativado);
  Object.assign(alcance, dados);

  const formaDeEntregaEmpresa = empresa.obtenhaFormaReceberEmCasa();

  if(!alcance.desativado){
    if( formaDeEntregaEmpresa.possuiAlcance(alcance) ) {
      res.json(Resposta.erro(`Você já tem esse alcance ${dados.alcance}KM cadastrado.`));
      return;
    }
  }

  const mapeadorDeAlcance = new MapeadorDeAlcance();

  await mapeadorDeAlcance.atualizeSync(alcance);
  await new MapeadorDeEmpresa().removaDasCaches(empresa);

  res.json(Resposta.sucesso(alcance));
});

router.delete('/:id', async(req: any, res: any) => {
  const id = req.params.id;
  const empresa: Empresa = req.empresa;

  const alcance = new Alcance(0, 0, empresa.obtenhaFormaReceberEmCasa());
  alcance.id = id;

  const mapeadorDeAlcance = new MapeadorDeAlcance();
  mapeadorDeAlcance.removaAsync(alcance).then( async () => {
    await new MapeadorDeEmpresa().removaDasCaches(empresa);

    res.json(Resposta.sucesso('Alcance removido com sucesso'));
  });
});

export const FormasDeEntregaController: Router = router;
