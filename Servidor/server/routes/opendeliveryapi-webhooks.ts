import {Router} from "express";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {NotificacaoDeliveryService} from "../service/NotificacaoDeliveryService";
import {NotificacaoDeliveryOpendelivery} from "../domain/integracoes/NotificacaoDeliveryOpendelivery";
const crypto = require('crypto');
const router: Router = Router();
//const rateLimit = require('express-rate-limit');

const MAX_REQUESTS_PER_SECOND = 100;

// Crie um middleware para limitar as solicitações por endereço IP
/*const limiter = rateLimit({
  windowMs: 60 * 1000, // Janela de 60 segundo
  max: MAX_REQUESTS_PER_SECOND, // Limite máximo de solicitações por segundo
  keyGenerator: (req: any) => req.ip, // Use o endereço IP como identificador
  handler: (req: any, res: any, next: any) => {
    // Personalize a resposta de erro aqui
    res.status(429).json({
      error: 'Limite de solicitações excedido',
      message: 'Por favor, tente novamente mais tarde.  ',
      retryAfter: 60, // Valor em segundos indicando quando o cliente pode tentar novamente
    });
  },
}); */


function valideHmacSignature(body: any, secret: string, receivedHash: string) {
  console.log('hash recebido: ' + receivedHash)
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(body);
  const signature = hmac.digest('hex').toLowerCase();

  if(receivedHash !== signature){
    console.log('hash calculado: ' + signature)
    console.log(body)
    return false;
  } else {
    console.log('hash ok')
    return true;
  }
}

function valideToken(req: any, res: any, next: any){
  let appId = req.headers['x-app-id'],
    merchantId = req.headers['x-app-merchantid'] ,
    receivedHash = req.headers['x-app-signature'];
  let payload =  req.rawBody, empresa  = req.empresa, erro;

  console.log(`${req.connection.remoteAddress} validar token webhooks opendelivery`)

  if(!appId) erro = 'x-app-id is required'
  else if(!merchantId) erro = 'x-app-merchantId is required'
  else if(!receivedHash) erro = 'x-app-signature is required'

  if(!payload){
    console.log(req.headers['content-type'])
    console.log(req.body);
    erro = 'body is required'
  }

  if(!erro){
    if(empresa.integracaoOpendeliveryLogistica ){
      let secretKey = empresa.integracaoOpendeliveryLogistica.clientSecret;
      if(appId !== empresa.integracaoOpendeliveryLogistica.appid)
        erro = 'app-id inválido: ' + appId;

      if(!erro){
        if( !valideHmacSignature(payload, secretKey, receivedHash))
          erro = 'Hash inválido'
      }
    } else {
      erro = 'Nenhum integração com serviço de logistica configurada'
    }
  }

  if(!erro) return next()

  console.log(String(`IP: ${req.connection.remoteAddress} empresa: ${empresa?.dominio} Erro valide token: ${erro}`))
  res.status(401).json({
    texto: erro,
    status: 401
  });
}

router.post('/*', valideToken)

router.post('/deliveryEvent', async (req: any , res: any) => {
  let payload = req.body;
  console.log('Notificou novo evento na entrega..')
  console.log(payload)

  let  orderId  = payload.orderId,
    tipo = payload.event ? payload.event.type : null,
  deliveryId = payload.deliveryId ;

  if(!orderId) return sendErrorResponse(res, 'orderId é obrigatorio')
  if(!tipo) return sendErrorResponse(res, 'event.type é obrigatorio')
  if(!deliveryId) return sendErrorResponse(res, 'deliveryId é obrigatorio')


  let pedido = await new MapeadorDePedido().selecioneSync({guid: orderId})

  if(pedido){
    let notificacao  = new NotificacaoDeliveryOpendelivery(pedido, payload)

    await notificacao.salve(true)

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoDeliveryService.executeNotificacao(notificacao, pedido)
      cbAsync();
    }, () => {});

    res.status(204).send();
  } else {
    sendErrorResponse(res, 'Pedido não encontrado: ' + orderId)
  }
});


function sendErrorResponse(res: any, err: any) {
  console.error(err);

  let erro =  err instanceof Error ? err.message : 'Unexpected error';

  if(typeof err === 'string')
    erro = err;

  if(Array.isArray(erro))
    erro = err.join(', ');

  res.status(400).json({
    title: erro,
    status: 400
  });
}

export const OpenDeliveryApiHooksAppController: Router = router;
