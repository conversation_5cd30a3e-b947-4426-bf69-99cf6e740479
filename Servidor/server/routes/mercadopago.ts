import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {Fatura} from "../domain/faturamento/Fatura";
import {MercadoPagoService} from "../service/meiospagamentos/MercadoPagoService";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {NotificacaoMeioPagamentoService} from "../service/NotificacaoMeioPagamentoService";
import {NotificacaoMercadoPago} from "../domain/faturamento/NotificacaoMercadoPago";
import {Pagamento} from "../domain/faturamento/Pagamento";
import {DTOFatura} from "../lib/dto/DTOFatura";
import {Ambiente} from "../service/Ambiente";
import {EnumMeioDePagamento} from "../domain/delivery/EnumMeioDePagamento";
import {MapeadorDeFormaDePagamento} from "../mapeadores/MapeadorDeFormaDePagamento";
import {MapeadorDePagamentoPedido} from "../mapeadores/MapeadorDePagamentoPedido";
import {StatusPagamentoDeParaMercadoPago} from "../lib/emun/EnumStatusPagamento";
import {PedidoService} from "../service/PedidoService";

const router: Router = Router();


//conta cariar usuarios de teste
//APP_USR-1b39beb7-1778-49b3-b392-d7e05d6faea7
//APP_USR-8399723740677426-122314-139acc3deba03f069884d871fcfa5b5f-505885789

let  vendedor = {
  "id": 1229708359,
  "nickname": "TESTBZPIKHNP",
  "password": "DiMjql1e8M",
  "site_status": "active",
  "email": "<EMAIL>",
  "publick key": "TEST-ebb8c6a4-c6b0-4cfe-9c08-3e3679de23df",
  "acess token": "TEST-7114854522300265-110116-616400808af47259e62915ef13772005-1229708359"
}
let compradorTeste = {
  "id": 1229713348,
  "nickname": "TESTCJH7LQ0I",
  "password": "HgPYMhuE5j",
  "site_status": "active",
  "email": "<EMAIL>"
}

//comprador teste mercado pago:
//email: <EMAIL>
//cartao: 5031 4332 1540 6351
//cv: 123   val: 11/25
//cpf: 12345678909
//https://www.mercadopago.com.br/developers/pt/docs/checkout-api/additional-content/your-integrations/test/cards

function obtenhaCredencialMP(){
  let credencial: any = {
    prod: {
      publicKey: "APP_USR-1b39beb7-1778-49b3-b392-d7e05d6faea7",
      token: "APP_USR-8399723740677426-122314-139acc3deba03f069884d871fcfa5b5f-505885789"
    },
    dev: {
      publicKey: "TEST-afaf0840-0dfd-4f1d-934c-7266bd758d2c",
      token: "TEST-8399723740677426-122314-a6290b656be8eb425f6ea90ff635870e-505885789"
    }
  }

  if(Ambiente.Instance.producao) return credencial.prod;

  return credencial.dev;
}


function ehAdmin(req: any, res: any, next: any){
  let usuario: any = req.user;

  if(!usuario)   res.json(Resposta.erro('Faça login para realizar a operação'))

  if(!usuario.admin)   res.json(Resposta.erro('Operação não permitida'))

  return next();
}


router.post('/boleto/gere/:id', async (req: any, res) => {
  let idFatura = req.params.id || req.body.fid;
  console.log('Id fatura: ' + idFatura);
  if(!idFatura)
    return res.json(Resposta.erro('Nenhum fatura informada.'));

  let fatura: Fatura = await Fatura.get(Number(idFatura))
  try{
    let pagamento = await new MercadoPagoService(obtenhaCredencialMP().token).gereBoleto(fatura);
    res.json(Resposta.sucesso(new DTOFatura(fatura)))
  } catch (e) {
    res.json(Resposta.erro(e))
  }
})

router.get('/meios',  async   (req, res) => {
  let meios = await new MercadoPagoService(obtenhaCredencialMP().token).obtenhaMeiosPagamentos();

  res.json(meios)
})

router.get('/pagamento/:id/sincronize',  async   (req: any, res) => {
  let formaDePagamentoMP = req.empresa.formasDePagamento.find(
    (item: any) => item.configMeioDePagamento && item.configMeioDePagamento.meioDePagamento === 'mercadopago')

  let empresa = req.empresa;

  if(formaDePagamentoMP){
    formaDePagamentoMP = await new MapeadorDeFormaDePagamento().selecioneSync(formaDePagamentoMP.id);

    let payment = await new MercadoPagoService(formaDePagamentoMP.configMeioDePagamento.token).obtenhaPagamento( Number(req.params.id) )

    if(payment){
      let pagamento = await new MapeadorDePagamentoPedido().selecioneSync({codigoTransacao: payment.id});

      if(pagamento){
        await pagamento.atualizeMercadoPago(payment)

        let novoStatus = StatusPagamentoDeParaMercadoPago.get(payment.status);

        if(novoStatus.toString() !== pagamento.status.toString()){
          pagamento.pedido.empresa = empresa;
          await new PedidoService().mudouStatusPagamento(pagamento.pedido, pagamento, novoStatus);
          await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamento, empresa)
        }

        res.json(pagamento);

      } else {
        res.json(Resposta.erro('Nenhum pagamento associado: ' + payment.id))
      }

    } else {
      res.json(Resposta.erro('Payment não encontrado: ' + req.params.id))
    }

  } else{
    res.json(Resposta.erro('Forma de pagametno Mercadopago não configurado'))
  }
})

router.get('/pagamento/:id',  async   (req: any, res) => {

  let token =  obtenhaCredencialMP().token

  let formaDePagamentoMP = req.empresa.formasDePagamento.find(
        (item: any) => item.configMeioDePagamento && item.configMeioDePagamento.meioDePagamento === 'mercadopago')

  if(formaDePagamentoMP){
    formaDePagamentoMP = await new MapeadorDeFormaDePagamento().selecioneSync(formaDePagamentoMP.id);

    token = formaDePagamentoMP.configMeioDePagamento.token;
  }

  let payment = await new MercadoPagoService(token).obtenhaPagamento( Number(req.params.id) )

  res.json(payment)
})

router.post('/pagamento/:codigo/cancele', ehAdmin,  async   (req, res) => {
  let idPagamento = Number(req.params.codigo);
  let operador: any = req.user;
  let pagamento = await Pagamento.get({codigo: idPagamento});

  if(!pagamento)
    return res.json(Resposta.erro('Não existe pagamento com esse codigo: ' + idPagamento))

  new MercadoPagoService(obtenhaCredencialMP()).cancelePagamento(pagamento, operador).then( ( resposta: any) => {
    res.json(Resposta.sucesso())
  }).catch( (erro: any) => {
    res.json(Resposta.erro(erro))
  })
})

router.post('/notificacao', async (req, res) => {
  let dados = req.body,
      id = req.body.id,
      horario = new Date(req.body.date_created),
      tipo = dados.type, //payment, plan, subscription, invoice
      acao = dados.action, //payment.created
      codigo = dados.data.id;

  console.log('mercadopago notificou: ')
  console.log(dados)
  let payment = await new MercadoPagoService(obtenhaCredencialMP()).obtenhaPagamento(codigo)

  if(payment){
    let notificacao: any = new NotificacaoMercadoPago(id, tipo, acao, horario, payment);
    await  notificacao.salve();
    res.json(new Resposta(true));

    ExecutorAsync.execute( async (cb: Function) => {
      await NotificacaoMeioPagamentoService.executeDoMercadoPago(notificacao);
      cb();
    }, () => {}, 100)
  } else {
    res.status(400);
    res.json(new Resposta(false));
  }
})

router.get('/pbtoken', async (req: any, res) => {
  let empresa = req.empresa;

  let formaDePagamento = empresa.formasDePagamento.find(
        (forma: any) => forma.configMeioDePagamento &&  forma.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.MercadoPago)

  if(formaDePagamento && formaDePagamento.configMeioDePagamento.publicKey){
    res.json(Resposta.sucesso(formaDePagamento.configMeioDePagamento.publicKey))
  } else {
    console.log(formaDePagamento)
    res.json(Resposta.erro('Forma de pagamento não disponivel'))
  }
})



//APP_USR-5323249369154820-061107-4b884ecd0d6057221545fc4a377fc302-1809469487

router.get('/pagamentos/golpista', async (req: any, res) => {
  //payments/search

  let pagamentos
  = await new MercadoPagoService('APP_USR-5323249369154820-061107-4b884ecd0d6057221545fc4a377fc302-1809469487').busquePagamentos();

  res.json(pagamentos.map((item: any) =>   {
    return  {id: item.id , data: item.date_created, status: item.status, tipo: item.payment_method.id, url: item.notification_url,
      description: item.description, valor: item.transaction_amount}
  } ))

})

export const MercadoPagoController: Router = router;
