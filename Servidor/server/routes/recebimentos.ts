import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import {Resposta} from "../utils/Resposta";
import {DTOFaturaCompleta} from "../lib/dto/DTOFatura";
import {Router} from "express";
import {MapeadorDeAssinatura} from "../mapeadores/MapeadorDeAssinatura";
import {DTOAssinatura, DTOAssinaturaCompleta} from "../lib/dto/DTOAssinatura";
import {Fatura} from "../domain/faturamento/Fatura";
import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";


const router: Router = Router();
function ehAdmin(req: any, res: any, next: any){
  let usuario: any = req.user;

  let mapeador = new MapeadorDeUsuario()

  if(!usuario) return  res.json(Resposta.erro('Faça login para realizar a operação'))

  mapeador.selecioneSync(usuario).then((usuarioAtualizado: any) => {
    if(!usuarioAtualizado || !usuarioAtualizado.admin)   return res.json(Resposta.erro('Operação não permitida'))

    return next();
  })
}

router.get('/assinaturas', ehAdmin, async (req: any, res: any) => {
  let dados = req.query;

  let query: any = {inicio: 0 , total: 5000 , ordemInversa: true, recorrentes: true };

  console.log(query)

  let assinaturas = await new MapeadorDeAssinatura().listeAsync(query);

  res.json(Resposta.sucesso(assinaturas.map((assinatura: any) => new DTOAssinaturaCompleta(assinatura))))

})


router.get('/faturas', ehAdmin, async (req: any, res: any) => {
  let dados = req.query;

  let query: any = {inicio: 0 , total: 10000, ordemVencimento: true};

  query.dataVencimentoInicio = dados.div;
  query.dataVencimentoFim = dados.dfv;

  console.log(query)

  let faturas = await new MapeadorDeFatura().listeAsync(query);

  res.json(Resposta.sucesso(faturas.map((fatura: any) => new DTOFaturaCompleta(fatura))))

})


router.get('/assinatura/:id/resumo', ehAdmin, async (req: any, res: any) => {
  let query = { idAssinatura: Number(req.params.id), orderByDesc: true};
  let faturas = await new MapeadorDeFatura().listeAsync(query);
  let resposta = { faturas: new Array(), total: 0, qtde: 0, media: 0 };


  faturas.forEach( (fatura: Fatura) => {
    if(fatura.estaPaga()){
      resposta.qtde++;
      resposta.total += fatura.getValor();
    }

    if(!fatura.estaCancelada() && resposta.faturas.length <= 12)
      resposta.faturas.push(new DTOFaturaCompleta(fatura) )
  })



  resposta.media = Number( (resposta.total / resposta.qtde ).toFixed(2))

  res.json(Resposta.sucesso(resposta));
})


router.get('/competencia/resumo', ehAdmin, async (req: any, res: any) => {
  let dados = req.query;


  let query: any = {inicio: 0 , total: 10000, ordemVencimento: true};

  query.dataPagamentoInicio = dados.dic;
  query.dataPagamentoFim = dados.dfc;


  console.log(query)

  let faturas = await new MapeadorDeFatura().listeAsync(query);


  let resumo = {
    qtde: faturas.length,
    total: 0,
    totalMensalidade: 0,
    totalAdesao: 0,
    totalOutros: 0,
    qtdeNovos: 0,
    totalNovos: 0
  }

  faturas.forEach( (fatura: Fatura) => {
    let totalFatura = fatura.getValor();
    let valorAdesao = fatura.obtenhaValorPagoAdesao();

    resumo.total +=  totalFatura;
    resumo.totalAdesao +=  valorAdesao
    resumo.totalMensalidade +=  totalFatura - valorAdesao;

    if(fatura.primeira){
      resumo.qtdeNovos++;
      resumo.totalNovos += totalFatura;
    }
  })

  resumo.total =  Number(resumo.total.toFixed(2))
  resumo.totalAdesao =  Number(resumo.totalAdesao.toFixed(2))
  resumo.totalMensalidade =  Number(resumo.totalMensalidade.toFixed(2))

  res.json(Resposta.sucesso(resumo))
})







export const RecebimentosController: Router = router;
