import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {Contra<PERSON>} from "../domain/faturamento/Contrato";
import {DTOContrato} from "../lib/dto/DTOContrato";
import {DTOAssinatura} from "../lib/dto/DTOAssinatura";
import {DTOFatura} from "../lib/dto/DTOFatura";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Fatura} from "../domain/faturamento/Fatura";
import {ContratoService} from "../service/ContratoService";
import {IuguService} from "../service/IuguService";
import {EnumFormaPagamentoIugu} from "../lib/emun/EnumFormaPagamentoIugu";
import {Assinatura} from "../domain/faturamento/Assinatura";
import * as moment from "moment"
import {EnumStatusFatura} from "../lib/emun/EnumStatusFatura";
import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import {MapeadorDeContrato} from "../mapeadores/MapeadorDeContrato";
import {CartaoCredito} from "../domain/faturamento/CartaoCredito";
import {EmpresaContatosPontuado} from "../domain/utils/EmpresaContatosPontuado";
import {Ambiente} from "../service/Ambiente";


const router: Router = Router();

router.get( '/', async (req: any, res) => {
  res.render('politica-privacidade.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
});

export const PoliticaDePrivacidadeController: Router = router;
