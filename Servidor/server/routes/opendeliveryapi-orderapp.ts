import {Router} from "express";

import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {DTOPedidoOpenDelivery} from "../lib/integracao/opendelivery/DTOPedidoOpenDelivery";
import {OrderEvent} from "../domain/opendelivery/OrderEvent";
import {NotificacaoPedidoOpenDelivery} from "../domain/integracoes/NotificacaoPedidoOpenDelivery";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {NotificacaoPedidoService} from "../service/NotificacaoPedidoService";
import {IntegracaoOpendelivery} from "../domain/integracoes/IntegracaoOpendelivery";
import {MapeadorDeBearerToken} from "../mapeadores/MapeadorDeBearerToken";
import * as moment from "moment";
import {EnumStatusPedidoOpenDelivery} from "../lib/integracao/opendelivery/EnumStatusPedidoOpenDelivery";
import {EnumCancellationCode} from "../lib/integracao/opendelivery/EnumCancellationCode";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";

const router: Router = Router();

async function salveNotificacaoAlteracaoPedido(pedido: any, orderId: string, status: string,  dados: any) {

  dados.orderId = orderId;
  dados.status = status;

  let notificacaoPedido = new NotificacaoPedidoOpenDelivery(pedido, dados)

  await notificacaoPedido.salve();

  dados.pedido = pedido;

  ExecutorAsync.execute(async (cbAsync: any) => {
    await NotificacaoPedidoService.executeOpenDelivery(notificacaoPedido)
    cbAsync();
  }, (erroAsync: any) => {
    console.log(erroAsync)
  }, 100);

  console.log(String(`Notificação pedido "${dados.orderId}" processada com sucesso: ${notificacaoPedido.id}`))
}

function sendErrorResponse(res: any, err: any) {
  console.error(err);

  let erro =  err instanceof Error ? err.message : 'Unexpected error';

  if(typeof err === 'string')
    erro = err;

  if(Array.isArray(erro))
    erro = err.join(', ');

  res.status(400).json({
    title: erro,
    status: 400
  });
}

async function  valideToken (req: any, res: any, next: any){
  let integracao: IntegracaoOpendelivery =  req.empresa.integracaoOpendelivery;

  if(req.headers.authorization && integracao){
    let token = req.headers.authorization.replace('Bearer', '').trim();

    let bearer =  await new MapeadorDeBearerToken().selecioneSync({token: token});

    if(bearer && bearer.cliente.id === integracao.cliente.id)
      return next();
  }

  res.status(401).json({
    texto: "Nao autorizado",
    status: 401
  });
}

router.get('/*', valideToken)
router.post('/*', valideToken)
router.put('/*', valideToken)

router.get('/merchantStatus', async (req: any, res: any) => {
  let merchantId = req.query.merchantId;

  if(merchantId){
    let empresa = req.empresa;

    if(empresa.integracaoOpendelivery){
      if(empresa.integracaoOpendelivery.merchantId === merchantId){
        res.json({
          lastRead: new Date(),
          status: "SUCCESS",  //"SUCCESS" "PROCESSING" "FAIL"
          moreInfo: ""
        })
      } else {
        sendErrorResponse(req, 'merchantId is invalid')
      }
    } else {
      sendErrorResponse(req, 'Integração com Comerciante nao configurada na loja')
    }
  } else {
    sendErrorResponse(req, 'merchantId is required')
  }
})

router.get('/versions/orderingApp', async (req: any, res: any) => {

  res.json({
    merchant: {
      version: "1.0.0"
    },
    order: {
      version: "1.1.0",
      availableEventsMethods: [  "POLLING"] //WEBHOOK
    }
  })
})

router.get('/events:polling', async (req: any, res: any) => {
  let events: any = await OrderEvent.listeNovos();

  if(events && events.length){
    res.json(events.map((item: OrderEvent) => item.toDataApi(req.empresa)))
  } else {
    res.status(204).send();
  }
})

router.put('/merchantOnboarding',  async (req: any, res: any) => {

   const payload  = req.body,
     merchantId = req.query.merchantId || req.body.merchantId ,
     merchantUrl = payload.getMerchantURL,
     ordersWebhookURL = payload.ordersWebhookURL;

  if(!merchantId) return   sendErrorResponse(req, 'merchantId is required')
  if(!merchantUrl) return sendErrorResponse(res, 'getMerchantURL is required')
  if(!merchantUrl.apiKey) return sendErrorResponse(res, 'getMerchantURL.apiKey is required')
  if(!merchantUrl.baseURL) return sendErrorResponse(res, 'getMerchantURL.baseURL is required')

  let empresa = req.empresa;

  if(empresa.integracaoOpendelivery){
    if(empresa.integracaoOpendelivery.merchantId === merchantId){

      if(merchantUrl.baseURL !== empresa.integracaoOpendelivery.merchantBaseUrl ||
        merchantUrl.apiKey !==  empresa.integracaoOpendelivery.merchantApiKey){

        empresa.integracaoOpendelivery.merchantBaseUrl = merchantUrl.baseURL;
        empresa.integracaoOpendelivery.merchantApiKey = merchantUrl.apiKey;

        empresa.integracaoOpendelivery.obtenhaService().obtenhaMerchant().then(async (merchant: any) => {
          if(merchant && merchant.id){
            empresa.integracaoOpendelivery.setMerchant(merchant)
            await  empresa.integracaoOpendelivery.atualizeMerchant();
            await new MapeadorDeEmpresa().removaDasCaches(empresa);
            res.status(201).send();
          } else {
            sendErrorResponse(res, 'dados do comerciante não pode ser carregados')
          }

        }).catch((erro: any) => {
          sendErrorResponse(res, 'dados do comerciante não pode ser carregados: ' + erro)
        })
      } else {
        res.status(201).send();
      }
    } else {
      sendErrorResponse(res, 'merchantId inválido para esse estabelecimento')
    }
  } else {
    sendErrorResponse(res, 'Integração opendelivery não disponível')
  }
})

router.post('/events/acknowledgment', async (req: any , res: any) => {
  let events = req.body, erros: any = [];
  console.log('notificou opendelivery acknowledgment');
  console.log(req.headers['content-type'])
  console.log(req.body);

  try{
    if(!events || !events.length) return sendErrorResponse(res, 'Nenhum evento informado')

    for(let i = 0; i < events.length; i++){
      let eventDados: any = events[i];

      if(!eventDados.id) {
        erros.push(String(`Id evento não informado indice: ${i}`))
        continue;
      }

      let event: any  = await OrderEvent.get({eventId: eventDados.id})

      if(!event){
        erros.push(String(`Evento nao encontraodo: ${eventDados.id},  indice: ${i}`))
        continue;
      }

      if(event.reconhecido) continue;

      if(event.eventType === eventDados.eventType)
       await event.maqueComoReconhecido();
      else {
        erros.push(String(`Tipo do Evento nao confere: ${eventDados.eventType},  indice: ${i}`))
      }
    }

    if(erros.length){
      sendErrorResponse(res, erros)
    } else {
      res.json({})
    }
  } catch (err: any) {
    sendErrorResponse(res, err);
  }
})

router.get('/orders/:orderId', async (req: any, res: any) => {
  let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.orderId})

  if(pedido){
    try{
      res.json(new DTOPedidoOpenDelivery(pedido, req.empresa))

    } catch (err: any){
      sendErrorResponse(res, err);
    }
  }
})


router.post('/orders/:orderId/confirm', async (req: any , res: any) => {
  let orderId: string = req.params.orderId,
    payload: any = req.body;
  console.log(String(`Notificou confirmaçao pedido opendelivery => ${orderId}`));
  console.log(req.body);

  let dataCriacao = payload.createdAt,
    codigoExterno = payload.orderExternalCode,
    motivo = payload.reason,
    tempoPreparacao = payload.preparationTime;

  if(!codigoExterno) return sendErrorResponse(res, 'orderExternalCode is required')
  if(!dataCriacao) return sendErrorResponse(res, 'createdAt is required')
  if( !moment(new Date(dataCriacao)).isValid()) return sendErrorResponse(res, 'createdAt is invalid date')

  if(tempoPreparacao && !(tempoPreparacao >= 0))
    return sendErrorResponse(res, 'preparationTime is invalid')

  let pedido = await new MapeadorDePedido().selecioneSync({ guid: orderId, semItens: true});

  if(pedido){
    await salveNotificacaoAlteracaoPedido(pedido, orderId, EnumStatusPedidoOpenDelivery.confirmed, payload);

    res.json({});

  } else {
    sendErrorResponse(res, 'Nenhum pedido encontrado: ' +  orderId);
  }

});

router.post('/orders/:orderId/requestCancellation', async (req: any , res: any) => {
  let orderId: string = req.params.orderId;
  console.log('notificou opendelivery para cancelar pedido');
  console.log(req.body);

  let payload  = req.body,
    motivo = payload.reason,
    codigo = payload.code,
    mode = payload.mode,
    outOfStockItems = payload.outOfStockItems,
    invalidItems = payload.invalidItems;

  if(!motivo) return sendErrorResponse( res, 'reason is required')
  if(!codigo) return sendErrorResponse(res, 'code is required')
  if(!mode) return sendErrorResponse(res, 'mode is required')

  mode = mode.toString().toUpperCase();

  if(mode !== "AUTO" && mode !== "MANUAL" ) return sendErrorResponse(res, 'invalid mode: ' + mode)

  // Verifica se a string está dentro do enum CancellationCode
  if (!Object.values(EnumCancellationCode).includes(codigo))
    return sendErrorResponse(res, 'invalid code: ' + codigo)

  let pedido = await new MapeadorDePedido().selecioneSync({ guid: orderId, semItens: true});

  if(pedido){
    payload.orderId = orderId;

    await salveNotificacaoAlteracaoPedido(pedido, orderId,  EnumStatusPedidoOpenDelivery.requestCancellation, payload);

    res.status(204).send();
  } else {
    sendErrorResponse(res, 'Nenhum pedido encontrado: ' +  orderId);
  }
});

router.post('/orders/:orderId/acceptCancellation', async (req: any , res: any) => {
  let orderId: string = req.params.orderId;
  console.log('notificou acceptCancellation');
  console.log(req.body);

  let pedido = await new MapeadorDePedido().selecioneSync({ guid: orderId, semItens: true});

  if(pedido){
    await salveNotificacaoAlteracaoPedido(pedido, orderId, EnumStatusPedidoOpenDelivery.cancelled, req.body);

    res.status(204).send();

  } else {
    sendErrorResponse(res, 'Nenhum pedido encontrado: ' +  orderId);
  }
});

router.post('/orders/:orderId/denyCancellation', async (req: any , res: any) => {
  let orderId: string = req.params.orderId,
    payload: any = req.body;
  console.log('notificou denyCancellation');
  console.log(payload);

  let motivo = payload.reason,
    code = payload.code;    //DISH_ALREADY_DONE -> prato pronto, OUT_FOR_DELIVERY > saiu par entrega

  if(!motivo)     return sendErrorResponse(res, 'reason is required')
  if(!code)     return sendErrorResponse(res, 'code is required')

  let pedido = await new MapeadorDePedido().selecioneSync({ guid: orderId, semItens: true});

  if(pedido){
    await salveNotificacaoAlteracaoPedido(pedido, orderId, EnumStatusPedidoOpenDelivery.denyCancellation, payload);

    res.status(204).send();

  } else {
    sendErrorResponse(res, 'Nenhum pedido encontrado: ' +  orderId);
  }

});


router.post('/orders/:orderId/:status', async (req: any , res: any) => {
  let orderId: string = req.params.orderId,
    status = req.params.status,
    payload: any = req.body;
  console.log(String(`Notificou confirmaçao pedido opendelivery => ${orderId}: ${status}`));
  console.log(payload)
  let listaStatus = ['readyForPickup' , 'dispatch', 'delivered'];

  if(listaStatus.indexOf(status) === -1) return sendErrorResponse(res, 'Inavalid status')

  let pedido = await new MapeadorDePedido().selecioneSync({ guid: orderId, semItens: true});

  if(pedido){
    await salveNotificacaoAlteracaoPedido(pedido, orderId, status, payload);

    res.status(204).send();

  } else {
    sendErrorResponse(res, 'Nenhum pedido encontrado: ' +  orderId);
  }
});

router.post('/deliveryUpdate', async (req: any , res: any) => {


});


router.post('/merchantUpdated', async (req: any , res: any) => {
      sendErrorResponse(req, 'Webhook nao disponível')
});

router.post('/merchantUpdate', async (req: any, res: any) => {
  let dados: any = {};

  if(!dados){
  //This will force Ordering Application to make a new request to the GET /v1/merchant endpoint to update all the merchant information.
  }

  if(dados.merchantStatus){
    //This will force the opening or closing of the merchant within the Ordering Application, without forcing a new GET /v1/merchant call.
  }

  if(dados.entityType && dados.updatedObjects){
    //This will force the Ordering Application to update only the sent objects, without forcing a new GET /v1/merchant call.
  }

  res.json({})
})

export const OpenDeliveryApiOrderAppController: Router = router;
