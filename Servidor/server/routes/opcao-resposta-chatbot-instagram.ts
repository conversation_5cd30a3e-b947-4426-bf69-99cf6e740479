import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeOpcaoRespostaChatbotInstagram} from "../mapeadores/MapeadorDeOpcaoRespostaChatbotInstagram";
import {MapeadorDeRespostaChatbotInstagram} from "../mapeadores/MapeadorDeRespostaChatbotInstagram";
import {OpcaoRespostaChatbotInstagram} from "../domain/instagram/OpcaoRespostaChatbotInstagram";

const router: Router = Router();

// GET - Listar opções de resposta
router.get('/', async (req: any, res: any) => {
  const mapeador = new MapeadorDeOpcaoRespostaChatbotInstagram();
  
  try {
    const opcoes = await mapeador.listeAsync({
      idEmpresa: req.empresa.id,
      respostaId: req.query.respostaId,
      chaveResposta: req.query.chaveResposta,
      tipo: req.query.tipo,
      ativo: req.query.ativo
    });
    
    res.json(Resposta.sucesso(opcoes));
  } catch (error) {
    res.json(Resposta.erro('Erro ao listar opções de resposta: ' + error.message));
  }
});

// GET - Obter opção específica
router.get('/:id', async (req: any, res: any) => {
  const mapeador = new MapeadorDeOpcaoRespostaChatbotInstagram();
  
  try {
    const opcao = await mapeador.selecioneSync({
      idEmpresa: req.empresa.id,
      id: req.params.id
    });
    
    if (!opcao) {
      return res.json(Resposta.erro('Opção não encontrada'));
    }
    
    res.json(Resposta.sucesso(opcao));
  } catch (error) {
    res.json(Resposta.erro('Erro ao obter opção: ' + error.message));
  }
});

// POST - Criar nova opção
router.post('/', async (req: any, res: any) => {
  const mapeadorOpcao = new MapeadorDeOpcaoRespostaChatbotInstagram();
  const mapeadorResposta = new MapeadorDeRespostaChatbotInstagram();
  
  try {
    // Verificar se a resposta pai existe e pertence à empresa
    const respostaPai = await mapeadorResposta.selecioneSync({
      idEmpresa: req.empresa.id,
      id: req.body.respostaId
    });
    
    if (!respostaPai) {
      return res.json(Resposta.erro('Resposta pai não encontrada'));
    }
    
    // Verificar se já existe uma opção com o mesmo texto para esta resposta
    const existe = await mapeadorOpcao.existeSync({
      empresa: req.empresa,
      respostaChatbotInstagram: { id: req.body.respostaId },
      texto: req.body.texto
    });
    
    if (existe) {
      return res.json(Resposta.erro('Já existe uma opção com este texto para esta resposta'));
    }
    
    const opcao = new OpcaoRespostaChatbotInstagram();
    opcao.empresa_id = req.empresa.id;
    opcao.respostaChatbotInstagram = respostaPai;
    opcao.texto = req.body.texto;
    opcao.tipo = req.body.tipo;
    opcao.destino = req.body.destino;
    opcao.url = req.body.url;
    opcao.icone = req.body.icone;
    opcao.ordem = req.body.ordem || 0;
    opcao.ativo = req.body.ativo !== undefined ? req.body.ativo : true;
    
    const opcaoSalva = await mapeadorOpcao.insiraSync(opcao);
    
    res.json(Resposta.sucesso(opcaoSalva));
  } catch (error) {
    res.json(Resposta.erro('Erro ao criar opção: ' + error.message));
  }
});

// PUT - Atualizar opção
router.put('/:id', async (req: any, res: any) => {
  const mapeadorOpcao = new MapeadorDeOpcaoRespostaChatbotInstagram();
  
  try {
    // Verificar se a opção existe e pertence à empresa
    const opcaoExistente = await mapeadorOpcao.selecioneSync({
      idEmpresa: req.empresa.id,
      id: req.params.id
    });
    
    if (!opcaoExistente) {
      return res.json(Resposta.erro('Opção não encontrada'));
    }
    
    // Verificar se o texto não está sendo usado por outra opção da mesma resposta
    if (req.body.texto && req.body.texto !== opcaoExistente.texto) {
      const existe = await mapeadorOpcao.existeSync({
        empresa: req.empresa,
        respostaChatbotInstagram: opcaoExistente.respostaChatbotInstagram,
        texto: req.body.texto,
        id: req.params.id
      });
      
      if (existe) {
        return res.json(Resposta.erro('Já existe outra opção com este texto para esta resposta'));
      }
    }
    
    const opcao = new OpcaoRespostaChatbotInstagram();
    opcao.id = Number(req.params.id);
    opcao.empresa_id = req.empresa.id;
    opcao.texto = req.body.texto || opcaoExistente.texto;
    opcao.tipo = req.body.tipo || opcaoExistente.tipo;
    opcao.destino = req.body.destino || opcaoExistente.destino;
    opcao.url = req.body.url || opcaoExistente.url;
    opcao.icone = req.body.icone || opcaoExistente.icone;
    opcao.ordem = req.body.ordem !== undefined ? req.body.ordem : opcaoExistente.ordem;
    opcao.ativo = req.body.ativo !== undefined ? req.body.ativo : opcaoExistente.ativo;
    
    const opcaoAtualizada = await mapeadorOpcao.atualizeSync(opcao);
    
    res.json(Resposta.sucesso(opcaoAtualizada));
  } catch (error) {
    res.json(Resposta.erro('Erro ao atualizar opção: ' + error.message));
  }
});

// DELETE - Remover opção
router.delete('/:id', async (req: any, res: any) => {
  const mapeador = new MapeadorDeOpcaoRespostaChatbotInstagram();
  
  try {
    // Verificar se a opção existe e pertence à empresa
    const opcao = await mapeador.selecioneSync({
      idEmpresa: req.empresa.id,
      id: req.params.id
    });
    
    if (!opcao) {
      return res.json(Resposta.erro('Opção não encontrada'));
    }
    
    await mapeador.removaAsync({
      id: req.params.id,
      empresa: req.empresa
    });
    
    res.json(Resposta.sucesso({ id: req.params.id, removida: true }));
  } catch (error) {
    res.json(Resposta.erro('Erro ao remover opção: ' + error.message));
  }
});

// DELETE - Remover todas as opções de uma resposta
router.delete('/resposta/:respostaId', async (req: any, res: any) => {
  const mapeador = new MapeadorDeOpcaoRespostaChatbotInstagram();
  
  try {
    await mapeador.removaAsync({
      respostaId: req.params.respostaId,
      empresa: req.empresa
    });
    
    res.json(Resposta.sucesso({ respostaId: req.params.respostaId, removidas: true }));
  } catch (error) {
    res.json(Resposta.erro('Erro ao remover opções da resposta: ' + error.message));
  }
});

export const OpcaoRespostaChatbotInstagramController: Router = router; 