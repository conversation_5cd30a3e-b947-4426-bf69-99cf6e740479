import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {NotificacaoMeioPagamentoService} from "../service/NotificacaoMeioPagamentoService";
import {NotificacaoIugu} from "../domain/faturamento/NotificacaoIugu";
import {NotificacaoMeioPagamento} from "../domain/faturamento/NotificacaoMeioPagamento";
import {NotificacaoCielo} from "../domain/faturamento/NotificacaoCielo";
import {NotificacaoPagarme} from "../domain/faturamento/NotificacaoPagarme";
import {CieloEcommerceService} from "../service/meiospagamentos/CieloEcommerceService";
import {MapeadorDeConfigMeioDePagamento} from "../mapeadores/MapeadorDeConfigMeioDePagamento";
import {NotificacaoCieloCheckout} from "../domain/faturamento/NotificacaoCieloCheckout";
import {PagarmeApiService} from "../service/PagarmeApiService";
import {NotificacaoPagSeguro} from "../domain/faturamento/NotificacaoPagSeguro";
import {MapeadorDePagamentoPedido} from "../mapeadores/MapeadorDePagamentoPedido";
import {MapeadorDeFormaDePagamento} from "../mapeadores/MapeadorDeFormaDePagamento";
import {ERedeItauApi} from "../lib/ERedeItauApi";
import {NotificacaoERede} from "../domain/faturamento/NotificacaoERede";
import {TunaPayApi} from "../lib/TunaPayApi";
import {NotificacaoTunaPay} from "../domain/faturamento/NotificacaoTunaPay";
import {CriadorMensagemPagamentoPendenteService} from "../service/CriadorMensagemPagamentoPendenteService";
const crypto = require('crypto');

const router: Router = Router();


//https://promokit.promokit.com.br/pagamentos/pagseguro/order
router.post('/pagseguro/order', async (req, res) => {
  const dados = req.body;
  console.log('notificou Order pagseguro: ')
  console.log(JSON.stringify(dados))

  if(dados.qr_codes && dados.qr_codes.length){ //pix implemntar
    let qrcode = dados.qr_codes[0];
    let charge = dados.charges.find((item: any) => item.status === 'PAID') || dados.charges[0];
    let status = charge.status;
    let chargeId = charge.id;

    const notificacao =   NotificacaoPagSeguro.novaDoTipo('qrcode', dados);
    await notificacao.salve();

  } else {
    if(dados.charges && dados.charges.length){
      const notificacao =   NotificacaoPagSeguro.novaDoTipo('order', dados);
      await notificacao.salve();

    }
  }

  res.json(new Resposta(true))
})

//https://promokit.promokit.com.br/pagamentos/pagseguro/charge
router.post('/pagseguro/charge', async (req, res) => {
  const dados = req.body;
  console.log('notificou charge pagseguro: ')
  console.log(JSON.stringify(dados))

  const notificacao =   NotificacaoPagSeguro.novaDoTipo('charge', dados)
  await notificacao.salve();

  ExecutorAsync.execute( async (cbAsync: any) => {
    await NotificacaoMeioPagamentoService.executeDoPagseguro(notificacao)
    cbAsync();
  }, 5000)

  res.json(new Resposta(true))
})



router.post('/notificacao/erede', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou erede');
  console.log(dados);

  //caso deseje utilizar autenticação para envio das notificações (opcional)
  let authorization = req.headers['authorization'];
  let requestId = req.headers['request-ID'];

  let idNotificacao = dados.id,
    idTransacao =  dados.data.id || dados.data.qrcode,
    pv  = dados.companyNumber || dados.merchantId, //todo: doc esta companyNumber, produção esta vindo merchantId
    events = dados.events;  //[]PV.UPDATE_TRANSACTION_PIX,PV.REFUND_PIX

  if(!idTransacao  ) return retorneErro('Parametros inválidos', res) ;

  let pagamentoPedido: any = await new MapeadorDePagamentoPedido().tenteSelecionar( { codigoTransacao: idTransacao});
  if( !pagamentoPedido ) {
    console.log('Pagamento ERede não encontrado: ' + idTransacao);
    return  retorneErro('Pagamento não encontrado: ' + idTransacao, res);
  }

  let formaPagamento: any = pagamentoPedido.formaDePagamento;
  formaPagamento = await new MapeadorDeFormaDePagamento().selecioneSync(formaPagamento.id);

  if(!formaPagamento.ERede())
    return     retorneErro('Forma de pagamento invalida', res);

  //todo: merchantId nao bate com pv cadastrado
  //if(pv.toString() !== formaPagamento.configMeioDePagamento.publicKey)
   // return  retorneErro('companyNumber invalido ' + pv, res);

  let transaction: any = await new ERedeItauApi(formaPagamento.configMeioDePagamento).obtenhaTransacao(idTransacao).catch((err) => {
    console.log(err)
    retorneErro(err, res);
  })

  if(transaction){
    if(events[0] === 'PV.UPDATE_TRANSACTION_PIX'){
      transaction.status = 'Approved'
      if(transaction.authorization)
        transaction.authorization.status = 'Approved'
    }

    console.log(transaction)

    let notificacao = new NotificacaoERede(requestId, 'transaction', idTransacao, transaction );
    await notificacao.salve();

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMeioPagamentoService.executeDaRede(notificacao)
      cbAsync();
    }, (err: any) => { console.error(err)}, 2000);

    res.json({ success: true })
  }
})

router.post('/notificacao/erede/estorno', async(req: any, res) => {
  let dados: any = req.body;
  console.log('notificou erede reembolso');
  console.log(dados);

  const {type, tid, nsu, amount, status, cancellationNotice, refundId} = req.body;  //type === refund, notice=cancelId

  let pagamentoPedido = await new MapeadorDePagamentoPedido().selecioneSync( { codigoTransacao: tid});
  if( !pagamentoPedido ) {
    console.log('Pagamento ERede não encontrado: ' + tid);
    return  retorneErro(res, 'Pagamento não encontrado: ' + tid);
  }

  let notificacao = new NotificacaoERede(refundId, type, tid, dados);
  await notificacao.salve();

  ExecutorAsync.execute( async (cbAsync: any) => {
    await NotificacaoMeioPagamentoService.executeDaRede(notificacao)
    cbAsync();
  }, (err: any) => { console.error(err)}, 2000);

  res.json({ success: true })
})




//https://promokit.promokit.com.br/pagamentos/tunapay/payment
router.post('/tunapay/payment', async (req: any, res) => {
  let payload: any = req.body;
  console.log('notificou tunapay pagamento');
  console.log(payload);

  let authorization = req.headers['authorization'];
  let idNotificacao: string = req.headers['id'];
  console.log(authorization)
  let tokenValidao = authorization ?  authorization.replace('Bearer', '').trim() === TunaPayApi.TOKENWEBWOOK : false;

  if(!tokenValidao){
    let payment: any = await new TunaPayApi().obtenhaPagamento(payload.paymentKey)

    if(!payment || payment.status  !== payload.statusId )
      return  res.status(401).json({mensagem: 'not authorization'})
  }


  let notificacao = new NotificacaoTunaPay(idNotificacao, 'payment', payload.paymentKey, payload );
  await notificacao.salve();

  ExecutorAsync.execute( async (cbAsync: any) => {
    await NotificacaoMeioPagamentoService.executeDoTunapay(notificacao)
    cbAsync();
  }, (err: any) => { console.error(err)}, 5000);

  res.json(new Resposta(true));

})

//https://promokit.promokit.com.br/pagamentos/notificacao/pagarme
router.post('/notificacao/pagarme', async (req: any, res) => {
  let payload: any = req.body;
  console.log('notificou pagarme checkout');
  console.log(payload);
  let tipo = payload.type; //charge.

  let hubAssinatura = req.headers['x-hub-signature'];
  if(hubAssinatura){
    console.log('assinatura hub: ' + hubAssinatura)
    let secret = PagarmeApiService.getPrivateKey();
    //SHA-256, SHA-1, MD5
    const hashCalculado = crypto.createHmac('sha1', secret).update(JSON.stringify(payload)).digest('hex');
    console.log('hash calculado: ' + hashCalculado)
    console.log('hash valido: ' + hubAssinatura === hashCalculado)

    if(req.rawBody){
      const hashCalculado2 = crypto.createHmac('sha1', secret).update(req.rawBody).digest('hex');
      console.log('hash calculado2: ' + hashCalculado2)
      console.log('hash valido2: ' + hubAssinatura === hashCalculado2)
    }

  } else {
    console.log('headers:' )
    console.log(req.headers)
  }

  if(tipo && tipo.indexOf('charge.') >= 0){
    let charge: any = payload.data;

    if(charge.metadata &&  charge.metadata.platform === 'promokit'){
      let dados: any = {
        id: charge.id,
        status: charge.status,
        metodoPagamento: charge.payment_method,
      }

      if( charge.last_transaction)
        dados.last_transaction =  charge.last_transaction

      if(charge.metadata)
        dados.metadata = charge.metadata

      let notificacao = new NotificacaoPagarme(payload.id, 'charge', charge.id, dados );
      await notificacao.salve();

      ExecutorAsync.execute( async (cbAsync: any) => {
        await NotificacaoMeioPagamentoService.executeDaPagarme(notificacao)
        cbAsync();
      }, (err: any) => { console.error(err)}, 5000);
    } else{
      console.log('ignorar notificação pagarme outra plataforma')
      console.log(charge.metadata)

    }
  } else {
    console.warn('Ignorando notificação pargarme do tipo: ' + tipo)
  }

  res.json(new Resposta(true));

})

function retorneErro(erro: any, res: any){
  console.warn(erro)
  res.status(400).json({error: erro});
}

//https://promokit.promokit.com.br/pagamentos/notificacao/cielo/checkout
router.post('/notificacao/cielo/checkout', async (req: any, res) => {
  let body: any = req.body;
  let empresa = req.empresa;
  console.log('notificou cielo checkout');
  console.log(body);
  // 1 - > 	Mudança de status do pagamento
  //2 - > Mudança de status do Antifraude
  //7 - > Notificação de chargeback
  //8 - > Alerta de fraude
  let ChangeType =  body.ChangeType;
  let PaymentId =  body.PaymentId;

  if(!PaymentId || !ChangeType) return retorneErro('Pagamento não informado', res)

  let formaDepagamento: any = empresa.obtenhaFormaPagamentoOnline('cielocheckout');

  if(formaDepagamento){
    let config: any = await new MapeadorDeConfigMeioDePagamento().selecioneSync(formaDepagamento.configMeioDePagamento.id);
    let serviceCielo = new CieloEcommerceService(config.merchantId, config.merchantKey);

    let payment: any = await serviceCielo.obtenhaTransacao(PaymentId).catch( (error: any) => {
      console.warn(error)
    })

    if(payment){
      let dados: any = { status:  payment.Payment.Status , tipo: ChangeType}
      let notificacao = new NotificacaoCieloCheckout( 'sale', PaymentId, new Date(), dados );

      await notificacao.salve();
      res.json(new Resposta(true));

      ExecutorAsync.execute( async (cbAsync: any) => {
        await NotificacaoMeioPagamentoService.executeDaCieloCheckout(notificacao)
        cbAsync();
      }, (err: any) => { console.error(err)});

    } else {
      retorneErro('Pagamento não encontrado: ' + PaymentId, res)
    }
  } else {
    retorneErro('Forma de pagamento cielo não encontrada ' + empresa.nome, res)
  }
})

// Ocorre quando a transação é finalizada. Esse POST possui todos os dados do pedido, incluindo o STATUS inicial da transação
router.post('/notificacao/cielo', async (req, res) => {

  try{
    console.log('notificou cielo.....')
    console.log(req.body)
    let body: any = req.body;

    const codigo = body.product_id, //Identificador do Botão/Link de pagamento que gerou a transação
      orderNumber = body.order_number, //Número do pedido enviado pela loja
      checkoutOrderNumber = body.checkout_cielo_order_number, //Identificador único gerado pelo CHECKOUT CIELO
      codigoTipoPagamento = body.payment_method_type,  //'1' -> Cartao.
      codigoBandeira = body.payment_method_brand,
      status = body.payment_status;

    let dados = { status:  status, codigoPagamento: checkoutOrderNumber,
      orderNumber: orderNumber, codigoBandeira: codigoBandeira,
      codigoTipoPagamento: codigoTipoPagamento };

    let notificacao = new NotificacaoCielo(orderNumber, 'transaction', codigo, new Date(), dados );

    await notificacao.salve();
    res.json(new Resposta(true));

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMeioPagamentoService.executeDaCieloSuperlink(notificacao)
      cbAsync();
    }, (err: any) => { console.error(err)});

  } catch (e) {
    console.log('Erro ao receber notificação')
    console.log(e)
    res.status(400);
    res.json(new Resposta(false));
  }

})

//Ocorre quando uma transação possui seu STATUS alterado - EX: “Autorizado” > > > “Pago”
router.post('/notificacao/cielo/status', async (req, res) => {

  try{
    console.log('notificou mundança status cielo.....')
    console.log(req.body)
    let body: any = req.body;

    const codigo = body.product_id, //Identificador do Botão/Link de pagamento que gerou a transação
      orderNumber = body.order_number, //Número do pedido enviado pela loja
      checkoutOrderNumber = body.checkout_cielo_order_number, //Identificador único gerado pelo CHECKOUT CIELO
      codigoTipoPagamento = body.payment_method_type,  //'1' -> Cartao.
      codigoBandeira = body.payment_method_brand,
      status = body.payment_status;

    let dados = { status:  status, codigoPagamento: checkoutOrderNumber,
      orderNumber: orderNumber, codigoBandeira: codigoBandeira,
      codigoTipoPagamento: codigoTipoPagamento };

    let notificacao = new NotificacaoCielo(orderNumber, 'paypament-status', codigo, new Date(), dados );

    await notificacao.salve();
    res.json(new Resposta(true));

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMeioPagamentoService.executeDaCieloSuperlink(notificacao)
      cbAsync();
    }, (err: any) => { console.error(err)});

  } catch (e) {
    console.log('Erro ao receber notificação')
    console.log(e)
    res.status(400);
    res.json(new Resposta(false));
  }

})

router.post('/notificacao/iugu', async (req, res) => {
  let body: any = req.body;
  console.log('iugu notificou: ');
  console.log(body);

  try{

    let event = body.event;
    let tipo = event.split('.')[0];
    let codigo = body["data[id]"];
    let idConta = body["data[account_id]"];
    let status = body["data[status]"];
    let assinatura = body["data[subscription_id]"];
    let dados = { assinatura : assinatura, status: status };

    let id: any = new Date().getTime().toString();
    let notificacao = new NotificacaoIugu(id, tipo, codigo, new Date(), dados );

    await notificacao.salve();
    res.json(new Resposta(true));

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMeioPagamentoService.executeDoIugu(notificacao)
      cbAsync();
    }, (err: any) => { console.error(err)});

  } catch (e) {
    console.log('Erro ao receber paramentos')
    console.log(e)
    res.status(400);
    res.json(new Resposta(false));
  }
})

router.get('/notificacao/:codigo/execute', async (req, res) => {
  let notificacao: NotificacaoMeioPagamento = await NotificacaoMeioPagamento.get(req.params.codigo);

  if(!notificacao) return  res.json(Resposta.erro( 'notificação não existe'));

  try{
   await NotificacaoMeioPagamentoService.execute(notificacao)
   res.json(Resposta.sucesso())
  }catch (e) {
   res.json(Resposta.erro(e.message ? e.message : e))
  }

})

router.post('/notificacao/iugu/teste', async (req: any, res: any) => {
  let body: any = req.body;
  console.log('iugu notificou teste: ');
  console.log(body);
  res.json(new Resposta(true));
})

router.get('/tarefas/pendentes/pix/execute', async (req: any, res: any) => {
  await CriadorMensagemPagamentoPendenteService.criePendentes();
  res.json(new Resposta(true));
})


export const PagamentoController: Router = router;
