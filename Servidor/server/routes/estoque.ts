import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {GrupoDeInsumo} from "../domain/estoque/GrupoDeInsumo";
import {MapeadorDeUnidadeMedida} from "../mapeadores/MapeadorDeUnidadeMedida";
import {MapeadorDeGrupoDeInsumo, MapeadorDeInsumo} from "../mapeadores/MapeadorDeInsumo";
import {MapeadorDeMovimentacaoEstoqueInsumo} from "../mapeadores/MapeadorDeMovimentacaoEstoqueInsumo";
import {EstoqueService} from "../service/EstoqueService";
import {MovimentacaoEstoqueInsumo} from "../domain/estoque/MovimentacaoEstoqueInsumo";
import {Insumo} from "../domain/estoque/Insumo";
import {MotivoDaMovimentacao} from "../domain/estoque/MotivoDaMovimentacao";
import {MapeadorDeRegistroDeOperacao} from "../mapeadores/MapeadorDeRegistroDeOperacao";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";


const router: Router = Router();

router.get('/grupos',  async (req: any, res: any) => {
  let grupos  = await new MapeadorDeGrupoDeInsumo().listeAsync({idEmpresa: req.empresa.id})
  res.json(Resposta.sucesso(grupos))
})

router.get('/insumos',  async (req: any, res: any) => {
  let filtro: any = {idEmpresa: Number( req.empresa.id), orderBy: true}, query: any = req.query;

  if(query.termo) {
    if( Number(query.termo) > 0){
      filtro.codigo = Number(query.termo)
    } else {
      filtro.termo = `%${query.termo}%`
    }
  }

  if(query.comEstoque  )
    filtro.comEstoque = true;

  if(query.vincular  )
    filtro.vincular = true;

  if(query.gid != null){
    if(Number(query.gid) > 0)
      filtro.idGrupo = Number(query.gid)
    else filtro.semGrupo = true;
  }

  let insumos  = await new MapeadorDeInsumo().listeAsync(filtro)

  res.json(Resposta.sucesso(insumos.map((insumo: any) => insumo.toDTO())))
})

router.get('/insumos/:id',  async (req: any, res: any) => {
   let insumo = await new MapeadorDeInsumo().selecioneSync({id: Number(req.params.id)})

  res.json(Resposta.sucesso(insumo.toDTO()))

})

router.get('/insumos/:id/composicoes',  async (req: any, res: any) => {
   let composicoes: Array<Insumo> = await new MapeadorDeInsumo().listeComposicoes(req.params.id )

  res.json(Resposta.sucesso(composicoes.map((insumo: any) => insumo.toDTO())))

})

router.get('/insumos/:id/historico',  async (req: any, res: any) => {
  let idInsumo = req.params.id
  let filtroTexto =  req.query.texto ? '%' + req.query.texto + '%' : null ;
  let filtroOperacao = req.query.operacao ?  req.query.operacao : null;

  let dados: any = {
    idInsumo: idInsumo,
    texto: filtroTexto,
    operacao: filtroOperacao

  }

  if(req.query.i) {
    dados.inicio = Number(req.query.i)
    dados.total = Number(req.query.t)
  }


  let mapeador = new MapeadorDeRegistroDeOperacao()
  let historico = await mapeador.listeAsync(dados)
  let quantidade = await mapeador.obtenhaQuantidade(dados)

  res.json(Resposta.sucesso({
    registros: historico,
    quantidade: quantidade
  }))

})

router.get('/produtos/alerta/minino',  async (req: any, res: any) => {
  let produtos = await new MapeadorDeProduto(req.empresa.catalogo).listeAsync({alertaEstoque: true});

  let dtoInsumos = produtos.map((item: any) => item.obenhaDTOEstoque());

  res.json(Resposta.sucesso({total: dtoInsumos.length, items: dtoInsumos}))
})

router.get('/insumos/alerta/minino',  async (req: any, res: any) => {
  let insumos  = await new MapeadorDeInsumo().listeAsync({idEmpresa: req.empresa.id, alertaEstoque: true});

  let dtoInsumos =  insumos.map((insumo: Insumo) => insumo.toDTO());

  res.json(Resposta.sucesso({total: dtoInsumos.length, items: dtoInsumos}))
})

router.get('/movimentacoes',  async (req: any, res: any) => {
  let filtro: any = { inicio: Number(req.query.i), total: Number(req.query.t)}

  if(req.query.termo) {
    if( Number(req.query.termo) > 0){
      filtro.codigo = Number(req.query.termo)
    } else {
      filtro.termo = `%${req.query.termo}%`
    }
  }

  if(req.query.gid)
    filtro.idGrupo = Number(req.query.gid)

  let movimentacoes: Array<MovimentacaoEstoqueInsumo> = await new MapeadorDeMovimentacaoEstoqueInsumo().listeAsync(filtro);
  let total =  await new MapeadorDeMovimentacaoEstoqueInsumo().selecioneTotal({});

  res.json(Resposta.sucesso({movimentacoes: movimentacoes.map((item: any) => item.toDTO()), total: total}))
})

router.get('/insumos/:id/movimentacoes',  async (req: any, res: any) => {
  let query: any = { idInsumo: Number(req.params.id), inicio: Number(req.query.i), total: Number(req.query.t)}
  let movimentacoes: Array<MovimentacaoEstoqueInsumo>  = await new MapeadorDeMovimentacaoEstoqueInsumo().listeAsync(query);
  let total =  await new MapeadorDeMovimentacaoEstoqueInsumo().selecioneTotal({});

  res.json(Resposta.sucesso({movimentacoes: movimentacoes.map((item: any) => item.toDTO()), total: total}))
})

router.get('/produtos/:id/movimentacoes',  async (req: any, res: any) => {
  let query: any = { idProduto: Number(req.params.id), inicio: Number(req.query.i), total: Number(req.query.t)}
  let movimentacoes  = await new MapeadorDeMovimentacaoEstoqueInsumo().listeAsync(query);
  let total =  await new MapeadorDeMovimentacaoEstoqueInsumo().selecioneTotal({});

  res.json(Resposta.sucesso({movimentacoes: movimentacoes.map((item: any) => item.toDTO()), total: total}))
})

router.get('/unidades', async (req, res) => {
  let unidades = await new MapeadorDeUnidadeMedida().listeAsync( {insumo: true});

  res.json(Resposta.sucesso(unidades))
})


router.get('/movimentacoes/motivos', async (req, res) => {
  let query: any = {manual: true};

  let motivosManual: Array<any> =
    await new MapeadorDeMovimentacaoEstoqueInsumo().listeMotivos( query);

  motivosManual.forEach((item: any) => item.paraInsumoBeneficado = item.id === MotivoDaMovimentacao.EntradaPorBeneficiamento.id)

  res.json(Resposta.sucesso(motivosManual))
})


router.post('/grupo',  async (req: any, res: any) => {
  let dados = req.body;

  let grupo = new GrupoDeInsumo(dados.nome);

  await grupo.salve(true);

  res.json(Resposta.sucesso(grupo))
})


router.put('/grupo',  async (req: any, res: any) => {
  let dados = req.body;

  let grupo = new GrupoDeInsumo(dados.nome);

  grupo.id = dados.id

  await grupo.atualize();

  res.json(Resposta.sucesso(grupo))
})

router.put('/grupo/:id/remova',  async (req: any, res: any) => {
  let dados = req.body;

  let grupo = new GrupoDeInsumo(dados.nome);

  grupo.id = dados.id

  let erro: any = await grupo.remova();

  if(!erro){
    res.json(Resposta.sucesso())
  } else {
    res.json(Resposta.erro(erro))
  }
})

router.post('/insumos',  async (req: any, res: any) => {
  let dados = req.body;

  let insumo: any = await new EstoqueService().salveInsumo(dados, req.user).catch((err: any) => {
    res.json(Resposta.erro(err))
  });

  if(insumo)
    res.json(Resposta.sucesso(insumo))
})

router.put('/insumos',  async (req: any, res: any) => {
  let dados = req.body;

  let insumo: any = await new EstoqueService().atualizeInsumo(dados, req.user).catch((err: any) => {
    res.json(Resposta.erro(err))
  });

  if(insumo)
    res.json(Resposta.sucesso(insumo))

})

router.post('/insumos/:id/produtos/add',  async (req: any, res: any) => {
  let dados = req.body;
  let produtos = dados.produtos;

  if(!produtos.length)
    return res.json(Resposta.erro("Nenhum produto informado"))

  console.log(dados)

  await new EstoqueService().vinculeInsumoAosProdutos({id: req.params.id}, produtos) ;

  let insumo = await new MapeadorDeInsumo().selecioneSync(req.params.id);

  res.json(Resposta.sucesso({ total: produtos.length , produtos: insumo.produtos  }));
})

router.post('/insumos/:id/produtos/del',  async (req: any, res: any) => {
  let dados = req.body;
  let produto = dados.produto;

  if(!produto || !produto.id)
    return res.json(Resposta.erro("Nenhum produto informado"))

  await new EstoqueService().removaVinculoInsumoAoProduto({id: req.params.id}, produto) ;

  res.json(Resposta.sucesso({ removidos: 1} ));
})

router.post('/insumos/:id/opcoes/add',  async (req: any, res: any) => {
  let dados = req.body;
  let opcoes = dados.opcoes;

  if(!opcoes.length)
    return res.json(Resposta.erro("Nenhum opcao informada"))

  console.log(dados)

  await new EstoqueService().vinculeInsumoAsOpcoes({id: req.params.id}, opcoes) ;

  let insumo = await new MapeadorDeInsumo().selecioneSync(req.params.id);

  res.json(Resposta.sucesso({ total: opcoes.length , opcoes: insumo.opcoes  }));
})

router.post('/insumos/:id/opcoes/del',  async (req: any, res: any) => {
  let dados = req.body;
  let opcao = dados.opcao;

  if(!opcao || !opcao.id)
    return res.json(Resposta.erro("Nenhuma opcao informada"))

  await new EstoqueService().removaVinculoInsumoAOpcao({id: req.params.id}, opcao) ;



  res.json(Resposta.sucesso({ removidos: 1} ));
})


router.post('/movimentacoes/manual',  async (req: any, res: any) => {
  let dados = req.body;
  console.log(dados)

  let movimentacoes: any = await new EstoqueService().registreMovimentacoesManual(dados, req.user).catch((err) => {
    res.json(Resposta.erro(err ));
  });

  if(movimentacoes)  res.json(Resposta.sucesso(  ));
})

router.post('/movimentacoes/manual/insumos/:id',  async (req: any, res: any) => {
  let dados = req.body;
  console.log(dados)
  let insumo = await new MapeadorDeInsumo().selecioneSync(Number(req.params.id))

  if(!insumo) return res.json(Resposta.erro('Insumo naõ contrado: ' + req.params.id))

  dados.insumo = insumo;

  let movimentacao: any = await new EstoqueService().registreMovimentacaoManual(dados, req.user).catch((err) => {
    res.json(Resposta.erro(err ));
  });

  if(movimentacao){
    movimentacao.insumo = movimentacao.insumo.toDTO();
    res.json(Resposta.sucesso(movimentacao ));
  }

})

router.put('/insumos/:id/remova',  async (req: any, res: any) => {
  let insumo = await new MapeadorDeInsumo().selecioneSync(req.params.id);

  const erro: any = await insumo.remova();

  if(!erro){
    await new EstoqueService().obtenhaRegistroDeOperacaoService().removeuInsumo(insumo)
    res.json(Resposta.sucesso())
  } else {
    res.json(Resposta.erro(erro ));
  }
});

export const EstoqueController: Router = router;
