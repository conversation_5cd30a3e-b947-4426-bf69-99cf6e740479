import {Router} from 'express';
import {Resposta} from "../utils/Resposta";
import {MapeadorDeRede} from "../mapeadores/MapeadorDeRede";
let path = require('path');

const uuidv1 = require('uuid/v1');
let redis = require("redis");
let fs = require('fs');
let readline = require('readline');

let client = redis.createClient();

const router: Router = Router();

router.get('/liste', async (req: any, res) => {
  res.json( Resposta.sucesso(
    {
      campanhas: [],
      total: 0
    }
  ));
});

router.get('/redes-empresa', async (req: any, res) => {
  let empresa = req.empresa;
  let grupo = empresa.dadosRede.grupo;

  let q = {
    grupo: grupo
  };

  const mapeadorDeRede = new MapeadorDeRede();
  const redes = await mapeadorDeRede.listeAsync(q);

  res.json(Resposta.sucesso(redes));
});

export const RedeController: Router = router;
