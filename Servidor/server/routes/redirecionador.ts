import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {EmpresaService} from "../service/EmpresaService";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {Empresa} from "../domain/Empresa";
import {MapeadorDeBrinde} from "../mapeadores/MapeadorDeBrinde";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {MapeadorDeFoto} from "../mapeadores/MapeadorDeFoto";
import {MapeadorDeAtividade} from "../mapeadores/MapeadorDeAtividade";
import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";
import {ContatoEmpresa} from "../domain/ContatoEmpresa";
// @ts-ignore
import moment = require("moment");
// @ts-ignore
import _ = require("underscore");
moment.locale('pt-br');
import {HorarioFuncionamento} from "../domain/HorarioFuncionamento";
import {ContatoService} from "../service/ContatoService";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {MapeadorDePet} from "../mapeadores/MapeadorDePet";
import {MapeadorDeContatoEmpresa} from "../mapeadores/MapeadorDeContatoEmpresa";
import {Categoria} from "../domain/delivery/Categoria";
import {MapeadorDeCategoria} from "../mapeadores/MapeadorDeCategoria";
import {TomtomService} from "../service/TomtomService";
import {Endereco} from "../domain/delivery/Endereco";
import {Ambiente} from "../service/Ambiente";
import {MapeadorDeProspect} from "../mapeadores/MapeadorDeProspect";
import {ApiCloudfare} from "../lib/cloudfare/ApiCloudfare";
import {Prospect} from "../domain/faturamento/Prospect";
import {MapeadorDeNotificacao} from "../mapeadores/MapeadorDeNotificacao";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {Notificacao} from "../domain/Notificacao";
import {ConfigImpressao} from "../domain/ConfigImpressao";
import {Impressora} from "../domain/Impressora";
import {EnumTamanhoPapel} from "../lib/emun/EnumTamanhoPapel";
import {MapeadorDeConfigImpressao} from "../mapeadores/MapeadorDeConfigImpressao";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {MapeadorDeMesa} from "../mapeadores/MapeadorDeMesa";
var QRCode = require('qrcode')

const router: Router = Router();


router.get('/w', async (req, res) => {

});


export const RedirecionadorController: Router = router;
