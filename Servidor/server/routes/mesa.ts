import {Router} from 'express';
import {Resposta} from "../utils/Resposta";
import {MapeadorDeMesa} from "../mapeadores/MapeadorDeMesa";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Empresa} from "../domain/Empresa";
import {Mesa} from "../domain/Mesa";
import {DTOMesa} from "../lib/dto/DTOMesa";
import {EcleticaService} from "../service/EcleticaService";
import {MapeadorDeComanda} from "../mapeadores/MapeadorDeComanda";
import {MapeadorDeNotificacaoMesa} from "../mapeadores/MapeadorDeNotificacaoMesa";

const router: Router = Router();

router.post('/', async (req: any, res) => {
  const dados: any = req.body;

  const empresa: Empresa = req.empresa;

  insiraOuAtualize(req.empresa, dados).then( (resposta) => {
    res.json(resposta);
  }).catch( erro => {
    res.json(erro);
  });
});



router.get('/status', async(req: any, res) => {
  const empresa: Empresa = req.empresa;
  const mapeadorComanda = new MapeadorDeComanda();

  try {
    // Busca mesas e comandas usando o método do mapeador
    const mesasComComandas = await mapeadorComanda.obtenhaMesasEComandas();

    // Adiciona erros de notificação de integrado com Ecletica
    if(req.empresa.integradoComEcleticaDWMS()){
      for(let mesa of mesasComComandas){
        if (mesa.comandas && mesa.comandas.length) {
          for(let comanda of mesa.comandas){
            const filtro = { idComanda: comanda.id, comErro: true};
            comanda.errosNotificacao = await new MapeadorDeNotificacaoMesa().listeAsync(filtro);
          }
        }
      }
    }

    res.json(Resposta.sucesso(mesasComComandas));
  } catch (error) {
    console.error('Erro ao buscar status das mesas:', error);
    res.json(Resposta.erro('Erro ao buscar status das mesas'));
  }
});

router.get('/:id', async(req: any, res) => {
  const empresa: Empresa = req.empresa;

  const mapeador = new MapeadorDeMesa();

  mapeador.listeAsync({
  }).then( (mesas: Array<Mesa>) => {
    const lista = mesas.map( (mesa) => {
      return new DTOMesa(mesa);
    });

    res.json(Resposta.sucesso(lista));
  });
});

router.put('/', async (req: any, res) => {
  const dados: any = req.body;

  insiraOuAtualize(req.empresa, dados).then( (resposta) => {
    res.json(resposta);
  }).catch( erro => {
    res.json(erro);
  });
});

router.delete('/:id', async(req: any, res) => {
  const id = req.params.id;
  const empresa: Empresa = req.empresa;

  const mesa = new Mesa();
  mesa.id = id;
  mesa.empresa = empresa;

  const mapeadorDeMesa = new MapeadorDeMesa();

  mapeadorDeMesa.marqueComoRemovida(mesa).then( () => {
    new MapeadorDeEmpresa().removaDasCaches(empresa);
    res.json(Resposta.sucesso('Forma de Pagamento Removida com sucesso'));
  });
});

function insiraOuAtualize(empresa: Empresa, dados: any) {
  return new Promise( (resolve, reject) => {
    const mapeadorDeMesa = new MapeadorDeMesa();

    mapeadorDeMesa.selecioneSync({
      nome: dados.nome
    }).then( (mesaExistente: Mesa) => {
      if( mesaExistente && dados.id !== mesaExistente.id && mesaExistente.removida === false) {
        resolve(Resposta.erro('Já existe uma forma de pagamento com esse nome!'));
        return;
      }

      const mesa = Object.assign(new Mesa(), dados);
      mesa.empresa = empresa;
      mesa.removida = false;

      if( mesaExistente )
        mesa.id = mesaExistente.id;


      if( mesa.id ) {
        mapeadorDeMesa.atualizeSync(mesa).then(() => {
          new MapeadorDeEmpresa().removaDasCaches(empresa);
          resolve(Resposta.sucesso(mesa));
        }).catch((erro: Error) => {
          resolve(Resposta.erro('Falha ao realizar a operação: ' + erro.message));
        });
      } else {
        mapeadorDeMesa.insiraGraph(mesa).then( (novoId: number) => {
          mesa.id = novoId;
          new MapeadorDeEmpresa().removaDasCaches(empresa);
          resolve(Resposta.sucesso(mesa));
        }).catch( (erro: Error) => {
          resolve(Resposta.erro('Falha ao realizar a operação: ' + erro.message));
        });
      }
    });
  });
}

router.get('/me/integradas', async(req: any, res) => {
  let empresa = req.empresa;

  if(empresa.integradoComEcleticaDWMS()){
    let integracao = empresa.integracaoDelivery;

    let service: EcleticaService = integracao.obtenhaService();

    if(integracao.integrarComComandas()){
      let comandas: any =   await service.listeMapaDeComandas();

      res.json(Resposta.sucesso(comandas))
    } else {
      let mesas: any =  await service.listeMapaDeMesas();

      res.json(Resposta.sucesso(mesas))
    }
  } else {
     res.json(Resposta.sucesso([]))
  }
});

router.get('/:id/comandas', async (req: any, res: any) => {
  const idMesa = req.params.id;
  const empresa: Empresa = req.empresa;

  try {
    const mapeadorComanda = new MapeadorDeComanda();
    const comandas = await mapeadorComanda.selecioneComandas({
      idMesa: idMesa,
      idEmpresa: empresa.id,
      status: 'ABERTA'
    });

    // Adiciona o tempo para cada comanda
    comandas.forEach((comanda: { horarioAbertura: string | number | Date; tempo: number; }) => {
      const abertura = new Date(comanda.horarioAbertura);
      const agora = new Date();
      comanda.tempo = Math.floor((agora.getTime() - abertura.getTime()) / (1000 * 60 * 60));
    });

    res.json(Resposta.sucesso(comandas));
  } catch (error) {
    console.error('Erro ao buscar comandas da mesa:', error);
    res.json(Resposta.erro('Erro ao buscar comandas da mesa'));
  }
});

export const MesasController: Router = router;
