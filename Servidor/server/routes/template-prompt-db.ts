import {Router} from 'express';
import {Resposta} from "../utils/Resposta";
import {MapeadorDeTemplatePromptDB} from "../mapeadores/MapeadorDeTemplatePromptDB";
import {TemplateDePromptDB} from "../domain/ia/TemplateDePromptDB";
import {MapeadorDeTrechoDePrompt} from "../mapeadores/MapeadorDeTrechoDePrompt";
import { TrechoDePrompt } from '../domain/ia/TrechoDePrompt';

let path = require('path');

let redis = require("redis");

let client = redis.createClient();

const router: Router = Router();

router.put('/', async(req: any, res: any) => {
  const templateDePromptDB: any = req.body;

  const objTemplateDePromptDB = new TemplateDePromptDB();
  Object.assign(objTemplateDePromptDB, templateDePromptDB);

  await new MapeadorDeTemplatePromptDB().atualizeSync(objTemplateDePromptDB);

  res.json(Resposta.sucesso({
    id: objTemplateDePromptDB.id
  }));
});

router.post('/', async(req: any, res: any) => {
  const templateDePromptDB: any = req.body;

  const objTemplateDePromptDB = new TemplateDePromptDB();
  Object.assign(objTemplateDePromptDB, templateDePromptDB);

  const mapeadorDeTemplateDePromptDB = new MapeadorDeTemplatePromptDB();

  if( !templateDePromptDB.id ) {
    await mapeadorDeTemplateDePromptDB.insiraGraph(objTemplateDePromptDB);
  }

  res.json(Resposta.sucesso(templateDePromptDB));
});

router.get('/liste', async (req: any, res: any) => {
  const empresa = req.empresa;
  const global = req.query.g !== undefined;

  const mapeador = new MapeadorDeTemplatePromptDB();
  const query: any = {todos: true};

  if( !global ) {
    query.idEmpresa = empresa.id;
  }
  const lista = await mapeador.listeAsync(query);

  //mapeia a lista de templaes em dtos de templates
  const listaDTO = lista.map((template: TemplateDePromptDB) => {
    return template.crieDTO(true);
  });

  res.json(Resposta.sucesso(listaDTO));
});

router.get('/:id', async (req: any, res) => {
  const mapeadorDeTemplateDePromptDB = new MapeadorDeTemplatePromptDB();
  const idTemplate = req.params.id;
  const empresa = req.empresa;
  const global = req.query.g !== undefined;

  console.log('idTemplate', idTemplate, 'id empresa', empresa.id);

  const templateDePromptDBObj: TemplateDePromptDB = await mapeadorDeTemplateDePromptDB.selecioneSync({
    id: idTemplate
  });

  const mapeador = new MapeadorDeTrechoDePrompt();
  if( global ) {
    mapeador.desativeMultiCliente();
  }

  const trechos = await mapeador.listeAsync({
    idTemplate: idTemplate,
    global: true
  });

  templateDePromptDBObj.trechosDePrompt = trechos;

  res.json(Resposta.sucesso(templateDePromptDBObj));
});


async function salveOuAtualize(templateId: number, trechoDePrompt: any, res: any) {
  const templateDePromptDB = await new MapeadorDeTemplatePromptDB().selecioneSync({id: templateId});

  if (!templateDePromptDB) {
    res.status(400).json(Resposta.erro('O template informado não existe.'));
    return;
  }

  const mapeadorDeTrechoDePrompt = new MapeadorDeTrechoDePrompt();

  if( trechoDePrompt.global ) {
    mapeadorDeTrechoDePrompt.desativeMultiCliente();
  }

  const trechoId = trechoDePrompt.id;
  let trechoDePromptDB: TrechoDePrompt = trechoId ? await mapeadorDeTrechoDePrompt.selecioneSync({
    idTemplate: templateId,
    id: trechoId}) : null;

  if (trechoId && !trechoDePromptDB) {
    res.status(400).json(Resposta.erro('O trecho de prompt informado não existe.'));
    return;
  }

  trechoDePromptDB = trechoDePromptDB || new TrechoDePrompt();
  trechoDePromptDB.template = templateDePromptDB;

  Object.assign(trechoDePromptDB, trechoDePrompt);

  console.log(trechoDePromptDB);

  //se o trecho de prompt é global, e atualização é da empresa, então insere na tabela trecho_de_prompt_empresa
  if (trechoDePrompt.escopo === 'global' && !trechoDePrompt.global) {
    const itens = await mapeadorDeTrechoDePrompt.atualizeAtivarDesativar(trechoDePromptDB);
    res.json(Resposta.sucesso({
      id: trechoDePromptDB.id
    }));

    return;
  }

  if( trechoDePromptDB.tipo === 'exemplos' ) {
    trechoDePromptDB.texto = '';
  }

  if (trechoId) {
    await mapeadorDeTrechoDePrompt.atualizeAsync(trechoDePromptDB);
  } else {
    await mapeadorDeTrechoDePrompt.insiraSync(trechoDePromptDB);
  }

  res.json(Resposta.sucesso({
    id: trechoDePromptDB.id
  }));
}

router.post('/:templateId/ordenar-trechos', async (req: any, res: any) => {
  const mapeador = new MapeadorDeTrechoDePrompt();
  const { templateId } = req.params;
  const templateAAtualizar = req.body;
  const trechosAtualizados = templateAAtualizar.tr;

  // Validação do body
  if (!Array.isArray(trechosAtualizados)) {
    return res.status(400).json(Resposta.erro('Body deve ser um array de trechos.'));
  }

  try {
    // Preparar uma lista de Promises para atualizar cada trecho individualmente
    const updatePromises = trechosAtualizados.map(async trecho => {
      const { id, posicao } = trecho;

      // Validação de cada trecho
      if (typeof id === 'undefined' || typeof posicao === 'undefined') {
        throw new Error('Cada trecho deve conter `id` e `posicao`.');
      }

      // Selecionar o trecho usando o mapeador
      const trechoBD = await mapeador.selecioneSync({ idTemplate: templateId, id: id });

      if (!trechoBD) {
        throw new Error(`Trecho com ID ${id} não encontrado.`);
      }

      // Atualizar a posição do trecho
      trechoBD.posicao = posicao;

      // Utilizar o mapeador para atualizar a posição
      if( trechoBD.escopo === 'global' ) {
        const mapeadorDeTrechoDePrompt = new MapeadorDeTrechoDePrompt();
        mapeadorDeTrechoDePrompt.desativeMultiCliente();

        await mapeadorDeTrechoDePrompt.atualizePosicaoGlobal(trechoBD);
      } else {
        const mapeadorDeTrechoDePrompt = new MapeadorDeTrechoDePrompt();
        await mapeadorDeTrechoDePrompt.atualizePosicaoEmpresa(trechoBD);
      }

      return trechoBD;
    });

    // Executar todas as atualizações de forma paralela
    await Promise.all(updatePromises);

    // Resposta de sucesso
    res.status(200).json(Resposta.sucesso('Ordem dos trechos atualizada com sucesso.'));
  } catch (error) {
    console.error('Erro ao atualizar a ordem dos trechos:', error);
    res.json(Resposta.erro('Erro ao atualizar a ordem dos trechos: ' + error.message));
  }
});

router.put('/:templateId/salvar-trecho', async (req: any, res: any) => {
  const trechoDePrompt: any = req.body;
  const templateId: number = Number(req.params.templateId);

  if (isNaN(templateId)) {
    res.status(400).json(Resposta.erro('Os IDs do template e do trecho devem ser números.'));
    return;
  }

  await salveOuAtualize(templateId, trechoDePrompt, res);
});

router.post('/:templateId/salvar-trecho', async (req, res) => {
  const trechoDePrompt: any = req.body;
  const templateId: number = Number(req.params.templateId);

  if (isNaN(templateId)) {
    res.status(400).json(Resposta.erro('O ID do template deve ser um número.'));
    return;
  }

  await salveOuAtualize(templateId, trechoDePrompt, res);
});

router.post('/:templateId/ativar-desativar-trecho/:id', async (req: any, res: any) => {
  const mapeador = new MapeadorDeTrechoDePrompt();
  const idTrecho = req.params.id;
  const idTemplate = req.params.templateId;
  const trecho = await mapeador.selecioneSync({ idTemplate: idTemplate, id: idTrecho });
  const dados = req.body;

  if (!trecho) {
    res.json(Resposta.erro('O trecho informado não existe.'));
    return;
  }

  trecho.ativo = dados.ativo;
  console.log(trecho);
  await mapeador.atualizeAtivarDesativar(trecho);

  res.json(Resposta.sucesso(trecho));
});

export const TemplatePromptDBController: Router = router;
