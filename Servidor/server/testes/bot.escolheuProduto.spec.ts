import { expect } from 'chai';
import 'mocha';
import {BotsService} from "../service/BotsService";
import {Empresa} from "../domain/Empresa";
import {ConvMock} from "./ConvMock";
import {ObjetoPedidoBot} from "../utils/bot/ObjetoPedidoBot";
import {EnderecoBot} from "../utils/bot/EnderecoBot";
import {EmpresaMock} from "./EmpresaMock";
import {FormaDeEntregaBot} from "../utils/bot/FormaDeEntregaBot";
import {ComunicadorDialogFlowMock} from "./ComunicadorDialogFlowMock";
import {ComunicadorDialogFlow} from "../service/ComunicadorDialogFlow";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {Produto} from "../domain/Produto";
import {AdicionalDeProdutoEscolhaSimples} from "../domain/delivery/AdicionalDeProdutoEscolhaSimples";
import {OpcaoDeAdicionalDeProduto} from "../domain/delivery/OpcaoDeAdicionalDeProduto";
import {ImagemDoProduto} from "../domain/ImagemDoProduto";

class MapeadorDeProdutoMock {
  constructor() {
  }

  selecioneSync() {
    return new Promise( (resolve, reject) => {
      const produto = new Produto(1, 'Monte seu Pastel', 0, '');
      produto.imagens = []
      produto.imagens.push(new ImagemDoProduto('https://fibo.promokit.com.br/imagem.jpg', 0))

      produto.camposAdicionais.push(new AdicionalDeProdutoEscolhaSimples('Sabor', true, [
        new OpcaoDeAdicionalDeProduto('Carne', 2, true, null),
        new OpcaoDeAdicionalDeProduto('Queijo', 2, true, null)
      ], 'produto'));
      produto.camposAdicionais.push(new AdicionalDeProdutoEscolhaSimples('Extras', true, [
        new OpcaoDeAdicionalDeProduto('Bacon', 2, true, null)
      ], 'produto'));

      resolve(produto);
    });
  }
}

describe('Bot Escolheu Produto', () => {
  const empresa = EmpresaMock.Nova();

  it('Deve Mostrar A partir De', () => {
    const conv = new ConvMock(BotsService.Intents.FORMA_DE_ENTREGA);
    conv.reqExpress.empresa = empresa;
    ComunicadorDialogFlow._instance = new ComunicadorDialogFlowMock();
    MapeadorDeProduto._instance = new MapeadorDeProdutoMock();

    const botsService = new BotsService();

    const intentFormaDeEntrega = botsService.NovoPedidoEscolhaProduto();

    return intentFormaDeEntrega.call(this, conv, {codigoDoProduto: '1'}).then( () => {
      expect(conv.obtenhaMensagem()).to.equal(`<<https://fibo.promokit.com.br/imagem.jpg>> Ótimo, você escolheu
📦 *Monte seu Pastel A Partir de R$ 4,00*
*Valor Produto*: R$ 0,00

----------------------
Sabor (Escolha 1 opção)❓

*1.* Carne R$ 2,00
*2.* Queijo R$ 2,00

*v*. Para voltar e remover produto do carrinho
`)
    });
  });
});
