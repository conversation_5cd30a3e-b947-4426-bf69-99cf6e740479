import {MensagemEnviada} from "../domain/MensagemEnviada";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {expect} from "chai";
import {BotsService} from "../service/BotsService";

describe('Mensagem Enviada expirar', async () => {
  it( 'deve expirar mensagem de aniversário', () => {
    const mensagem = new MensagemEnviada();
    mensagem.horario = new Date(new Date().getTime() - (1000 * 60 * 60 * 24));
    mensagem.tipoDeNotificacao = TipoDeNotificacaoEnum.Aniversario;

    expect(true).to.equal(mensagem.expirou());
  });

  it( 'não deve expirar mensagem de aniversário', () => {
    const mensagem = new MensagemEnviada();
    mensagem.horario = new Date();
    mensagem.tipoDeNotificacao = TipoDeNotificacaoEnum.Aniversario;

    expect(false).to.equal(mensagem.expirou());
  });

  it( 'deve expirar mensagem de pedido', () => {
    const mensagem = new MensagemEnviada();
    mensagem.horario = new Date(new Date().getTime() - (1000 * 60 * 60 * 2));
    mensagem.tipoDeNotificacao = TipoDeNotificacaoEnum.PedidoAlterado;

    expect(true).to.equal(mensagem.expirou());
  });

  it( 'não deve expirar mensagem de pedido', () => {
    const mensagem = new MensagemEnviada();
    mensagem.horario = new Date(new Date().getTime() - (1000 * 60 * 60 * 0.5));
    mensagem.tipoDeNotificacao = TipoDeNotificacaoEnum.PedidoAlterado;

    expect(false).to.equal(mensagem.expirou());
  });
});
