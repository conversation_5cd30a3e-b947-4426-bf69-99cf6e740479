export class Contextos {
  contexto: string;
  lifeSpan: number;

  set(contexto: string, lifeSpan: number) {
    this.contexto = contexto;
    this.lifeSpan = lifeSpan;
  }

  get(contexto: string) {
    return this.contexto === contexto ? contexto : null;
  }
}

export class ConvMock {
  intent: string;
  mensagens: Array<string> = [];
  data: any;
  reqExpress: any;
  contexts: Contextos;
  body: any;
  parameters: any;

  constructor(intent: string, contato: any = {nome: 'Eu', telefone: '99999999999'}) {
    this.intent = intent;
    this.data = {};
    this.reqExpress = {};
    this.parameters = {};
    this.contexts = new Contextos();
    this.body = {
      originalDetectIntentRequest: {
        payload: contato
      }
    }
  }

  ask(mensagem: string) {
    this.mensagens.push(mensagem);
  }

  obtenhaMensagem() {
    return this.mensagens.join(' ');
  }
}
