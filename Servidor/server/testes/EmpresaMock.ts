import {Empresa} from "../domain/Empresa";

export class EmpresaMock {
  public static dadosEmpresa = {
    id: 85,
    nome: 'Empresa X',
    formasDePagamento: [
      { id: 2257, nome: "cartao-credito", descricao: "Cartao de Crédito", exibirCardapio: true,
        removida: false, online: false },
      { id: 2258, nome: "cartao-debito", descricao: "Cartao de Débito", exibirCardapio: true,
        removida: false, online: false },
      { id: 2256, nome: "dinheiro", descricao: "Dinhe<PERSON>", exibirCardapio: true,
        removida: false, online: false }
    ],
    formasDeEntrega: [
      { id: 260, valorMinimoPedido: 0, formaDeEntrega: { id: 1, nome: 'Retirar' }, nome: 'Retirar', ativa: false },
      {
        raiosDeCobranca: [
          { tipo: 'VALOR_FIXO', alcance: 2, valorFixo: 1, valorKmTaxa: 0, valorMinimoTaxa: 0, id: 26 },
          { tipo: 'VALOR_POR_DISTANCIA', alcance: 15, valorFixo: 0, valorKmTaxa: 1, valorMinimoTaxa: 1, id: 61 },
          { tipo: 'FRETE_GRATIS', alcance: 234, valorFixo: 0, valorKmTaxa: 0, valorMinimoTaxa: null, id: 485 }
        ],
        "zonasDeEntrega": [
          { "nome": "Aldeota", "valor": 12, "id": 1701 },
          { "nome": "Beira Mar", "valor": 13, "id": 1702 },
          { "nome": "Cajazeiras", "valor": 5, "id": 1706 },
          { "nome": "Dionísio Torres", "valor": 12, "id": 1711 },
          { "nome": "Cambeba", "valor": 5, "id": 1707 },
          { "nome": "Castelão", "valor": 5, "id": 1708 },
          { "nome": "Cidade dos Funcionários", "valor": 5, "id": 1709 },
          { "nome": "Cocó", "valor": 5, "id": 1710 },
          { "nome": "Edson Queiroz", "valor": 5, "id": 1712 },
          { "nome": "Engenheiro Luciano Cavalcante", "valor": 5, "id": 1713 },
          { "nome": "Eusébio", "valor": 13, "id": 1714 },
          { "nome": "Guararapes", "valor": 5, "id": 1715 },
          { "nome": "Jardim das Oliveiras","valor": 5, "id": 1716 },
          { "nome": "José de Alencar", "valor": 5, "id": 1717 },
          { "nome": "Lagoa Redonda", "valor": 5, "id": 1718 },
          { "nome": "Luciano Cavalcante", "valor": 5, "id": 1719 },
          { "nome": "Meireles", "valor": 12, "id": 1720 },
          { "nome": "Messejana", "valor": 5, "id": 1721 },
          { "nome": "Papicu", "valor": 8, "id": 1722 },
          { "nome": "Parque Iracema", "valor": 0, "id": 1723 },
          { "nome": "Parque Manibura", "valor": 5, "id": 1724 },
          { "nome": "Passaré", "valor": 5, "id": 1725 },
          { "nome": "Sapiranga", "valor": 5, "id": 1726 },
          { "nome": "Seis Bocas", "valor": 5, "id": 1727 },
          { "nome": "Água Fria", "valor": 5, "id": 1728 }
        ],
        id: 17, valorMinimoFreteGratis: 0,
        valorMinimoPedido: 0,
        ativa: true,
        tipoDeCobranca: 'zona',
        permiteAgendamento: false,
        exibirTelaBusca: true,
        formaDeEntrega: { id: 2, nome: 'Receber em casa' },
        nome: 'Receber em casa'
      }
    ]
  };

  static Nova() {
    const empresa = new Empresa();
    Object.assign(empresa, EmpresaMock.dadosEmpresa);

    return empresa;
  }
}
