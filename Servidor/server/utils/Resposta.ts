export class Resposta<T> {

  constructor(obj: any = {} as any) { // Deu erro, informar mensagem de erro
    let {
      sucesso = !obj.erro,
      erro = null,
      data = null
    } = obj;

    this.sucesso = sucesso;
    this.data = data;

    if(erro){
      this.erro = erro;
      delete this.data;
    }
    else delete this.erro;

  }

  static erroRequerLogin = new Resposta({ erro: 'Operação requer estar logado'});
  static erroPermissaoUsuario = new Resposta({ erro: 'Usuario não tem permissão para realizar essa operação'} );
  static erroMultipedido = new Resposta({ erro: 'Pedido de um multipedido não pode ser alterado separado, altere o pedido principal'} );
  codigo: number;
  sucesso: boolean;
  erro: string;
  data: T;

  static erro(erro: string): Resposta<string> {
    return new Resposta({erro: erro});
  }

  static erroObjeto(erro: any): Resposta<any> {
    return new Resposta({erro: erro})
  }

  static sucesso(objeto: any = null)  {
    return new Resposta({data: objeto});
  }
}


