import {VariaveisDeRequest} from "../../service/VariaveisDeRequest";
import {Ambiente} from "../../service/Ambiente";
import {PhoneNumberFormat, PhoneNumberUtil} from "google-libphonenumber";
import {Empresa} from "../../domain/Empresa";
import {GcomERPService} from "../../service/integracoes/GcomERPService";
import {Contato} from "../../domain/Contato";
const phoneUtil = PhoneNumberUtil.getInstance();


export const ValorDoCampo = new Map<string, Function>([

  ['[LinkCartao]', async (contexto: any) => {
    return Promise.resolve(contexto.linkCartao);
}],
['[LINK_CUPOM]', async (contexto: any) => {
    return Promise.resolve(contexto.linkCupom);
}],
['[LinkExtratoCartao]', async (contexto: any) => {
    return Promise.resolve(contexto.linkExtratoCartao);
}],
['[Empresa]', async (contexto: any) => {
    return Promise.resolve(contexto.empresa.nome);
}],
['[CODIGO]', async (contexto: any) => {
    return Promise.resolve(contexto.contato.cartoes[0].codigoTemp);
}],
['[PontosExpirar]', async (contexto: any) => {
    return Promise.resolve(contexto.pontosExpirar);
}],
['[TempoRestante]', async (contexto: any) => {
    return Promise.resolve(contexto.tempoRestante);
}],
['[CodigoPedido]', async (contexto: any) => {
    return Promise.resolve(contexto.codigoPedido);
}],
['[LinkPedido]', async (contexto: any) => {
    return Promise.resolve(contexto.linkPedido);
}],
['[ConfirmacaoPedido]', async (contexto: any) => {
    return Promise.resolve(contexto.confirmacaoPedido);
}],
['[DadosPedido]', async (contexto: any) => {
    return Promise.resolve(contexto.dadosPedido);
}],
['[StatusPedido]', async (contexto: any) => {
    return Promise.resolve(contexto.statusPedido);
}],
['[LinkRastreamento]', async (contexto: any) => {
    return Promise.resolve(contexto.urlRastreamento);
}],
['[Link_Desinscrever]', async (contexto: any) => {
    return Promise.resolve(contexto.linkDesinscrever);
}],
['[Link_Cardapio]', async (contexto: any) => {
    return Promise.resolve(contexto.linkCardapio + '\n_Não compartilhe esse link que tem seu telefone_');
}],

['[Codigo_Pedido]', async (contexto: any) => {
  return Promise.resolve(contexto.codigoPedido || ''  );
}],

['[Nova_Mensagem]', async (contexto: any) => {
    return Promise.resolve('[Nova_Mensagem]');
}],
['[Link_Avaliar]', async (contexto: any) => {
    return Promise.resolve(contexto.linkAvaliarPedido + '\n_Não compartilhe esse link que tem seu telefone_');
}],
['[LinkAtivarCartao]', async (contexto: any) => {
    let link = new VariaveisDeRequest().obtenhaUrlRaiz(Ambiente.Instance.contexto().empresa) +
        contexto.contato.getLinkAtivacao();

    return Promise.resolve(link);
}],
['[Telefone_Cliente]', async (contexto: any) => {
    if (!contexto.cliente || !contexto.cliente.telefone) {
        return Promise.resolve('');
    }

    let codigoPais = contexto.cliente.codigoPais;
    let telefone = contexto.cliente.telefone;
    const phoneNumber = phoneUtil.parse(codigoPais + telefone, 'ZZ');

    // Verifica se o número é válido
    if (!phoneUtil.isValidNumber(phoneNumber)) {
        return Promise.resolve(telefone);
    }

    if (codigoPais === '+55')
        return Promise.resolve(phoneUtil.format(phoneNumber, PhoneNumberFormat.NATIONAL));

    return Promise.resolve(phoneUtil.format(phoneNumber, PhoneNumberFormat.INTERNATIONAL));
}],
['[Nome_Cliente]', async (contexto: any) => {
    if (!contexto.cliente || !contexto.cliente.nome) {
        return Promise.resolve('');
    }

    return Promise.resolve(contexto.cliente.nome);
}],
['[TelefoneContato]', async (contexto: any) => {
    if (!contexto.contato || !contexto.contato.telefone) {
        return Promise.resolve('');
    }

    const number = phoneUtil.parseAndKeepRawInput(contexto.contato.telefone, 'BR');

    return Promise.resolve(phoneUtil.format(number, PhoneNumberFormat.NATIONAL));
}],
['[NomeCompletoContato]', async (contexto: any) => {
    if (!contexto.contato.nome) {
        return Promise.resolve('Cliente');
    }

    let nome = contexto.contato.nome.trim();

    if (nome.length === 0) {
        return Promise.resolve('');
    }

    return Promise.resolve(nome);
}],
['[NomeContato]', async (contexto: any) => {
    if (!contexto.contato.nome) {
        return Promise.resolve('Cliente');
    }

    let nome = contexto.contato.nome.trim();

    const primeiroNome = nome.replace(/ .*/, '');

    if (primeiroNome.length === 0) {
        return Promise.resolve('');
    }

    return Promise.resolve(primeiroNome[0].toUpperCase() + primeiroNome.slice(1).toLowerCase());
}],
['[HorarioFuncionamento]', async (contexto: any) => {
    let empresa = contexto.empresa
    let frase = empresa.obtenhaDescricaoHorarioAtendimento();

    frase = "*Horario de funcionamento:*\n" + frase

    return Promise.resolve(frase);
}],
[
  '[SALDO]', async (contexto: any) =>
{
  return new Promise((resolve, reject: any) => {
    let contato: Contato = contexto.contato;

    if(!contato.cartoes || contato.cartoes.length === 0) { resolve(''); return; }

    let cartao = contato.cartoes[0];

    if(!cartao.acumulaReais()) { resolve(''); return; }

    const formattedValue = cartao.pontos.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    });

    resolve(formattedValue);
  })
}
],
['[Saldo_Cashback_Trigo]', async (contexto: any) => {
  return new Promise( (resolve, reject) => {
    let empresa = contexto.empresa;
    let telefone = contexto.contato.telefone;

    const gcomERPService = new GcomERPService();

    gcomERPService.obtenhaSaldoCashback(empresa, telefone).then((cashback: any) => {
      if( cashback.valor_cashback_saldo === 0 ) {
        reject('Sem saldo cashback');
        return;
      }

      const formattedValue = cashback.valor_cashback_saldo.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });

      resolve(formattedValue);
    }).catch( (erro) => {
      reject('Sem saldo cashback');
    });
  });
}],
['[TempoEntrega]', async (contexto: any) => {
    let empresa: Empresa = contexto.empresa

    let tipoDeEntrega = contexto.tipoDeEntrega

    if (tipoDeEntrega === 'RETIRADA')
        return Promise.resolve('Tempo estimado para retirada: ' + empresa.obtenhaFormaEntregaRetirada().tempoMinimoRetirada
            + " ~ " + empresa.obtenhaFormaEntregaRetirada().tempoMaximoRetirada + "m");
    if (tipoDeEntrega === 'ENTREGA')
        return Promise.resolve('Tempo estimado para entrega: ' + empresa.obtenhaFormaReceberEmCasa().tempoMinimo
            + " ~ " + empresa.obtenhaFormaReceberEmCasa().tempoMaximo + "m");

    return Promise.resolve("");
}]
]);
