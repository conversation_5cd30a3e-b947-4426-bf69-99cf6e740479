export class Localizacao {
  constructor(public latitude: string, public longitude: string) {
  }

  static fromString(latLong: any): Localizacao {
    if (typeof latLong === 'object') {
      return new Localizacao(latLong.lat.toString(), latLong.lon.toString());
    }

    if (!latLong) {
      return null;
    }
    const latitude1 = latLong.split(',')[0];
    const longitude1 = latLong.split(',')[1];

    return new Localizacao(latitude1, longitude1);
  }

  obtenhaString() {
    return this.latitude + "," + this.longitude;
  }

  lat() {
    return parseFloat(this.latitude)
  }

  lng() {
    return parseFloat(this.longitude);
  }
}
