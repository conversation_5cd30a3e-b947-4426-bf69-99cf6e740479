import {ZonaDeEntrega} from "../../domain/delivery/ZonaDeEntrega";

export class EnderecoBot {
  id: number;
  descricao: string;
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localizacao: any;
  numero: string;
  cidade: any;
  estado: any;
  taxaDeEntrega: number;
  nome: string;
  zona: ZonaDeEntrega;

  constructor(id: number, cidade: any, cep: string, logradouro: string,
              complemento: string, bairro: string, numero: any, descricao: string = null, localizacao: any) {
    this.id  = id;
    this.cidade = cidade;
    this.cep = cep;
    this.logradouro = logradouro;
    this.complemento = complemento;
    this.bairro = bairro;
    this.numero = numero;
    this.descricao = descricao;
    this.localizacao = localizacao;
  }

  obtenhaEnderecoCompleto() {
    if(this.descricao)  return  this.descricao;

    return  this.obtenhaEndereco();
  }

  obtenhaNomeAutocomplete() {
    let endereco = this.logradouro || '' ;

    if(this.complemento)  endereco  = endereco + ', ' + this.complemento;

    if(this.cep) endereco = endereco + ' ' + this.cep

    endereco = endereco[0].toUpperCase() + endereco.slice(1).toLocaleLowerCase()

    return endereco;
  }

  obtenhaEndereco() {
    let endereco = this.logradouro || '' ;

    if(this.complemento)  endereco  = endereco + ', ' + this.complemento;

    if(this.numero) endereco = endereco + ' num. ' + this.numero

    endereco = endereco[0].toUpperCase() + endereco.slice(1).toLocaleLowerCase()

    if(this.bairro) endereco = endereco +  ' Bairro: ' + this.bairro[0].toUpperCase() + this.bairro.slice(1).toLocaleLowerCase();

    if(this.cidade)  endereco = endereco + ", " + this.cidade.nome + "-" + this.cidade.estado.nome;

    return endereco;
  }

  // tslint:disable-next-line:member-ordering
  static novo(){
    return new EnderecoBot(null , null, null, null, null,  null, null,
      null, null);
  }

  informadoManualmente() {
    return this.localizacao != null;
  }
}
