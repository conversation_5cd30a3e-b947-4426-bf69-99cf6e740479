import {NotaFiscalEletronica} from "../../../domain/nfce/NotaFiscalEletronica";
import {ItemNFe} from "../../../domain/nfce/ItemNFe";

export class DTOImpressaoNfce {
  id: string
  emitente: any
  destinatario: any
  itens: any[]
  pagamentos: any[]
  valorItens: number;
  valorTotal: number;
  urlConsulta: string;
  chaveAcesso: string;
  protocolo: string;
  status: number;
  dataAutorizacao: string;
  numeroNFe: number;
  serie: number;
  dataEmissao: Date;
  qrCode: string
  mensagem: string;
  logo?: string;



  constructor(nota: NotaFiscalEletronica, empresa: any) {
    this.id = nota.id
    this.emitente = {
      cnpj: nota.cnpjEmitente,
      razaoSocial: nota.nomeEmitente,
      endereco: {
        logradouro: nota.enderecoEmitente.logradouro,
        numero: nota.enderecoEmitente.numero,
        complemento: nota.enderecoEmitente.complemento,
        bairro: nota.enderecoEmitente.bairro,
        nomeMunicipio: nota.enderecoEmitente.nomeMunicipio,
        siglaUF: nota.enderecoEmitente.siglaUF,
        telefone: nota.enderecoEmitente.telefone
      }
    }

    // Adiciona dados do destinatário se existir
    if (nota.nomeDestinatario) {
      this.destinatario = {
        nome: nota.nomeDestinatario,
        cpf: nota.cpfDestinatario
      }
    }

    // Mapeia os pagamentos usando o enum EnumMeioDePagamentoNotaFiscal
    this.pagamentos = nota.pagamentos ? nota.pagamentos.map(pagamento => ({
      forma: this.getFormaPagamentoTexto(pagamento.meioDePagamento),
      valor: pagamento.valor
    })) : []

    this.itens = []
    let item: ItemNFe
    for (item of nota.itens) {
      this.itens.push({
        codigo: item.codigoProduto,
        descricao: item.descricaoDoProduto,
        qtd: item.qtdeComercial,
        un: item.unidadeComercial,
        valorUnitario: item.valorUnicoComercial,
        valorTotal: item.valorTotalBruto
      })
    }

    this.valorItens = nota.valorTotalProdutos
    this.valorTotal = nota.valorTotalNFe
    this.urlConsulta = nota.gereUrlChave2()
    this.chaveAcesso = this.formateChaveDeAcesso(nota.chaveDeAcesso)
    this.protocolo = nota.numeroProtocolo
    this.status = nota.status
    this.numeroNFe = nota.numeroNFe
    this.serie = nota.serie
    this.dataEmissao = nota.dataDeEmissao
    this.qrCode = nota.qrCode ? nota.qrCode : nota.obtenhaURLQrCode2()
    //a data de autorizacao para ser impressa no danfe nfce
    this.dataAutorizacao =  nota.dataAutorizacao ? nota.dataAutorizacao.toLocaleString() : null
    this.mensagem = nota.informacoesAdicionaisContrib ? nota.informacoesAdicionaisContrib : ""

    if (nota.informacoesAdicionaisContrib && nota.infAdicionaisFisco)
      this.mensagem += '\n'

    this.mensagem += nota.infAdicionaisFisco ? nota.infAdicionaisFisco : ""
  }




  private getFormaPagamentoTexto(meioPagamento: string | number): string {
    const meioPagamentoStr = String(meioPagamento).padStart(2, '0');

    const formasPagamento: { [key: string]: string } = {
      '01': "Dinheiro",
      '02': "Cheque",
      '03': "Cartão de Crédito",
      '04': "Cartão de Débito",
      '05': "Crédito Loja",
      '10': "Vale Alimentação",
      '11': "Vale Refeição",
      '12': "Vale Presente",
      '13': "Vale Combustível",
      '15': "Boleto Bancário",
      '16': "Depósito Bancário",
      '17': "PIX",
      '18': "Transferência",
      '19': "Programa de Fidelidade",
      '90': "Sem Pagamento",
      '99': "Outros"
    };

    return formasPagamento[meioPagamentoStr] || "Outros";
  }

  formateChaveDeAcesso(accessKey: string): string {
    // Ensure the access key is 44 characters long
    if (accessKey.length !== 44) {
      throw new Error("Invalid access key length");
    }

    // Split the access key into blocks of 4 characters
    const blocks = accessKey.match(/.{1,4}/g);

    // Join the blocks with spaces
    return blocks.join(" ");
  }
}
