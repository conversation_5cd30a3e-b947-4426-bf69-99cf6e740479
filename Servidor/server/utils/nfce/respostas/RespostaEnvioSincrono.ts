import {NotaFiscalEletronica} from "../../../domain/nfce/NotaFiscalEletronica";

export class RespostaEnvioSincrono {
  foiAutorizada: boolean;
  resposta = '';
  nfe: NotaFiscalEletronica;
  estado: string;
  chaveDeAcesso: string;
  numeroProtocolo: string;
  motivo: string;


  constructor(foiAutorizada: boolean, estado: string, chaveDeAcesso: string, numeroProtocolo: string) {
    this.foiAutorizada = foiAutorizada;
    this.estado = estado;
    this.chaveDeAcesso = chaveDeAcesso;
    this.numeroProtocolo = numeroProtocolo;
  }
  get foiCancelada(): boolean {
    return this.estado === "Cancelada";
  }

  toString(): string {
    return `RespostaEnvioSincrono(nfe: ${this.nfe}, estado: ${this.estado}, foiAutorizada: ${this.foiAutorizada}, NumeroProtocolo: ${this.numeroProtocolo}, resposta: ${this.resposta.replace('\n', ' ')})`;
  }
}
