const moment = require("moment");

export class FormatadorDeDatas {
  private formato = 'YYYY-MM-DD'
  parse(text: any): Date {
    return this.parseFormat(text, this.formato);

  }

  parseFormat(text: any, format: any): Date {
    const parsedMoment = moment.parseZone(text, format);

    if (!parsedMoment.isValid()) {
      throw new Error('Invalid date string');
    }

    // Converter para um objeto Date e retornar
    return parsedMoment.toDate();

  }

  parseSefaz(text: any) {
    const format = 'YYYY-MM-DDTHH:mm:ssZ';

    return this.parseFormat(text, format)
  }

  formateSefaz(data: Date): string {
    if (!data) {
      console.log("Data é nula, não pode ser formatada");
      return '';
    }

    console.log(`Tentando fazer o parse da data ${data}`);
    return moment(data).format('YYYY-MM-DDTHH:mm:ssZ');
  }

  formate(data: Date | null): string {
    if (!data) {
      console.log("Tentando formatar data: Data é nula");
      return '';
    }

    const dataFormatada = moment(data).format(this.formato);
    console.log(`Data ${data} formatada: ${dataFormatada}`);
    return dataFormatada;
  }
}
