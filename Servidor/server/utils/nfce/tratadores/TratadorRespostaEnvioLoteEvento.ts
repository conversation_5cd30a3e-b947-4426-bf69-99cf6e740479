import { TratadorDeResposta } from './TratadorDeResposta';
import { RespostaEnvioLoteEvento } from '../respostas/RespostaEnvioLoteEvento';
import { FabricaDeTratadorDeResposta } from '../comunicacao/FabricaDeTratadorDeResposta';
import { XmlUtils } from '../comunicacao/XmlUtils';
import {TratadorRespostaEnvioEvento} from "./TratadorRespostaEnvioEvento";

/*
<retEnvEvento versao="1.00" xmlns="http://www.portalfiscal.inf.br/nfe">
  <idLote>123456789012345</idLote>
  <tpAmb>1</tpAmb> <!-- 1 = Produção, 2 = Homologação -->
  <verAplic>SVRS202403051420</verAplic>
  <cOrgao>91</cOrgao> <!-- Código do órgão que processou -->
  <cStat>128</cStat>
  <xMotivo>Lote de evento processado</xMotivo>
  <retEvento versao="1.00">
    <infEvento>
      <tpAmb>1</tpAmb>
      <verAplic>SVRS202403051420</verAplic>
      <cOrgao>91</cOrgao>
      <cStat>135</cStat>
      <xMotivo>Evento registrado e vinculado a NF-e</xMotivo>
      <chNFe>35123456789012345678901234567890123456789012</chNFe>
      <tpEvento>110111</tpEvento>
      <xEvento>Cancelamento</xEvento>
      <nSeqEvento>1</nSeqEvento>
      <CNPJDest>12345678000195</CNPJDest>
      <dhRegEvento>2025-04-14T15:25:00-03:00</dhRegEvento>
      <nProt>135230000123456</nProt>
    </infEvento>
  </retEvento>
</retEnvEvento>

*/
export class TratadorRespostaEnvioLoteEvento extends TratadorDeResposta {
  async trate(): Promise<RespostaEnvioLoteEvento> {
    console.log("Chamou tratador de resposta do envio de lote de evento");

    let xmlResposta = this.xmlResposta['retEnvEvento']

    const respostaEnvioLoteEvento = new RespostaEnvioLoteEvento(
      xmlResposta['idLote'],
      xmlResposta['cStat'],
      xmlResposta['xMotivo']
    );

    // Como sabemos que sempre teremos apenas um retEvento, podemos tratar diretamente
    console.log("Tratando resposta de evento");


    const respostaEnvioEvento = await FabricaDeTratadorDeResposta
      .obtenhaInstancia()
      .ObtenhaTratadorEnvioEvento(XmlUtils.transformeEmXml(xmlResposta['retEvento']))
      .trate(null);

    respostaEnvioLoteEvento.resposta = respostaEnvioEvento;
    return respostaEnvioLoteEvento;
  }
}
