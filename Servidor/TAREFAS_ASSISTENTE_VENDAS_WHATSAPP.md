# Tarefas - Assistente de Vendas WhatsApp

## Infraestrutura Existente ✅

- **ChatGPTService**: Serviço de IA já implementado e funcional
- **Integração WhatsApp**: Sistema que detecta mudança de conversa
- **Extensão Chrome**: Infraestrutura de side panel funcionando
- **Roteamento Dinâmico**: Sistema que exibe páginas diferentes por contexto

## Fase 1: MVP - Componentes Básicos

### 1.1 Frontend - Componente Angular
**Tempo estimado: 6-8 horas**

- [ ] **<PERSON><PERSON>r `whatsapp-assistant.component.ts`**
  - Exibir dados do contato atual
  - Mostrar contexto da conversa
  - Interface para sugestão de resposta

- [ ] **Implementar serviço `WhatsappAssistantService` (Angular)**
  ```typescript
  class WhatsappAssistantService {
    gerarSugestao(telefone: string, contexto: any): Observable<any>
    copiarParaClipboard(texto: string): void
  }
  ```

- [ ] **Template HTML básico**
  ```html
  <div class="whatsapp-assistant">
    <div class="contato-info">
      <h3>{{contato.nome}}</h3>
      <span>{{contato.telefone}}</span>
    </div>
    
    <div class="fase-spin">
      <span class="badge">Fase: {{faseSpin}}</span>
    </div>
    
    <div class="contexto-conversa">
      <h4>Contexto da Conversa:</h4>
      <p>{{contextoResumo}}</p>
    </div>
    
    <button (click)="gerarSugestao()" [disabled]="carregando">
      🤖 Sugerir Resposta
    </button>
    
    <div class="sugestao" *ngIf="sugestao">
      <h4>Sugestão:</h4>
      <textarea [(ngModel)]="sugestao.texto"></textarea>
      <div class="acoes">
        <button (click)="usarSugestao()">✅ Usar</button>
        <button (click)="editarSugestao()">✏️ Editar</button>
        <button (click)="regenerarSugestao()">🔄 Regenerar</button>
      </div>
    </div>
  </div>
  ```

### 1.2 Integração com WhatsApp
**Tempo estimado: 3-4 horas**

- [ ] **Atualizar content script do WhatsApp**
  - Capturar nome do contato ativo
  - Extrair últimas mensagens da conversa
  - Enviar dados para o componente Angular

- [ ] **Modificar roteamento**
  - Detectar quando está em conversa do WhatsApp
  - Carregar componente `whatsapp-assistant`
  - Passar telefone/contato como parâmetro

### 1.3 Backend - API de Sugestões
**Tempo estimado: 4-6 horas**

- [ ] **Criar rota `/api/whatsapp/sugestao-resposta`** 
  - Método POST
  - Recebe: telefone, mensagens, contexto_produto
  - Retorna: sugestão de resposta, fase_spin, confiança

- [ ] **Implementar WhatsAppAssistantService**
  ```typescript
  class WhatsAppAssistantService {
    async gerarSugestaoResposta(telefone: string, mensagens: any[], contexto: any)
    async identificarFaseSpin(mensagens: any[])
    async construirPromptSpin(fase: string, contexto: any)
  }
  ```

- [ ] **Criar prompts base para cada fase SPIN**
  - Situação: Focar em perguntas sobre contexto atual
  - Problema: Identificar dores e dificuldades
  - Implicação: Explorar impactos dos problemas
  - Necessidade: Demonstrar valor da solução

## Fase 2: Inteligência e Melhorias

### 2.1 Detector de Fase SPIN Automático
**Tempo estimado: 4-5 horas**

- [ ] **Implementar algoritmo de detecção**
  ```typescript
  detectarFaseSpin(mensagens: any[]): string {
    // Analisa padrões nas mensagens para identificar:
    // - Situação: Cliente explicando contexto
    // - Problema: Menções de dificuldades/dores
    // - Implicação: Discussão de impactos
    // - Necessidade: Interesse em soluções
  }
  ```

- [ ] **Palavras-chave por fase**
  - Situação: "atualmente", "fazemos", "processo", "sistema"
  - Problema: "dificuldade", "problema", "demora", "erro"
  - Implicação: "impacto", "prejuízo", "tempo perdido", "custo"
  - Necessidade: "preciso", "quero", "gostaria", "solução"

### 2.2 Prompts Avançados
**Tempo estimado: 2-3 horas**

- [ ] **Criar templates por tipo de produto**
  - Software de gestão
  - Serviços de marketing
  - Consultoria
  - E-commerce

- [ ] **Implementar variações de tom**
  - Formal/corporativo
  - Informal/próximo
  - Técnico/detalhado

### 2.3 Cache e Performance
**Tempo estimado: 2-3 horas**

- [ ] **Sistema de cache de respostas**
  - Evitar regenerar para contextos similares
  - Cache baseado em hash do contexto

- [ ] **Otimizações**
  - Debounce para evitar chamadas desnecessárias
  - Indicadores de loading
  - Tratamento de erros da API

## Fase 3: Recursos Avançados

### 3.1 Personalização
**Tempo estimado: 3-4 horas**

- [ ] **Configurações por empresa**
  - Prompt base personalizado
  - Tom padrão
  - Produtos/serviços principais

- [ ] **Templates pré-definidos**
  - Primeira abordagem
  - Objeções comuns
  - Fechamento de venda

### 3.2 Analytics e Métricas
**Tempo estimado: 4-5 horas**

- [ ] **Tabela de logs**
  ```sql
  CREATE TABLE whatsapp_assistant_usage (
    id SERIAL PRIMARY KEY,
    telefone VARCHAR(20),
    fase_spin VARCHAR(20),
    sugestao_gerada TEXT,
    sugestao_usada BOOLEAN,
    tempo_resposta INTEGER,
    data_uso TIMESTAMP
  );
  ```

- [ ] **Dashboard de métricas**
  - Taxa de uso das sugestões
  - Fases SPIN mais comuns
  - Tempo médio de resposta

## Estrutura de Arquivos

```
Servidor/
├── server/
│   ├── routes/
│   │   └── whatsapp-assistant.ts      # Novas rotas da API
│   ├── service/
│   │   └── WhatsAppAssistantService.ts # Lógica de negócio
│   └── domain/
│       └── WhatsAppAssistant.ts        # Entidades
├── src/app/
│   ├── whatsapp-assistant/
│   │   ├── whatsapp-assistant.component.ts
│   │   ├── whatsapp-assistant.component.html
│   │   ├── whatsapp-assistant.component.scss
│   │   └── whatsapp-assistant.service.ts
│   └── services/
│       └── whatsapp-assistant.service.ts
└── WhatsappChrome2/
    └── content-whatsapp.js             # Atualizado para capturar contexto
```

## Checklist de Implementação

### Preparação
- [ ] Verificar se ChatGPTService está funcionando
- [ ] Confirmar integração WhatsApp existente
- [ ] Definir rota para o componente

### Desenvolvimento
- [ ] Implementar backend (API + Service)
- [ ] Criar componente Angular
- [ ] Integrar com content script
- [ ] Testar fluxo básico

### Testes
- [ ] Testar geração de sugestões
- [ ] Validar detecção de fase SPIN
- [ ] Verificar copy para clipboard
- [ ] Testar com diferentes tipos de conversa

### Deploy
- [ ] Atualizar banco de dados (se necessário)
- [ ] Deploy do backend
- [ ] Deploy do frontend
- [ ] Publicar extensão atualizada

## Prompts Base (Exemplos)

### Situação
```
Você é um assistente de vendas especializado em SPIN Selling. 
O cliente está na fase SITUAÇÃO, onde preciso entender o contexto atual.
Baseado na conversa abaixo, sugira uma resposta que:
1. Demonstre interesse genuíno
2. Faça uma pergunta sobre a situação atual
3. Seja natural e conversacional

Conversa: {contexto}
Produto: {produto}
```

### Problema
```
O cliente está na fase PROBLEMA. Preciso identificar dores e dificuldades.
Sugira uma resposta que:
1. Demonstre empatia
2. Explore problemas específicos
3. Faça perguntas que revelem dores

Conversa: {contexto}
```

## Estimativa Total

- **Fase 1 (MVP)**: 13-18 horas
- **Fase 2 (Inteligência)**: 8-11 horas  
- **Fase 3 (Avançado)**: 7-9 horas

**Total estimado**: 28-38 horas de desenvolvimento

## Próximos Passos

1. **Definir prioridade**: Qual fase implementar primeiro?
2. **Setup do ambiente**: Confirmar dependências
3. **Criar branch**: `feature/whatsapp-assistant`
4. **Começar pela API**: Implementar backend primeiro
5. **Testes incrementais**: Testar cada funcionalidade isoladamente