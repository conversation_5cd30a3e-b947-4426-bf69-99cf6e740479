{"version": 3, "file": "839.571ccbf6b7d5cd12.js", "mappings": "2NAMO,IAAMA,EAAsB,MAA7B,MAAOA,EAWXC,YAAoBC,wBAKlBC,KAAKC,SAAWD,KAAKE,mBAEvB,CAOOC,UACFH,KAAKD,cAAcK,aACpBC,SAASC,KAAKC,MAAMC,IAAM,IAAIC,OAAOC,YACrCL,SAASC,KAAKC,MAAMI,SAAW,SAEjCN,SAASC,KAAKM,YAAaZ,KAAKC,SAElC,CAIOY,SACL,GAAGb,KAAKD,cAAcK,WAAY,CAChC,MAAMM,EAAUL,SAASC,KAAKC,MAAMC,IACpCH,SAASC,KAAKC,MAAMI,SAAW,GAC/BN,SAASC,KAAKC,MAAMC,IAAM,GAE1BC,OAAOK,SAAS,GAA8B,EAA3BC,SAASL,GAAW,MAEzCL,SAASC,KAAKU,YAAahB,KAAKC,SAElC,CAOQC,oBAEN,IAAIK,EAAQF,SAASY,cAAe,SAEpCV,SAAMW,KAAO,WACbX,EAAMY,aAAc,aAAc,wCAClCZ,EAAMa,YAAc,+FAMZb,CAEV,+CAnEWV,GAAsBwB,gFAAtBxB,EAAsByB,QAAtBzB,EAAsB0B,qBAFrB,SAED1B,CAAsB,kEC+DbwB,uCACEA,kCACFA,+EAD2BA,gMAI3BA,sCACEA,sBACFA,+EADEA,+HAEFA,sCACEA,uCACFA,oJAfRA,uCAAqK,WAArKA,CAAqK,WAArKA,CAAqK,YAG/HA,sBAAiBA,2BACjDA,uCAA8BA,+HAASA,qDAAiC,GACtEA,yCAGFA,2BACAA,uCACEA,wCAGAA,wCAGFA,4FAhBcA,4GAGkBA,+DAEfA,sFAKGA,uFAGAA,mHAf9BA,uCAA+C,YAE3CA,0CAoBFA,wEApBwHA,yGAP5HA,uCAA4E,QAExEA,sBACFA,2BAEAA,yCAwBFA,sEA3BIA,2FAG6BA,mIAgC3BA,uCAAqJ,WAArJA,CAAqJ,WAArJA,CAAqJ,WAArJA,CAAqJ,WAArJA,CAAqJ,YAMvCA,2HAASA,yCAAc,GAA3HA,+BAGJA,uCAAyB,UAAzBA,CAAyB,YAEWA,sBAAeA,gGAXgBA,4GAM1BA,qJAKLA,2FAb9CA,uCAA+C,YAE3CA,0CAiBFA,wEAjBsCA,yGAP1CA,uCAA+E,QAE3EA,sBACFA,2BAEAA,yCAqBFA,sEAxBIA,uFAG6BA,8FAkCzBA,uCAA2D,UACjBA,kCAAuEA,uDAA5GA,oGAAgEA,oKAZ/EA,uCAAuD,WAAvDA,CAAuD,QAGjDA,4CACFA,2BACAA,uCAAuB,YAEnBA,qCACFA,2BACAA,uCAAmB,YAEfA,0CAGFA,6BAEFA,wCAAyC,WAErCA,mCACAA,kDAEFA,8EAdQA,mGAIgBA,4EAMrBA,8IAC6BA,uGAAuD,gNArIrGA,sCAA+C,SAE3CA,+BACFA,2BAEAA,iCAEO,WAMPA,uCAAkC,YAE9BA,kCACAA,uCAA8B,aAE5BA,uBACFA,2BACEA,yCACAA,uBACFA,2BACEA,sCACEA,mCACAA,sCACAA,qDACFA,iCAKJA,wCACEA,uBACFA,2BACAA,wCAAiB,aACUA,mCAEhBA,wCAAuBA,uBAAoBA,6BACpDA,sCAAmD,YAAnDA,CAAmD,aAG7CA,mCACFA,2BACAA,yCACAA,yCACFA,mCAQNA,wCAA6B,YAA7BA,CAA6B,aAGvBA,0CA8BAA,2CA2BFA,2BACAA,4CA8BFA,yEAhJCA,8IAKEA,iHAIAA,iHAK2BA,kIAG1BA,gFAGAA,oFAEoBA,4KAGlBA,yLAOJA,qFAG8CA,4GAEdA,2EAILA,kHAAkE,0DAczEA,kHACCA,0GA8BKA,wGA4BIA,0LAmClCA,oCAAsB,YAE6IA,kHAASA,yCAAc,GAAGA,2BAC3LA,mCAAIA,sBAAuBA,yEADFA,yGAAyD,wDAC9EA,mJAYAA,sCAAuBA,sBAAeA,2BACtCA,uDADuBA,iEACYA,kGAAmBA,kDAAsBA,+FAXlFA,uCAA+C,yBAS3CA,+CAIFA,uEAXEA,yEAAuB,eAAvBA,CAAuB,aAAvBA,CAAuB,uBAAvBA,CAAuB,YAAvBA,CAAuB,0CAoBvBA,uCACEA,kCACFA,sEAD2BA,yLAM3BA,wCACEA,uDACFA,sEADEA,uJAEFA,wCACEA,uCACFA,4FAjBNA,uCAAoE,WAApEA,CAAoE,WAApEA,CAAoE,YAGmGA,kHAASA,8CAAmB,GAAGA,2BAChMA,mCAAIA,yCAAgBA,2BACpBA,0CAGAA,mCACEA,sBACFA,2BACAA,qCAAMA,uBAAgCA,2BACtCA,4CAGAA,4CAGAA,sCAAiG,aAE7FA,mCAAgFA,+CAClFA,+EAlBuBA,yGAAyD,wDAE9DA,4HAIlBA,2FAEIA,uFAC6DA,6HAGAA,+HAGhEA,4LCnLF,IAAMG,GAAoB,MAA3B,MAAOA,EAmBX1B,YAAoB2B,EAAwCC,EACxCC,EAAiDC,GADjD5B,sBAAwCA,iBACxCA,uBAAiDA,oBAnBrEA,mBAAgB,OAChBA,aAAe,GACfA,eAAW,EAIXA,sBAAkB,EAGlBA,wBAAoB,EAGpBA,kBAAe,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,UAI1DA,iBAAc,QAIZA,KAAK6B,YAAc7B,KAAK4B,aAAaxB,WAAa,QAAU,OAC9D,CAEA0B,WACE9B,KAAKyB,eAAeM,iBAAiBC,KAAMC,IACzCjC,KAAKkC,QAAUD,EAASC,QAExBlC,KAAKmC,8CAELnC,KAAKoC,QAAUpC,KAAK0B,UAAUW,+BAA+BrC,KAAKkC,QAAQI,UAC1EtC,KAAKuC,sBAELvC,KAAKyB,eAAee,0BAA0BR,KAAMS,IAClDzC,KAAK0C,iBAAkB,EACvB1C,KAAK2C,eAAiBF,EAAcG,MACpC5C,KAAK6C,UAAW,IACfC,MAAMC,IACP/C,KAAK0C,iBAAkB,EACvB1C,KAAK6C,UAAW,EAChBG,QAAQC,IAAIF,EAAa,EAE1B,GACAD,MAAMI,IACPlD,KAAK6C,UAAW,EAChBG,QAAQC,IAAIjD,KAAKmD,cACjBnD,KAAKmD,aAAe,oDAAgDD,GAExE,CAGAE,gBACE3C,OAAO4C,SAASC,KAAO,kBAAoBtD,KAAKkC,QAAQqB,eAAeC,QACzE,CAEAC,kBACEhD,OAAO4C,SAASC,KAAOtD,KAAKkC,QAAQI,QACtC,CAEQH,8CAGN,GAFAnC,KAAK0D,WAAa1D,KAAKkC,QAAQyB,YAE3B3D,KAAKkC,QAAQ0B,cAAqD,IAArC5D,KAAKkC,QAAQ0B,aAAaC,OAEzD,YADA7D,KAAK8D,iBAAmB,WAI1B9D,KAAK8D,iBAAmB9D,KAAK+D,aAAa/D,KAAKkC,QAAQ0B,aAAa,GAAGI,aACvE,IAAIC,EAAoB,GAExB,QAAQC,EAAI,EAAGA,EAAIlE,KAAKkC,QAAQ0B,aAAaC,OAAQK,IAAK,CACxD,IAAIC,EAAcnE,KAAKkC,QAAQ0B,aAAaM,GAC5C,IAAIC,EAAYC,SAAU,CACxBH,EAAkBI,KAAK,WACvB,MAEFJ,EAAkBI,KAAKF,EAAYG,gBAAgBC,OAAO,EAAG,GAAM,MAAQJ,EAAYK,kBAAkBD,OAAO,EAAG,IAErHvE,KAAK8D,kBAAoB,IAAMG,EAAkBQ,KAAK,MACxD,CAEAC,eACE1E,KAAK2E,iBAAkB,EACvB3E,KAAKuC,sBACLvC,KAAK2B,gBAAgBd,QACvB,CAEA+D,YAAYC,GACV7E,KAAK2B,gBAAgBxB,UAErB,QAAQ+D,EAAI,EAAGA,EAAIW,EAASX,IAC1BlE,KAAK8E,cAEPC,WAAW,KACT/E,KAAK2E,iBAAkB,GACtB,EAEL,CAEAG,cACE9E,KAAKgF,eAAeX,KAAKrE,KAAKgF,eAAeC,SAC7CjF,KAAKkF,YAAYb,KAAKrE,KAAKkF,YAAYD,QACzC,CAEQ1C,sBACNvC,KAAKgF,eAAiBhF,KAAKkC,QAAQiD,SAASC,IAAIC,KAE5CC,IAAK,8CAAgDD,EAAKE,KAC1DC,OAAQH,EAAKG,UAIjBxF,KAAKkF,YAAclF,KAAKkC,QAAQiD,SAASC,IAAIC,GAAQA,EACvD,CAEAI,oBACEzF,KAAK2B,gBAAgBd,SACrBb,KAAK0F,mBAAqB,IAC5B,CAEAC,wBAAwBC,GACtB5F,KAAK2B,gBAAgBxB,UACrBH,KAAK0F,mBAAqBE,CAC5B,+CA3HWpE,GAAoBH,8LAApBG,EAAoBqE,o5FDXjCxE,2CAqJAA,sCACEA,yCAKAA,yCAeFA,2BAEAA,iDA5KMA,0CAqJDA,0FACGA,mEAKAA,mEAiBFA,i5QCjKOG,CAAoB,+BCNjC,MAAMsE,GAAM,CAAC,KAAK,IACZC,EAAK,MAAX,MAAMA,EACFjG,cACIE,KAAKgG,IAAM,GACXhG,KAAKiG,SAAW,GAChBjG,KAAKkG,KAAM,CACf,CACAC,SACqB,IAAbnG,KAAKkG,MAETlG,KAAKkG,KAAM,EACXlG,KAAKoG,UAAY,IAAIC,KACrBrG,KAAKsG,UACT,CACAA,UACI,KAAOtG,KAAKiG,SAASpC,QACjB7D,KAAKiG,SAAShB,OAAdjF,GAEJ,IAAIuG,GAAQ,IAAIF,KAASrG,KAAKoG,SAC9B,MAAMI,EAAQ,EAAIC,KAAKC,MAAMH,EAAO,KAGpC,IAAII,EAAWC,EAAM1C,EAAG2C,EACxB,IAHAN,EAAO,IAAMA,EAAO,IACpBvG,KAAKoG,UAAY,IAAMI,EAElBtC,EAAI,EAAG2C,EAAM7G,KAAKgG,IAAInC,OAAQK,EAAI2C,EAAK3C,GAAK,EAC7CyC,EAAY3G,KAAKgG,IAAI9B,EAAI,GAErB,IAAMyC,EACN3G,KAAKgG,IAAI9B,GAAGsC,IAKZG,GAAa,EAAIH,EAAQ,EACzBI,EAAOH,KAAKC,MAAMC,EAAY,IAC1BC,EAAO,GACP5G,KAAKgG,IAAI9B,GAAG0C,GAGhB5G,KAAKgG,IAAI9B,EAAI,GAAKyC,EAAY,GAAK,GAGvC3G,KAAKkG,KACLnB,WAAW,IAAM/E,KAAKsG,UAAWC,EAEzC,CACAO,IAAIC,EAAIJ,GACJ3G,KAAKiG,SAAS5B,KAAK,KACfrE,KAAKgG,IAAI3B,KAAK0C,GACd/G,KAAKgG,IAAI3B,KAAmB,MAAdsC,EAAqB,EAAI,GACvC3G,KAAKkG,KAAM,GAEnB,CACAc,OAAOD,GACH/G,KAAKiG,SAAS5B,KAAK,KACf,MAAMH,EAAIlE,KAAKgG,IAAIiB,QAAQF,IACjB,IAAN7C,GACAlE,KAAKgG,IAAIkB,OAAOhD,EAAG,GAEvBlE,KAAKkG,IAAMlG,KAAKgG,IAAInC,OAAS,GAErC,EAEJkC,SAAMxE,UAAO,SAAuB4F,GAAK,OAAO,IAAKA,GAAKpB,EAAU,EACpEA,EAAMqB,WAAsBC,+BAA0B,CAAEC,MAAOvB,EAAOzE,QAASyE,EAAMxE,YA9D/EwE,CAAK,KAmEX,MAAMwB,EACFzH,cACIE,KAAKwH,QAAS,EACdxH,KAAKyH,SAAW,EAChBzH,KAAK0H,SAAW,iCAChB1H,KAAK2H,OAAS,SACd3H,KAAK4H,WAAa,mBAClB5H,KAAK6H,MAAQ,CAAC,IAAK,IAAK,EAAG,IAAK,GAAI,EAAG,IAAK,GAAI,EAAG,IAAK,GAAI,EAAG,IAAK,GAAI,EAC5E,EAEJN,EAAgBhG,UAAO,SAAiC4F,GAAK,OAAO,IAAKA,GAAKI,EAAoB,EAClGA,EAAgBH,WAAsBC,+BAA0B,CAAEC,MAAOC,EAAiBjG,QAASiG,EAAgBhG,UAAMuG,WAAY,SACrIP,EAAgBQ,mBAAkBC,oBAAiB,CAAE1G,QAAS,WAAqC,OAAO,IAAIiG,CAAmB,EAAGD,MAAOC,EAAiBO,WAAY,SAIxH,IAE1CG,EAAkB,MAAxB,MAAMA,EACFnI,YAAYoI,EAAIC,EAAOC,GACnBpI,KAAKkI,GAAKA,EACVlI,KAAKmI,MAAQA,EACbnI,KAAKoI,IAAMA,EACXpI,KAAK2G,UAAY,IACjB3G,KAAKqI,QAAU,CAAC,EAChBrI,KAAKsI,MAAQ,GACbtI,KAAKuI,KAAO,EACZvI,KAAKwI,QAAS,EAEdxI,KAAKyI,QAAS,EACdzI,KAAKmG,MAAQ,IAAIuC,eACjB1I,KAAK2I,SAAW,IAAID,eACpB1I,KAAK4I,OAAS,IAAIF,eAClB1I,KAAK6I,MAAQ,IAAIH,cACrB,CAEAI,QACI9I,KAAKwI,QAAS,EACdxI,KAAKmG,MAAM4C,OACX/I,KAAKgJ,UAAU,QACnB,CAEAC,UACSjJ,KAAKyI,QACNzI,KAAKkJ,UACTlJ,KAAKmJ,OACLnJ,KAAKgJ,UAAU,UACnB,CAEAI,OACQpJ,KAAKyI,SAETzI,KAAKyI,QAAS,EACdzI,KAAKkJ,UACLlJ,KAAKgJ,UAAU,QACnB,CAEAK,QACQrJ,KAAKyI,QAAUzI,KAAKwI,SAExBxI,KAAKwI,QAAS,EACdxI,KAAKgJ,UAAU,SACnB,CAEAM,SACQtJ,KAAKyI,SAAWzI,KAAKwI,SAEzBxI,KAAKwI,QAAS,EACdxI,KAAKgJ,UAAU,UACnB,CACAA,UAAUO,GACNvJ,KAAK6I,MAAME,KAAK,CAAEQ,SAAQhB,KAAMvI,KAAKuI,MACzC,CACAY,OACI,MAAMK,EAAKxJ,KACXwJ,EAAGC,OAASC,OAAOC,OAAO,CAAC,EAAG,IAAIpC,EAAmBiC,EAAGpB,IAAKoB,EAAGC,QAChE,MAAMvB,EAAKsB,EAAGtB,GAAG0B,cACjBJ,EAAGhB,OAASgB,EAAGC,OAAOjC,OACtBgC,EAAGf,QAAS,EAEZ,MAAMoB,EAAO3B,EAAG4B,WAAaN,EAAGC,OAAO/B,SACvC8B,EAAGC,OAAO7B,WAAWmC,UAAY,EACjC7B,EAAG4B,UAAYD,EAAKG,QAAQR,EAAGC,OAAO7B,WAAY,CAACqC,EAAK/I,MAEvC,MAATA,GAAyB,UAATA,KAChBsI,EAAG7C,UAAY,KAEnB,IAAIuD,EAAU,GACd,MAAa,UAAThJ,GACAsI,EAAGlB,MAAMjE,KAAK,CAAEnD,KAAM,MACtBsI,EAAGlB,MAAMjE,KAAK,CAAEnD,KAAM,MACtBgJ,EACIV,EAAGW,KAAK,GAAI,IAAK,WACbX,EAAGW,KAAK,IAAK,GAAI,WACjBX,EAAGW,KAAK,GAAI,IAAK,YAGzBX,EAAGlB,MAAMjE,KAAK,CAAEnD,KAAMA,IAEnBsI,EAAGW,KAAKD,EAAShJ,EAAM,OAAM,GAExC,MAAM2G,EAAQ2B,EAAGC,OAAO5B,MACxB2B,EAAGlB,MAAM8B,QAASC,IACd,MAAMnJ,EAAOmJ,EAAKnJ,KAClB,IAAgBgD,EAAZoG,EAAO,IAGX,IAFAD,EAAKE,KAAOrC,EAAGsC,cAAc,SAAStJ,KAEjCgD,EAAI2D,EAAMhE,OAAS,EAAGK,GAAI,GACvBhD,IAAS2G,EAAM3D,GADYA,GAAK,EAIpCoG,GAAQzC,EAAM3D,EAAI,GAEtBmG,EAAKC,KAAOA,EACZD,EAAKI,MAAQ5C,EAAM3D,EAAI,GACvBmG,EAAKK,KAAO7C,EAAM3D,EAAI,KAE1BsF,EAAGmB,UACHnB,EAAGoB,OAAO,GAAG,GAEb,MAAMC,EAAUrB,EAAGoB,OACnBpB,SAAGoB,OAAS,CAACpE,EAAQ,IACVqE,EAAQC,MAAMtB,EAAI,CAAChD,IAG1BgD,EAAGC,OAAOb,QACVY,EAAGC,OAAOb,OAAOwB,QAASW,IACtB,GAAIA,EAAO,EACP,MAAM,IAAIC,MAAM,iDACpBD,GAAc,IAEdvB,EAAGnB,QADH0C,GAAeA,EAAOvB,EAAG7C,YACN,IAG3B6C,EAAGrB,MAAMrB,IAAI0C,EAAGoB,OAAQpB,EAAG7C,WAE3BuB,EAAG3H,MAAM0K,QAAU,SACnBjL,KAAKmI,MAAMhC,QACJqD,CACX,CACAN,UACI,YAAKf,MAAMnB,OAAOhH,KAAK4K,QAChB5K,IACX,CAIA4K,OAAOpE,EAAQ,EAAG0E,GAAQ,GACtB,MAAM1B,EAAKxJ,MACNkL,IAAU1B,EAAGhB,QAAUgB,EAAGf,UAE/Be,EAAGjB,KAAOiB,EAAGjB,KAAOiB,EAAG7C,UAAYH,EACnCgD,EAAGlB,MAAM8B,QAASC,IACdA,EAAKc,UAAYd,EAAKe,MACtBf,EAAKe,MAAQ3E,KAAKC,MAAM8C,EAAGjB,KAAO8B,EAAKC,MAAQD,EAAKI,QAExDjB,EAAG6B,UACC7B,EAAGnB,QAAQmB,EAAGjB,QACdiB,EAAGZ,OAAOG,KAAKS,EAAGjB,MAClBiB,EAAGR,UAAU,WAEbQ,EAAGjB,KAAO,IACViB,EAAGb,SAASI,KAAK,GACjBS,EAAGf,QAAS,EACZe,EAAGR,UAAU,YACbQ,EAAGN,WAEX,CAIAmC,UACI,MAAM7B,EAAKxJ,KACX,GAAIwJ,EAAGC,OAAO4B,QAEV,YADA7B,EAAGC,OAAO4B,QAAQP,MAAMtB,GAG5B,IAAIU,EACJV,EAAGlB,MAAM8B,QAASC,IACVA,EAAKc,YAAcd,EAAKe,QACxBlB,EAAU,GACVV,EAAG8B,WAAWjB,EAAKe,MAAOf,EAAKK,MAAMN,QAASmB,IAC1CrB,GAAWV,EAAGW,KAAKoB,EAAQC,WAAY,GAAI,UAAS,GAExDnB,EAAKE,KAAKT,UAAYI,IAGlC,CAIAS,UACI,MAAMnB,EAAKxJ,KACX,IAAIuI,EAA4B,IAArBiB,EAAGC,OAAOhC,SACrB,MAAMgE,EAAMjC,EAAGC,OAAOiC,UACjBnD,GAAQkD,IACTlD,EAAOkD,GAAM,IAAIpF,MAAOsF,WAC5BnC,EAAGjB,KAAOA,EAAQA,EAAOiB,EAAG7C,SAChC,CAIAwD,KAAKyB,EAAKC,EAAW3K,GACjB,OAAQA,OACC,WACA,UACD2K,EAAY3K,EAAO,SAAW2K,EAC9B,UACC,UAEGA,EADQ,MAARD,EACY1K,EAAO,IAAMA,EAAO,UAAY2K,EAGhC3K,EAAO,IAAMA,EAAO,IAAM0K,EAAM,IAAMC,EAI9D,MAAO,gBAAkBA,EAAY,KAAOD,EAAM,SACtD,CAIAN,WAAWF,EAAOV,GACdU,EAAQA,EAAQ,EAAI,EAAIA,EACxB,MAAMU,EAAW,GAEjB,KAAOpB,KACHoB,EAASpB,GAAQU,EAAQ,GACzBA,EAAQ3E,KAAKC,MAAM0E,EAAQ,IAE/B,OAAOU,CACX,CACAhK,WACI9B,KAAKmJ,OACAnJ,KAAKyJ,OAAOjC,QACbxH,KAAK8I,OACb,CACAiD,cACI/L,KAAKkJ,SACT,CACA8C,YAAYC,GACHA,EAAQxC,OAAOyC,aAChBlM,KAAKiJ,SAEb,EAEJhB,SAAmB1G,UAAO,SAAoC4F,GAAK,OAAO,IAAKA,GAAKc,GAAoBZ,8BAAyBA,cAAoBA,8BAAyBtB,GAAQsB,8BAAyBE,GAAmB,EAClOU,EAAmBkE,UAAqB9E,8BAAyB,CAAEnG,KAAM+G,EAAoBpC,UAAW,CAAC,CAAC,cAAeuG,SAAU,EAAGC,aAAc,SAAyCC,EAAIC,GAAgB,EAALD,GACpMjF,wBAAmB,cAAc,EACnC,EAAGmF,OAAQ,CAAE/C,OAAQ,UAAYgD,QAAS,CAAEtG,MAAO,QAASwC,SAAU,WAAYC,OAAQ,SAAUC,MAAO,SAAW6D,SAAU,CAACrF,kCAA8BsF,mBAAoB7G,GAAK8G,MAAO,EAAGC,KAAM,EAAGnF,SAAU,SAAqC4E,EAAIC,GAAgB,EAALD,IACvQjF,8BACAA,yBAAoB,GACtB,EAAGyF,OAAQ,CAAC,8DAA+DC,cAAe,EAAGC,gBAAiB,IA1O9G/E,CAAkB,KAyQlBgF,GAAe,MAArB,MAAMA,GAENA,SAAgB1L,UAAO,SAAiC4F,GAAK,OAAO,IAAKA,GAAK8F,EAAoB,EAClGA,EAAgBC,UAAqB7F,6BAAwB,CAAEnG,KAAM+L,IACrEA,EAAgBE,UAAqB9F,6BAAwB,CAAE+F,UAAW,CAACrH,GAAQsH,QAAS,CAACC,kBAJvFL,CAAe,+CCvVjB5L,kCAA2BA,sBAAmBA,qEAAnBA,qGAIzBA,mCAAoCA,sBAAwBA,6FAAxBA,2GAEpCA,sCAA8D,OACzDA,sBAAQA,uDAARA,qFAIHA,qCACEA,sBACFA,qDADEA,8FATNA,sCACEA,wCACAA,kCAAIA,sBAAmBA,2BACvBA,yCAIAA,sCACEA,wCAGFA,qFAVKA,gFACDA,4EACiBA,oEAKEA,+FAQxBA,kCACEA,sBACFA,qDADEA,oECxBA,IAAMkM,GAAgB,MAAvB,MAAOA,UAAwBC,KAEnC1N,YAAqB2B,GACnBgM,QADmBzN,sBADrBA,WAAc,EAGd,CAEA8B,WACE9B,KAAKyB,eAAeiM,gBAAgB1L,KAAM2L,IACxC3N,KAAK2N,MAAQA,GAEjB,+CAVWJ,GAAelM,qFAAfkM,EAAe1H,2iBDT5BxE,sCAA2B,UAA3BA,CAA2B,UAGrBA,iCACFA,2BACAA,yCAAuDA,iDAASkL,cAAY,GAC1ElL,uCAAyBA,6BAAOA,+BAGpCA,wCAA6B,QAEvBA,uBAAgBA,2BAEpBA,wCAGAA,yCAeDA,uCACEA,yCAGFA,sCAxBKA,wEAEAA,0EAGkBA,0EAgBAA,2PCvBbkM,CAAgB,4HCPzBlM,wCAAyFA,sCAAUA,mDACnGA,wCAA+FA,wCAAYA,0FAC3GA,wCAA0CA,kCAC4CA,uDAAqDA,qEADzEA,kGAAkD,yDAC9BA,gKAKxFA,uCAAiD,UAE3CA,kCACFA,2BACAA,uCAA8B,SAA9BA,CAA8B,aAGxBA,sBACFA,6BAEFA,wCACIA,sBACFA,2BACFA,qCACEA,mCACAA,sCACEA,qDACFA,2EAf0BA,kIAKxBA,gFAIAA,oFAEgCA,4KAGhCA,iOAMVA,uCACEA,kCACM,OADNA,CACM,QACNA,qCAAMA,sCAAaA,wDAGrBA,uCAA0D,aAC/BA,sBAAkBA,uEAAlBA,oGAUvBA,qCACGA,sBACHA,sEADGA,8LARPA,uCAA+E,cAA/EA,CAA+E,aAElDA,6BAACA,6BAE5BA,qCACEA,6DAEAA,0CAGFA,uEAHUA,sLASRA,oCACEA,8CACFA,gFADoBA,uFAFtBA,oCACEA,yCAGFA,6GAHUA,qHAMRA,oCACEA,sDACFA,gFAD4BA,uFAF9BA,oCACEA,yCAGFA,6GAHUA,qHAMRA,uCACEA,gDACFA,8GAFMA,mEACgBA,4DAAiB,gDAFzCA,oCACEA,0CAGFA,6GAH6DA,qHAM3DA,oCACEA,kDACFA,gFADwBA,uFAF1BA,oCACEA,yCAGFA,6GAHWA,qHApBbA,uCACEA,yCAMAA,yCAMAA,yCAMAA,yCAKFA,mFAvBQA,mHAMAA,kHAMAA,qHAMAA,oHAiBEA,kCAAqCA,oDAAqBA,mDAC1DA,kCAA0CA,kEAAmCA,sDAK/EA,uCACEA,sBACFA,sEAF2GA,mFACzGA,gHAEFA,uCACEA,sBACFA,sEAF8FA,oFAC5FA,6GAiCAA,kCAAoCA,iDAAkBA,sDA2BxDA,uCACEA,sBACFA,sEAFmIA,yFACjIA,sHAEFA,uCACEA,sBACFA,sEAF4GA,yFAC1GA,4JAcNA,qCAAYA,mHAASA,2CAAgB,GAAmCA,4CAAeA,4PAlG3FA,oCAAqC,WAArCA,CAAqC,QAE7BA,6CAAoBA,2BACxBA,8DACAA,2CAAiHA,qHAAYA,qCAAU,GACrIA,uCAA8B,iBACmBA,2HAAaA,kDAA6B,GAAzFA,2BAGAA,wCACEA,wCACAA,wCACFA,6BAEFA,wCAAyC,gBAC6HA,+CAAiBA,2BACrLA,2CAGAA,2CAGFA,+BAGJA,wCAAyC,YAAzCA,CAAyC,YAAzCA,CAAyC,YAG5BA,sCAAYA,2BAEnBA,wCAA8C,SAA9CA,CAA8C,YAA9CA,CAA8C,YAA9CA,CAA8C,WAA9CA,CAA8C,cAIYA,yDAAqCA,yCAOnGA,oCAAIA,6EAAmDA,2BAGvDA,4CAA4IA,qHAAYA,gDAAqB,GAC3KA,wCAA8B,eAE1BA,4CACFA,2BACAA,6CAAyDA,2HAAaA,8CAAyB,GAA/FA,2BAGAA,wCACEA,wCACFA,6BAEFA,wCAA8B,eAE1BA,0DACFA,2BACAA,wDAA+CA,2HAAaA,wDAAmC,GAE/FA,6BAEFA,wCAA8B,YAE1BA,iDACFA,2BACAA,wCAA4C,eACsCA,2HAAaA,gDAA2B,GAAxHA,2BACAA,0CAA+CA,mCAASA,2BACxDA,0CAAiFA,2HAAaA,gDAA2B,GAAzHA,2BACAA,0CAA0CA,kCAAQA,2BAClDA,wCACEA,oDACFA,+BAGJA,wCAAyC,gBACyIA,2CAAaA,2BAC7LA,2CAGAA,2CAGFA,+BAGJA,wCAAwD,aAEtDA,uBACFA,2BACAA,qDAEAA,wCAAyD,mBAC8BA,qHAAYA,uCAAY,GAAEA,2BAC/GA,+BACAA,yCACFA,iMAlGKA,uFAGGA,sHAEKA,yFAAuC,mCAIxCA,8FACAA,mGAIsCA,qGAAqD,sFAC9CA,mGAGAA,0FAMpDA,mFAS2DA,wHAUxDA,sHAKeA,yFAAuC,+BAIlDA,8FAOyCA,yFASmCA,iFAECA,iFAQ3BA,qGAAqD,sFAC1DA,yGAGAA,+FAMpDA,qFACoCA,yFACvCA,mFAEqBA,4DAESA,0EACMA,6FAEKA,kJAM7CA,uCAAwBA,kHAASA,sCAAW,GAC1CA,4CACFA,mDACAA,uCACEA,4CACFA,4BCxKK,IAAMuM,EAAgB,MAAvB,MAAOA,EAmCX9N,YAAoB+N,EAA+BpM,EAC/BqM,EACAC,EAAgCC,EAAwBC,EACxDC,GAHAlO,aAA+BA,sBAC/BA,qBACAA,UAAgCA,cAAwBA,uBACxDA,4BAlCXA,gBAAY,EACrBA,iBAAa,EACbA,eAAW,EAGXA,aAAe,GAEfA,cAA6BmO,UAC7BnO,gBAA+BmO,WAC/BnO,eAA8BmO,UAG9BnO,kBAAoB,GASpBA,6BAAkC,EAClCA,oBAAgB,EAChBA,uBAAmB,EAEnBA,UAAO,oBACPA,6BAA0B,GAC1BA,oBAAgB,EAChBA,YAAS,GAOPA,KAAKoO,KAAO,CAAC,IAAK,KAAM,KAAM,IAAK,IAAK,KAAM,IAAK,KAAM,KAAM,KAAM,KAAM,IAAK,KAAM,KAAM,KAAM,KACpG,CAEAC,kBACEtJ,WAAW,KACL/E,KAAKsO,SACPtO,KAAKsO,QAAQlF,OAEXpJ,KAAKuO,kBACPvO,KAAKwO,sBAAwBC,YAAmB,CAC9CC,aAAc1O,KAAKuO,gBAAgB3E,cACnCwE,KAAMpO,KAAKoO,OACZ,EAGP,CAEAtM,WACE,GAAG9B,KAAK2O,UAAW,OAEnB,IAAIC,EAAe5O,KAAK6N,MAAMgB,SAASC,OAAOxH,MAC1CyH,EAAW/O,KAAK6N,MAAMgB,SAASC,OAAOE,OACvChP,KAAK6N,MAAMgB,SAASC,OAAOG,IAC5BjP,KAAKkP,OAASlP,KAAK6N,MAAMgB,SAASC,OAAOG,GAEvCL,EAGF5O,KAAKmP,yBAAyBP,EAAcG,GAF5C/O,KAAKoP,WAAWpP,KAAK6N,MAAMgB,SAASC,OAAOO,UAAWN,EAG1D,CAEAO,aAAaC,EAAcrN,GACzBlC,KAAKuP,QAAUA,EACfvP,KAAKkC,QAAUA,EACflC,KAAK6C,UAAW,EAChB7C,KAAKwP,YAAa,EAClBxP,KAAKuP,QAAQE,QAAQrF,QAAU4E,IAC7BA,EAAOzJ,KAAOvF,KAAKgO,OAAO1I,IACtB0J,EAAOzJ,KAAKmK,SAASV,EAAOW,MAC9BX,EAAOzJ,KAASyJ,EAAOzJ,KAAS,IAAMyJ,EAAOW,IAC/CX,EAAOY,MAAMC,QAAWb,EAAOY,MAAMC,QAAQC,KAAK,CAACC,EAASC,IAAaD,EAAQE,cAAgBD,EAAQC,cAAiB,GAAI,EAAE,EAEpI,CAEQb,WAAWC,EAAWN,GAC1B/O,KAAKqP,UAAYA,GAAarP,KAAKkO,qBAAqBgC,SAAS,aAAa,GAE9ElQ,KAAKyB,eAAe0O,eAAenQ,KAAKqP,WAAWrN,KAAMC,IACvDjC,KAAKsP,aAAarN,EAASsN,QAAStN,EAASC,SAC7ClC,KAAKoQ,WAAarB,IACnBjM,MAAMuN,IACPrQ,KAAKwP,YAAa,EAClBxP,KAAK6C,UAAW,EACbwN,EAASnO,SACVlC,KAAKkC,QAAUmO,EAASnO,QACxBlC,KAAKsQ,eAAgB,GAGrBtQ,KAAKuQ,eAAiB,kDAAoDF,GAEhF,CACQlB,yBAAyB7H,EAAOyH,GACtC/O,KAAKyB,eAAe+O,iBAAiBlJ,GAAOtF,KAAKyO,IAC/CzQ,KAAKkO,qBAAqBwC,gBAAgB,YAAaD,EAAQd,IAC/D3P,KAAKkO,qBAAqBwC,gBAAgBD,EAAQd,GAAKZ,GACvD/O,KAAKoQ,WAAarB,EAClB/O,KAAKoP,WAAWqB,EAAQd,GAAIZ,GAC5B/O,KAAKkP,OAAS,WACbpM,MAAMuN,IACPrQ,KAAKwP,YAAa,EAClBxP,KAAK6C,UAAW,EAChB7C,KAAKuQ,eAAiBF,GAE1B,CAEAM,YAEE,MAAMC,EAAuB5Q,KAAK8N,cAAc+C,KAAK,CACnDC,MAAO,KACP5G,QAASqD,GACTwD,SAAU,IACVC,MAAOvQ,OAAOwQ,WAAa,IAAM,IAAMxQ,OAAOwQ,WAC9CC,UAAWzQ,OAAO0Q,YAAc,IAChCC,SAAU,YAIZC,2BAAsCT,EAExC,CAEAU,kBAEA,CAEAC,WAEE,GADAvR,KAAKwR,UAAW,GACVxR,KAAKyR,IAAIC,MAIb,OAHA1R,KAAK2R,kBAAoB,0EACzBlR,OAAOK,SAAS,EAAG,QACnBd,KAAKwR,UAAW,GAGlBxR,KAAKuQ,eAAiB,KACtBvQ,KAAK2R,kBAAoB,KACzB3R,KAAK4R,mBAAqB,KAE1B5R,KAAKyB,eAAeoQ,0BAA0B7R,KAAK8R,OADvC9R,KAAK+R,aAC+CC,WAAWhQ,KAAMC,IAC/Ee,QAAQC,IAAIhB,GACZ,IAAIwO,EAAUxO,EAASsN,QAEvB,GAAGkB,GAID,GAHAzQ,KAAKwR,UAAW,EAChBxR,KAAKyQ,QAAUA,EAEXzQ,KAAKyQ,QAAQd,GAAK,CACpB3P,KAAKkO,qBAAqBwC,gBAAgB,YAAaD,EAAQd,IAC/D3P,KAAKiS,iBACL,MAAM3M,EAAM,eAAgB,IAAIe,MAAOsF,UAEvClL,OAAO4C,SAASC,KAAOgC,QAGzBtF,KAAKwR,UAAW,EAChBxR,KAAKkS,iBAAe,GAErBpP,MAAOuN,IAERrQ,KAAKwR,UAAW,EAChBxR,KAAK4R,mBAAqBvB,GAE9B,CAEAyB,OAAOK,GACL,OAAOA,EAAInI,QAAQ,OAAQ,GAC7B,CACAoI,aACE,YAAKC,wBAAyB,EAC9BrS,KAAKsS,gBAAkB,KACvBtS,KAAKsO,QAAQ7E,OAAS,CACpBhC,SAAU,GACVD,QAAQ,IAEH,CACT,CAEA+K,iBACE,YAAKC,SAAU,EACfxS,KAAKqS,wBAAyB,GACvB,CACT,CAEAJ,iBACEjS,KAAKyS,eAAgB,CACvB,CAEAC,sBACE1S,KAAK2S,eAAiB,EAEtB3S,KAAKiO,gBAAgB2E,gBAAgB,CACnCnC,QAAS,CACPoC,KAAM7S,KAAK+R,aAAac,KACxBb,SAAUhS,KAAK8R,OAAO9R,KAAK+R,aAAaC,UACxCc,OAAQ9S,KAAK+R,aAAae,OAC1BC,eAAgB/S,KAAK+R,aAAagB,kBAEnC/Q,KAAMgR,IACPhT,KAAK2S,eAAgB,EACrB3P,QAAQC,IAAI,YAEZD,QAAQC,IAAI+P,GACZhT,KAAKkO,qBAAqBwC,gBAAgB,YAAasC,EAAKrD,IAE5D,MAAMrK,EAAM,wBAAyB,IAAIe,MAAOsF,UAEhDlL,OAAO4C,SAASC,KAAOgC,IACtBxC,MAAMmQ,IACPjT,KAAK2S,eAAgB,GAEzB,CAEAT,kBACElS,KAAKkT,KAAO,eACd,CAEAC,eAEA,+CAlOWvF,GAAgBvM,4SAAhBuM,EAAgB/H,iJAGhBoC,EAAkB,g0GD1B/B5G,sCAA2B,WAEvBA,0CACAA,0CACAA,0CAEFA,6BAGFA,sCACEA,0CAsBAA,yCAMAA,yCAIAA,yCAcAA,0CA0BAA,4CAsGFA,2BACAA,wCACEA,2CAGAA,2CAGFA,2BACAA,wCACEA,mCACFA,kCAjM6BA,gJACAA,oJACAA,mEAMOA,kEAsBTA,qEAMAA,mFAI2BA,4EAc5BA,6EA0BlBA,kFAwG0CA,mEAGrBA,qEAKRA,+gGC3KRuM,CAAgB,4BCTjBvM,uCACEA,kCAEAA,mCAAIA,iCAAQA,2BACZA,qCAAoCA,qEAAwCA,wDAK9EA,uCACEA,kCAEAA,mCAAIA,sBAA2BA,2BAC/BA,qCAAoCA,iEAAoCA,qCAA6BA,oCAAWA,2BAAKA,iDAAoBA,uEADrIA,4FACuEA,oGAK7EA,uCACEA,kCAEAA,mCAAIA,8BAAKA,2BACTA,qCAAoCA,sBAA0DA,uEAA1DA,6HC1B3C,IAAM+R,GAAkB,MAAzB,MAAOA,EAOXtT,YAAoB2B,EAAwC4R,EACxCnF,GADAlO,sBAAwCA,sBACxCA,4BAPpBA,kBAAc,EAIdA,gBAAY,EAKVA,KAAK+O,SADa/O,KAAKqT,eAAexE,SAASC,OACxBC,QACzB,CAEAjN,WAEE9B,KAAKyB,eAAe+O,iBAAiBxQ,KAAK+O,UAAU/M,KAAKyO,IACvDzQ,KAAKsT,aAAc,EACnBtT,KAAKuT,WAAY,EACjBvT,KAAKyQ,QAAWA,EAEhBzQ,KAAKkO,qBAAqBwC,gBAAgB,YAAaD,EAAQd,GAAE,GAChE7M,MAAMuN,IACPrQ,KAAKsT,aAAc,EACnBtT,KAAKuT,WAAY,EACjBvT,KAAKmD,aAAekN,GAExB,+CA1BW+C,GAAkB/R,4JAAlB+R,EAAkBvN,iiBDV/BxE,sCAAqC,UAArCA,CAAqC,UAArCA,CAAqC,UAArCA,CAAqC,UAArCA,CAAqC,UAArCA,CAAqC,UAArCA,CAAqC,QAArCA,CAAqC,UAUfA,iCAA0DA,+BAIpEA,0CASAA,0CASAA,0CAWFA,4CA7BiCA,uEASCA,oFASDA,oHCtB9B+R,CAAkB,6CCDzBI,GAAkB,CACtB,CACEC,KAAO,GACP9E,UCLmC,MAAjC,MAAO+E,EAEX5T,cAAgB,CAEhBgC,WACA,+CALW4R,EAA0B,kDAA1BA,EAA0B7N,mFCPvCxE,gFDOaqS,CAA0B,KDMnCC,SAAU,CACR,CAAEF,KAAM,UAAW9E,UAAWf,GAC9B,CAAE6F,KAAM,WAAY9E,UAAWf,GAC/B,CAAE6F,KAAM,wBAAyB9E,UAAWf,GAC5C,CAAE6F,KAAM,iCAAkC9E,UGNf,MAA3B,MAAOiF,EAIX9T,YAAoB+N,EAAgCpM,GAAhCzB,aAAgCA,sBAFpDA,aAAe,EAEuE,CAEtF8B,WAIE9B,KAAKyB,eAAeoQ,0BAFP7R,KAAK6N,MAAMgB,SAASC,OAAO+E,OAD5B7T,KAAK6N,MAAMgB,SAASC,OAAOxH,OAGsBtF,KAAMC,IAC9DA,EAASsN,UACVvP,KAAK8T,YAAYxE,aAAarN,EAASsN,QAAWtN,EAASC,SAC3DlC,KAAK8T,YAAYX,eAAY,EAKnC,+CAlBWS,GAAoBvS,yHAApBuS,EAAoB/N,oRCXjCxE,gDAA0BA,2DDWbuS,CAAoB,MHO3B,CAAEH,KAAM,qBAAsBM,YAAa,CAACC,KAAmBrF,UAAWf,GAC1E,CAAE6F,KAAM,6BAA8BM,YAAa,CAACC,KAAmBrF,UAAWf,GAClF,CAAE6F,KAAM,sBAAuB9E,UAAWyE,IAC1C,CAAEK,KAAM,GAAI9E,UAAYnN,OASvB,IAAMyS,GAAwB,MAA/B,MAAOA,kDAAwB,iDAAxBA,uDAHDC,cAAsBV,IACtBU,QAECD,CAAwB,kEKhBzB5S,uCACEA,kCAEAA,mCAAIA,iCAAQA,2BACZA,qCAAoCA,qEAAwCA,wDAK9EA,uCACEA,kCAEAA,mCAAIA,sBAA2BA,2BAC/BA,qCAAoCA,iEAAoCA,qCAAmBA,oCAAWA,2BAAKA,iDAAoBA,uEAD3HA,uHAMNA,uCACEA,kCAEAA,mCAAIA,8BAAKA,2BACTA,qCAAoCA,sBAA0DA,uEAA1DA,6HC7B3C,IAAM8S,GAAyB,MAAhC,MAAOA,EAWXrU,cAVAE,kBAAc,EAQdA,gBAAY,CAGZ,CAEA8B,WACA,+CAfWqS,EAAyB,kDAAzBA,EAAyBtO,qkBDPtCxE,sCAAqC,UAArCA,CAAqC,UAArCA,CAAqC,UAArCA,CAAqC,UAArCA,CAAqC,UAArCA,CAAqC,UAArCA,CAAqC,QAArCA,CAAqC,UAUfA,iCAA0DA,+BAIpEA,0CASAA,0CASAA,0CAWFA,4CA7BiCA,uEASCA,oFASDA,+GCzB9B8S,CAAyB,2ECoB9B9S,kCAAuCA,sDAAoBA,sDAM7DA,uCACEA,sBACFA,sEAFuGA,mFACrGA,gHAEFA,uCACEA,sBACFA,sEAF6EA,oFAC3EA,gQArCRA,sCAA0C,UAA1CA,CAA0C,UAA1CA,CAA0C,WAG7BA,qCAAYA,2BAEnBA,sCAA8C,QAA9CA,CAA8C,UAA9CA,CAA8C,UAA9CA,CAA8C,SAA9CA,CAA8C,aAIYA,uBAAgCA,yCAQ9FA,oCAAIA,6CAAgBA,2BAEpBA,+EAEAA,4CAC4CA,qHAAYA,kDAAuB,GAC7EA,wCAA8B,kBACrBA,iKAAPA,2BAEAA,wCACEA,yCACFA,6BAEFA,wCAAyC,gBAEsDA,+CAAiBA,2BAC9GA,2CAGAA,2CAGFA,2BACAA,wCACEA,iHACFA,uIAjC4DA,wFAYxDA,sHAGKA,oEAGDA,8FAIsCA,uGAAwE,yFAEjEA,sFAGAA,6EAIhDA,mHAKTA,6FAAuCA,4CAAmB,8BCjCnD,IAAM+S,GAAwB,MAA/B,MAAOA,EAkBXtU,YAAoB2B,EACAyM,GADAlO,sBACAA,4BAZpBA,YAAS,GAETA,uBAAoB,GACpBA,eAAW,EACXA,kBAAc,EAEdA,gBAAY,EAOVA,KAAKqU,WAAa,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KACnD,CAEAvS,WACA,CAEAuM,kBACEtJ,WAAY,KACV/E,KAAKsU,uBAAyB7F,YAAmB,CAC/CC,aAAc1O,KAAKuU,UAAU3K,cAC7BwE,KAAMpO,KAAKqU,YACZ,EAEL,CAEAG,wBAGE,GAFAxU,KAAKwR,UAAW,GAEVxR,KAAKyU,iBAAiB/C,MAI1B,OAHA1R,KAAK2R,kBAAoB,0EACzBlR,OAAOK,SAAS,EAAG,QACnBd,KAAKwR,UAAW,GAIlBxR,KAAKyB,eAAeiT,aAAa1U,KAAK8R,OAAO9R,KAAKkP,QAChDlP,KAAK2U,OAAO7C,OAAO9R,KAAK2U,OAAO5C,aAAaC,WAAWhQ,KAAMyO,IAC7DzQ,KAAKwR,UAAW,EAChBxR,KAAKyQ,QAAUA,EAEXzQ,KAAKyQ,QAAQd,IACf3P,KAAKkO,qBAAqBwC,gBAAgB,YAAaD,EAAQd,IAC/D3P,KAAK2U,OAAO1C,iBAEZjS,KAAKsS,gBACH,8HAEFtS,KAAK2U,OAAOzC,iBAAe,GAE5BpP,MAAOuN,IACRrQ,KAAKwR,UAAW,EAEhBxR,KAAK4R,mBAAqBvB,GAE9B,CAEAyB,OAAOK,GACL,OAAOA,EAAInI,QAAQ,OAAQ,GAC7B,+CApEWoK,GAAwB/S,wHAAxB+S,EAAwBvO,mvCDZrCxE,2CA6CAA,kEA7CMA,+CA6CkBA,64LCjCX+S,CAAwB,0DCT/B/S,wCAA0CA,8BAAIA,sBAAYA,+EAAZA,sFAYxCA,qCAA0BA,0BAACA,uHAdrCA,sCAA0D,YACjBA,sBACrCA,0CACFA,2BAEAA,sCAAmB,WAEfA,4CACFA,2BACAA,uCAAgC,UAExBA,sBAAoBA,2BAC1BA,wCAA4E,QAExEA,4CACAA,uBAAmBA,6DAdYA,mFACXA,8DAKRA,0DAAa,sBAIvBA,4EACDA,gHAEMA,iEACPA,6IAYZA,0CAAmEA,kHAASA,yCAAc,GAAyBA,iCAAQA,mDAC3HA,0CAA2FA,uCAAWA,4BCrB/F,IAAMuT,EAAsB,MAA7B,MAAOA,EAQX9U,YAAoBmO,0BAPXjO,YAAS,GAElBA,WAAa,GACbA,iBAAa,EACbA,YAAS,EACTA,WAAQ,GACRA,oBAAiB,CACuC,CAExD8B,WACM9B,KAAK6U,MAAMhR,QACb7D,KAAK8U,eAET,CAEAA,gBACE9U,KAAKiO,gBAAgB8G,oBAAoB,KAAM/U,KAAKgP,OAAQhP,KAAKgV,OAAQhV,KAAKiV,OAAO,GAAMjT,KAAOC,IAChGA,EAASmI,QAAS8K,GAAQlV,KAAK6U,MAAMxQ,KAAK6Q,IAC1ClV,KAAKmV,cAAkBlT,EAAS4B,OAAS7D,KAAKiV,OAGlD,CAEAG,eACEpV,KAAKgV,QAAUhV,KAAKiV,MACpBjV,KAAK8U,eACP,+CA3BWF,GAAsBvT,qFAAtBuT,EAAsB/O,qrBDRnCxE,sCACEA,2CAyBFA,2BAEAA,4CACAA,mDA5BwBA,mEA2BqEA,yEACvBA,4WCrBzDuT,CAAsB,2CCmB9BvT,sCAAmD,OAAnDA,CAAmD,SAAnDA,CAAmD,OAGxCA,kFAAgDA,4DAKzDA,qCAAmF,WAE/EA,mDACFA,2BACAA,mCAAM,OACDA,sBAA+BA,uFAHlCA,4HAGGA,+EC/BP,IAAMgU,EAAkC,MAAzC,MAAOA,EAOXvV,YAAoBmO,0BANXjO,YAAe,GACxBA,qBAAuB,GACvBA,aAAS,EACTA,aAAe,GACfA,cAAgB,GAChBA,aAAU,UAC8C,CAExD8B,WAEE,GAAG9B,KAAKgP,OAAOY,MAAM0F,SAEjBtV,KAAKuV,uBAAuBvV,KAAKgP,OAAOY,MAAM0F,cAE3C,CACL,IAAIE,EAAQC,OAAOzV,KAAKgP,OAAOY,MAAM8F,YACjCC,EAAOF,OAAOD,GAAOjP,KAAMkP,SAAW,KAE1CzV,KAAKuV,uBAAuBI,GAG9B3V,KAAK4V,QAAU5V,KAAK6V,SAAS7V,KAAK6V,SAAShS,OAAS,GAEpD7D,KAAK8V,yBACP,CAEAP,uBAAuBI,GAErB,IAAIC,EAAWD,EAAO,GAAOlP,KAAKsP,MAAMJ,EAAO,GAAOA,EAEtD3V,KAAK6V,SAASxR,KAAK,CAAE2R,UAAWJ,EAAU,QACxCxK,MAAO,CAAE4J,OAAQ,EAAGiB,IAAKR,SAAS3O,IAAI8O,EAAS,KAAKM,OAAOlW,KAAKmW,YAE/DR,EAAO,KACR3V,KAAK6V,SAASxR,KAAK,CAAE2R,UAAqB,EAAVJ,EAAc,QAC5CxK,MAAO,CAAE4J,OAAQ,EAAGiB,IAAKR,SAAS3O,IAAc,EAAV8O,EAAa,KAAKM,OAAOlW,KAAKmW,YACtEnW,KAAK6V,SAASxR,KAAK,CAAE2R,UAAqB,EAAVJ,EAAc,QAC5CxK,MAAO,CAAE4J,OAAQ,EAAGiB,IAAKR,SAAS3O,IAAc,EAAV8O,EAAa,KAAKM,OAAOlW,KAAKmW,YAG1E,CAEAC,UAAUC,GAER,OAAGrW,KAAKgP,QAA8C,UAApChP,KAAKgP,OAAOY,MAAM0G,eAAkB,EAE7CC,kBAAeF,EAAQ,QAAS,OAAI,EAGtCG,gBAAa/P,KAAKC,MAAM2P,GAAS,QAC1C,CAEAP,0BACE9V,KAAKiO,gBAAgBwI,uBAAuBzW,KAAKgP,OAAQhP,KAAK4V,QAAQxK,MAAM4J,OAAQhV,KAAK4V,QAAQxK,MAAM6K,KAAKjU,KAAKC,IAC/GjC,KAAK0W,gBAAkBzU,EACvBjC,KAAK2W,QAAS,GAElB,+CA1DWtB,GAAkChU,qFAAlCgU,EAAkCxP,0aDT/CxE,sCACEA,iCAGAA,sCAAoB,UAApBA,CAAoB,WAGNA,iDAAoBA,2BAC9BA,qDAAoCA,wEAAqB,kCACKkL,2BAAyB,GACvFlL,iCAQNA,wCAA4C,UAA5CA,CAA4C,OAA5CA,CAA4C,SAGhCA,iDAAiBA,2BACrBA,oCACEA,uBACFA,+BAGHA,4CAOAA,uCACEA,yCAQFA,oCAnCqCA,qEAAqB,mBAerDA,8FAIGA,8FAQoBA,8PC1BpBgU,CAAkC,iDC4CjChU,oCAAkD,YACRA,sBAAmDA,gHAAnDA,oOAE1CA,oCAAmD,YAEHA,2JAASA,sDAAgC,GACrFA,iCACAA,2CACFA,mCAF6BA,wGAAwD,6KAzB3FA,uCAAyE,WAAzEA,CAAyE,WAAzEA,CAAyE,WAAzEA,CAAyE,UAKcA,sBAAeA,2BAC9FA,qCAEEA,sBACFA,6BAEFA,uCAA8B,YAE1BA,mCACFA,+BAKJA,2CAGAA,2CAQFA,sFAzBqCA,8GAAgDA,+DACxDA,8JAErBA,wFAKyBA,4JAMzBA,wFAGAA,oHAjCdA,uCAA4B,UAA5BA,CAA4B,YAGtBA,sBACFA,+BAKJA,uCAA8B,YAE1BA,4CAkCFA,uEA1CIA,gJAQ8CA,4GA0CpDA,0FAAoBA,oEAQpBA,wGAAiCA,kEAHrCA,sDACEA,kDAKFA,iCANoBA,4CAAsB,sDC5ErC,IAAMuV,EAAiB,MAAxB,MAAOA,EAGX9W,cACKuD,SAASwT,SAAS5P,QAAQ,aAAe,GAC1CjH,KAAK8W,qBAET,CACAA,sBACE/R,WAAa,KACX/E,KAAK+W,KAAKC,UAAU,EAAC,EACpB,EACL,+CAZWJ,EAAiB,kDAAjBA,EAAiBK,uJAAjBL,CAAiB,KAqBjBM,GAAsB,MAA7B,MAAOA,UAA8BN,EAIzC9W,YAAoBkO,GAAkBP,QAAlBzN,cAHXA,YAAc,CAAEqW,OAAQ,GACxBrW,aAAe,EAEsB,CAC9C8B,WACE9B,KAAKmX,iBACP,CAEA7F,gBAAgB8F,EAAapI,GAE3B,IAAIqI,EAAkBrI,EAAOY,MAAM0G,cAAcgB,cAE7CC,EAAQC,OAAO,4BAA4BH,mBAAiCD,EAAOvE,SAEnFtN,EAAOiS,OAAO,mBAAmBxX,KAAKkC,SAASsB,iBAAiB+T,KAErE9W,OAAOoQ,KAAKtL,EACb,CAEQ4R,kBAAqB,CAE7BM,mBAAmBL,EAAapI,GAI/B,OAHkCoI,EAAOnH,cAAgBjB,EAAOqH,QAGzCqB,QAAQ,EAChC,+CA3BWR,GAAqB7V,sFAArB6V,EAAqBrR,y7BD1BlCxE,oCAAgC,QAAIA,sBAA8BA,6BAClEA,sCAAmB,UAAnBA,CAAmB,UAAnBA,CAAmB,UAAnBA,CAAmB,WAMTA,iCACAA,sDACFA,mCASRA,oDAAsD,2BAGlDA,mDAkDFA,2BAEAA,uDACEA,mDAKFA,2BAEAA,0DAQFA,kCAzFGA,8DAAiCA,8GAOCA,wGAAwD,0DACnFA,gHAYYA,mEAAmB,eAAnBA,CAAmB,eAqDnBA,mEAAmB,gBAQyBA,o2GCvDrD6V,CAAsB,gHCjB3B7V,uCAA0F,YAMtFA,kCACAA,uCAAmB,UAAMA,sBAAcA,2DANbA,kIAKFA,uGAAuD,yDACtDA,6IAZ/BA,uCAKEA,0CAUFA,qIAfiBA,2IAKmBA,wFAiBlCA,oCAAkD,YACtBA,sBAAsHA,+GAAtHA,uLAE5BA,oCAAmD,YACvBA,uEAAqCA,wDA7BzEA,oCAAuE,UAAvEA,CAAuE,WAGjEA,yCAgBFA,2BACAA,sCAA4B,aACAA,sBAAqEA,2BAC/FA,oCAAK,YACsBA,sBAAeA,2BACxCA,mCACAA,2CAGAA,2CAGFA,mGAzBqBA,iFAeKA,sHAECA,+DACAA,gIACnBA,wFAGAA,mHAaRA,0FAAoBA,oEAQpBA,wGAAiCA,kEAHrCA,sDACEA,iDAKFA,iCANoBA,4CCtCf,IAAMsW,GAAmB,MAA1B,MAAOA,EAKX7X,YAAoBkO,iBAJXhO,YAAc,CAAEqW,OAAQ,GAGjCrW,sBAAmB,EACmB,CAEtC8B,WACE9B,KAAK4X,gBACP,CAEAC,mBAAmB7C,EAAQ8C,EAAgBC,GACzC,IAAIC,EAAmBvR,KAAKwR,KAAKF,EAAkB,GAC/CG,EAAgBzR,KAAKwR,KAAKF,EAAkBC,GAChDhY,KAAKmY,YAAc,GAEhBH,EAAmB,KACpBA,EAAmB,IAErB,QAAQI,EAAQ,EAAGA,EAAQJ,EAAkBI,IAAS,CACpD,IAAIC,EAAQ,GAEZ,QAASC,EAAS,EAAGA,EAASJ,EAAeI,IAAW,CACtD,IAAIpU,EAAIoU,EAAUF,EAAQF,EACtBK,EAAO,CACTC,OAAQ,EACRC,MAAOzD,EAAS9Q,EAAI,GAIpBqU,EAAKC,OADJtU,EAAI4T,EACS,EACR5T,EAAI6T,EACI,GAEA,EAEhBM,EAAMhU,KAAKkU,GAGb,GAAoB,IAAjBF,EAAMxU,OAAc,CACrB,IAAI0U,EAAO,CACTC,QAAQ,EACRC,MAAO,GAGTJ,EAAMK,QAAQH,GAEdA,EAAO,CACLC,QAAQ,EACRC,MAAO,GAGTJ,EAAMhU,KAAKkU,GAGbvY,KAAKmY,YAAY9T,KAAKgU,GAIxB,OAAOrY,KAAKmY,WACd,CAEQP,iBAGN,GAFA5X,KAAK2Y,iBAAmB,IAEpB3Y,KAAKgP,OAAOY,MAAMC,QAAQhM,OAAQ,OAEtC,IAAI+U,EAAoB5Y,KAAKgP,OAAOY,MAAMC,QAAQ,GAAGI,cACjD4I,EAAoB7Y,KAAKgP,OAAOqH,OAASuC,EAAoBA,EAAoB5Y,KAAKgP,OAAOqH,OAC7FyC,EAAkBD,IAAsBD,EAAoB5Y,KAAKgP,OAAOqH,OAASuC,EAAoB,EACrGG,EAAc,EAClB/Y,KAAK2Y,iBAAiBtU,KAAKrE,KAAK6X,mBAAmBkB,EAAaF,EAAmBD,IAEnFG,EAAcH,EAEd,QAAS1U,EAAI,EAAGA,EAAIlE,KAAKgP,OAAOY,MAAMC,QAAQhM,OAAQK,IACpD0U,EAAoB5Y,KAAKgP,OAAOY,MAAMC,QAAQ3L,GAAG+L,cAAgB8I,EACjEF,EAAoBC,EAAkBF,EAAoBA,EAAoBE,EAC9EA,EAAkBA,EAAkBF,EAAoBE,EAAkBF,EAAoB,EAG9F5Y,KAAK2Y,iBAAiBtU,KAAKrE,KAAK6X,mBAAmBkB,EAAaF,EAAmBD,IACnFG,EAAc/Y,KAAKgP,OAAOY,MAAMC,QAAQ3L,GAAG+L,aAE/C,CACA+I,0BAA0B5B,GACxB,OAAO3Q,KAAKwR,KAAKb,EAAOnH,cAAgB,EAC1C,+CAvFW0H,GAAmBtW,sFAAnBsW,EAAmB9R,6sBDThCxE,oCAAgC,QAAIA,sBAA8BA,6BAClEA,0CAqCAA,iDAA4C,0BAExCA,iDAKFA,2BAEAA,wDAQFA,kCAvDGA,8DAAiCA,8GACXA,4GAsCHA,mEAAqB,eAQIA,+oDCtClCsW,CAAmB,+BCS1BtW,uCACEA,uDACFA,qEADEA,6MAWFA,0FAAoBA,oEAQpBA,wGAAiCA,kEAHrCA,sDACEA,kDAKFA,iCANoBA,gFC3Bf,IAAM4X,GAAuB,MAA9B,MAAOA,EAGXnZ,YAAoBkO,iBADXhO,YAAc,CAAEqW,OAAQ,EACK,CACtCvU,WAAY,+CAJDmX,GAAuB5X,sFAAvB4X,EAAuBpT,kcDRpCxE,oCAAgC,QAAIA,sBAA6BA,6BAEjEA,sCAAqB,UAArBA,CAAqB,UAArBA,CAAqB,UAArBA,CAAqB,WAMXA,iCACAA,wDACFA,iCAMNA,uCAA4B,YAExBA,0CAGFA,+BAKJA,kDAA4C,4BAExCA,mDAKFA,2BAEAA,0DAQFA,kCA3CGA,8DAAiCA,uHAQCA,wGAAwD,0DACnFA,qHASuBA,+HASXA,mEAAqB,eAQIA,mvGC3BlC4X,CAAuB,gHCC9B5X,uCAA0F,YAMtFA,kCACAA,uCAAmB,UAAMA,sBAAcA,2DANbA,kIAKFA,uGAAuD,yDACtDA,6IAZ/BA,uCAKEA,0CAUFA,6FAfiBA,yIAKmBA,wFAmBhCA,uCAA6F,aACjEA,sBAA2EA,2BACrGA,oCAAK,YACsBA,sBAAeA,2BACxCA,kCAGFA,uDAN0BA,qHAECA,+DACAA,0JAJ7BA,mFAA+CA,6GAgB/CA,0FAAoBA,oEAQpBA,wGAAiCA,kEAHrCA,qDACEA,iDAKFA,iCANoBA,4CAAsB,kBCzCvC,IAAM6X,GAA2B,MAAlC,MAAOA,UAAmCtC,EAO9C9W,cAAgB2N,QANPzN,YAAc,CAAEqW,OAAQ,GAEjCrW,sBAAmB,GACnBA,uBAAoB,GACpBA,kBAAe,CAES,CAExB8B,WACE9B,KAAK4X,gBACP,CAEQA,iBACN5X,KAAK2Y,iBAAmB,GAErB3Y,KAAKgP,OAAOqH,OAASrW,KAAK4Y,oBAC3B5Y,KAAK4Y,kBAAoB5Y,KAAKgP,OAAOqH,QAGvCrW,KAAK2Y,iBAAiBtU,KAAKrE,KAAK6X,mBADd,EAC8C7X,KAAKgP,OAAOqH,QAE9E,CAEAwB,mBAAmB7C,EAAQ8C,GACzB,IAAIE,EAAmBvR,KAAKwR,KAAKjY,KAAK4Y,kBAAoB5Y,KAAKmZ,cAC3DjB,EAAgBzR,KAAKwR,KAAKjY,KAAK4Y,kBAAoBZ,GACvDhY,KAAKmY,YAAc,GAEnB,QAAQC,EAAQ,EAAGA,EAAQJ,EAAkBI,IAAS,CACpD,IAAIC,EAAQ,GAEZ,QAASC,EAAS,EAAGA,EAASJ,EAAeI,IAAW,CACtD,IAAIpU,EAAIoU,EAAUF,EAAQF,EACtBK,EAAO,CACTC,OAAQ,EACRC,MAAOzD,EAAS9Q,EAAI,GAIpBqU,EAAKC,OADJtU,EAAI4T,EACS,EACR5T,EAAIlE,KAAK4Y,kBACD,GAEA,EAEhBP,EAAMhU,KAAKkU,GAGb,GAAoB,IAAjBF,EAAMxU,OAAc,CACrB,IAAI0U,EAAO,CACTC,QAAQ,EACRC,MAAO,GAGTJ,EAAMK,QAAQH,GAEdA,EAAO,CACLC,QAAQ,EACRC,MAAO,GAGTJ,EAAMhU,KAAKkU,GAGbvY,KAAKmY,YAAY9T,KAAKgU,GAIxB,OAAOrY,KAAKmY,WACd,CAEAa,4BACE,OAAOvS,KAAKwR,KAAKjY,KAAK4Y,kBAAoB5Y,KAAKmZ,aACjD,+CA1EWD,EAA0B,kDAA1BA,EAA0BrT,4xBDRvCxE,oCAAgC,QAAIA,sBAA8BA,6BAElEA,sCAAmB,WAEfA,yCAgBFA,2BAGAA,mDAAoD,0BAGhDA,iDAaFA,2BAEAA,sDACEA,kDAKFA,2BAEAA,yDAQFA,oCAzDCA,8DAAiCA,8GAOTA,iFAkBHA,mEAAmB,eAAnBA,CAAmB,eAgBnBA,mEAAqB,gBAQGA,upDCzCnC6X,CAA2B,4CCkDjC,IAAME,GAAiB,MAAxB,MAAOA,kDAAiB,iDAAjBA,uDAjBL9L,eACA+L,KACApF,GACAqF,KACAC,KACAtM,GACAuM,KACAC,KACAC,KACAC,oBACAC,KACAC,KACAC,KACAC,KACAC,QAGKZ,CAAiB,qCAzBtBxL,EAAgB,uFAEhBwG,GACAuD,GAAqBT,GAAuB+B,GACjBC,IAA0B", "names": ["WindowScrollingService", "constructor", "deviceService", "this", "styleTag", "buildStyleElement", "disable", "isMobile", "document", "body", "style", "top", "window", "scrollY", "position", "append<PERSON><PERSON><PERSON>", "enable", "scrollTo", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "setAttribute", "textContent", "i0", "factory", "ɵfac", "SiteEmpresaComponent", "clienteService", "sanitizer", "windowScrolling", "dectorDevice", "alturaSlide", "ngOnInit", "obtenhaEmpresa", "then", "resposta", "empresa", "carregueInformacoesDeHorarioDeFuncionamento", "urlMaps", "bypassSecurityTrustResourceUrl", "linkMaps", "atualizeArraysFotos", "obtenhaImagensInstagram", "respInstagram", "possuiInstagram", "postsInstagram", "posts", "<PERSON><PERSON><PERSON><PERSON>", "catch", "erroInstagram", "console", "log", "erroEmpresa", "mensagemErro", "abrir<PERSON><PERSON>sapp", "location", "href", "numeroWhatsapp", "whatsapp", "abrirGoogleMaps", "esta<PERSON><PERSON>", "estaAberta", "horariosHoje", "length", "descricaoHorario", "diasDaSemana", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "descricoesHorario", "i", "horarioHoje", "funciona", "push", "horarioAbertura", "substr", "horarioFechamento", "join", "fecharSlides", "exibirSlideShow", "exibirSlide", "posicao", "arrayRotate", "setTimeout", "fotosDaEmpresa", "shift", "objetosFoto", "ambiente", "map", "foto", "url", "link", "titulo", "fecharSlidesItens", "produtoSelecionado", "exibaProdutoSelecionado", "produto", "selectors", "_c0", "Timer", "fns", "commands", "ing", "start", "nextTime", "Date", "process", "diff", "count", "Math", "floor", "frequency", "step", "len", "add", "fn", "remove", "indexOf", "splice", "t", "ɵprov", "ɵngcc0", "token", "CountdownConfig", "demand", "leftTime", "template", "effect", "varRegular", "clock", "providedIn", "ngInjectableDef", "defineInjectable", "CountdownComponent", "el", "timer", "cog", "_notify", "hands", "left", "paused", "stoped", "EventEmitter", "finished", "notify", "event", "begin", "emit", "callEvent", "restart", "destroy", "init", "stop", "pause", "resume", "action", "me", "config", "Object", "assign", "nativeElement", "tmpl", "innerHTML", "lastIndex", "replace", "str", "content", "html", "for<PERSON>ach", "hand", "base", "node", "querySelector", "radix", "bits", "getLeft", "reflow", "_reflow", "apply", "time", "Error", "display", "force", "lastValue", "value", "repaint", "toDigitals", "digital", "toString", "end", "stopTime", "getTime", "con", "className", "digitals", "ngOnDestroy", "ngOnChanges", "changes", "firstChange", "ɵcmp", "hostVars", "hostBindings", "rf", "ctx", "inputs", "outputs", "features", "ngContentSelectors", "decls", "vars", "styles", "encapsulation", "changeDetection", "CountdownModule", "ɵmod", "ɵinj", "providers", "imports", "CommonModule", "RegrasComponent", "ModalKendo", "super", "obtenhaRegras", "regra", "ClienteComponent", "route", "dialogService", "fb", "router", "contatosService", "armazenamentoService", "EnumTipoDeCartao", "mask", "ngAfterViewInit", "counter", "telefoneCliente", "maskedInputController", "textMask", "inputElement", "component", "tokenCliente", "snapshot", "params", "idCartao", "cartao", "c", "codigo", "confirmeClientePeloToken", "setCliente", "idCliente", "exibaCliente", "cliente", "carregando", "cartoes", "endsWith", "id", "plano", "brindes", "sort", "brinde1", "brinde2", "valorEmPontos", "<PERSON><PERSON><PERSON>", "obtenhaCliente", "cartaoLink", "mensagem", "buscarContato", "mensagemDeErro", "confirmarContato", "contato", "salveSemExpirar", "verRegras", "windowRef", "open", "title", "min<PERSON><PERSON><PERSON>", "width", "innerWidth", "maxHeight", "innerHeight", "cssClass", "KendoPopupUtils", "solicitarBrinde", "onSubmit", "enviando", "frm", "valid", "mensagemErroEnvio", "mensagemFalhaEnvio", "obtenhaClientePorTelefone", "unmask", "dadosContato", "telefone", "validouCliente", "validouTelefone", "val", "onFinished", "habiliteReenviarCodigo", "mensagemAguarde", "reenviarCodigo", "enviado", "validouCodigo", "onSubmitCriarCartao", "criandoCartao", "salveNovoCartao", "nome", "genero", "dataNascimento", "resp", "erro", "tela", "exibaExtrato", "ConfirmarComponent", "activatedRoute", "<PERSON>ando", "<PERSON><PERSON>", "routes", "path", "SiteEmpresaRouterComponent", "children", "MeusCartoesComponent", "numero", "telaCliente", "canActivate", "AuthGuardService", "SiteEmpresaRoutingModule", "RouterModule", "CartaoConfirmadoComponent", "FrmValideCodigoComponent", "<PERSON><PERSON><PERSON><PERSON>", "maskedInputController2", "txtCodigo", "onSubmitValidarCodigo", "frmValidarCodigo", "valideCodigo", "parent", "ExtratoPontosComponent", "acoes", "carregueAcoes", "obtenhaUltimasAcoes", "inicio", "total", "acao", "carregou<PERSON>odos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CartaoProximosVencimentosComponent", "validade", "adicionePeriodosFiltro", "final", "moment", "vencimento", "dias", "periodo", "periodos", "busquePontuacaoVencidas", "round", "descricao", "fim", "format", "formato", "getPontos", "pontos", "tipoDeAcumulo", "formatCurrency", "formatNumber", "obtenhaPontuacaoVencer", "pontuacao<PERSON>encer", "buscou", "TelaCartaoComTabs", "pathname", "selecioneTabExtrato", "tabs", "selectTab", "viewQuery", "CartaoPontosComponent", "configurePontos", "brinde", "descricaoPontos", "toLowerCase", "texto", "String", "getPontosRestantes", "toFixed", "CartaoSeloComponent", "configure<PERSON><PERSON><PERSON>", "obtenhaMatrizSelos", "pontosSobrando", "minimoParaTroca", "quantidadeLinhas", "ceil", "selosPorLinha", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "linha", "selos", "coluna", "selo", "status", "valor", "unshift", "listaMatrizSelos", "pontosNecessarios", "pontosQueJ<PERSON>", "pontosRestantes", "totalPontos", "obtenhaQuantidadeDeLinhas", "CartaoCashbackComponent", "CartaoConsumoSeloComponent", "qtdePorLinha", "SiteEmpresaModule", "ScrollViewModule", "InlineSVGModule", "FormsModule", "DropDownListModule", "ReactiveFormsModule", "NgxMaskModule", "FidelidadeModule", "MaskedTextBoxModule", "NumericTextBoxModule", "CompartilhadoModule", "DatePickerModule", "TabStripModule"], "sourceRoot": "webpack:///", "sources": ["./src/app/window-scrolling.service.ts", "./src/app/site-empresa/site-empresa.component.html", "./src/app/site-empresa/site-empresa.component.ts", "./node_modules/ngx-countdown/__ivy_ngcc__/fesm2015/ngx-countdown.js", "./src/app/regras/regras.component.html", "./src/app/regras/regras.component.ts", "./src/app/cliente/cliente.component.html", "./src/app/cliente/cliente.component.ts", "./src/app/confirmar/confirmar.component.html", "./src/app/confirmar/confirmar.component.ts", "./src/app/site-empresa/site-empresa-routing.module.ts", "./src/app/site-empresa/site-empresa-router.component.ts", "./src/app/site-empresa/site-empresa-router.component.html", "./src/app/cliente/meus-cartoes/meus-cartoes.component.ts", "./src/app/cliente/meus-cartoes/meus-cartoes.component.html", "./src/app/cliente/cartao-confirmado/cartao-confirmado.component.html", "./src/app/cliente/cartao-confirmado/cartao-confirmado.component.ts", "./src/app/frm-valide-codigo/frm-valide-codigo.component.html", "./src/app/frm-valide-codigo/frm-valide-codigo.component.ts", "./src/app/componentes/extrato-pontos/extrato-pontos.component.html", "./src/app/componentes/extrato-pontos/extrato-pontos.component.ts", "./src/app/componentes/cartao-proximos-vencimentos/cartao-proximos-vencimentos.component.html", "./src/app/componentes/cartao-proximos-vencimentos/cartao-proximos-vencimentos.component.ts", "./src/app/cliente/cartao-pontos/cartao-pontos.component.html", "./src/app/cliente/cartao-pontos/cartao-pontos.component.ts", "./src/app/cliente/cartao-selo/cartao-selo.component.html", "./src/app/cliente/cartao-selo/cartao-selo.component.ts", "./src/app/cliente/cartao-cashback/cartao-cashback.component.html", "./src/app/cliente/cartao-cashback/cartao-cashback.component.ts", "./src/app/cliente/cartao-consumo-selo/cartao-consumo-selo.component.html", "./src/app/cliente/cartao-consumo-selo/cartao-consumo-selo.component.ts", "./src/app/site-empresa/site-empresa.module.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport {DeviceDetectorService} from \"ngx-device-detector\";\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class WindowScrollingService {\n\n  private styleTag: HTMLStyleElement;\n\n  // I initialize the window-scrolling service.\n  // --\n  // CAUTION: This service makes direct references to the global DOCUMENT object.\n  // Theoretically, the Renderer2 service should be able to provide an API that would\n  // allow me to side-step direct DOM-references. However, the Renderer2 service cannot\n  // be injected directly into another Service - only into a Directive. As such, I'm\n  // just dropping all the pretenses and I'm using the document directly.\n  constructor(private deviceService: DeviceDetectorService) {\n\n    // Rather than directly overwriting the style of the BODY tag (which is dicey),\n    // we're going to inject a STYLE element that overrides the scroll behavior. This\n    // way we can add and remove the style in order to toggle the behavior.\n    this.styleTag = this.buildStyleElement();\n\n  }\n\n  // ---\n  // PUBLIC METHODS.\n  // ---\n\n  // I disable the scrolling feature on the main viewport.\n  public disable(): void {\n    if(this.deviceService.isMobile()) {\n      document.body.style.top = `-${window.scrollY}px`;\n      document.body.style.position = 'fixed';\n    }\n    document.body.appendChild( this.styleTag );\n\n  }\n\n\n  // I re-enable the scrolling feature on the main viewport.\n  public enable(): void {\n    if(this.deviceService.isMobile()) {\n      const scrollY = document.body.style.top;\n      document.body.style.position = '';\n      document.body.style.top = '';\n      // tslint:disable-next-line:radix\n      window.scrollTo(0, parseInt(scrollY || '0') * -1);\n    }\n    document.body.removeChild( this.styleTag );\n\n  }\n\n  // ---\n  // PRIVATE METHODS.\n  // ---\n\n  // I build and return a Style element that will prevent scrolling on the body.\n  private buildStyleElement(): HTMLStyleElement {\n\n    let style = document.createElement( \"style\" );\n\n    style.type = \"text/css\";\n    style.setAttribute( \"data-debug\", \"Injected by WindowScrolling service.\" );\n    style.textContent = `\n            body {\n                overflow: hidden !important ;\n            }\n        `;\n\n    return( style );\n\n  }\n}\n", "<div *ngIf=\"carregou\"  class=\"container_total\">\n  <a [href]=\"'http://wa.me/55' + empresa?.whatsapp\" class=\"float\" target=\"_blank\">\n    <i class=\"fab fa-whatsapp my-float\"></i>\n  </a>\n\n  <div class=\"CoverImage FlexEmbed FlexEmbed--2by1\"\n       [style.background-image]='\"url(\\\"\\/images\\/empresa\\/\" + empresa.capa +\"\\\")\" '\n  ></div>\n\n  <div class=\"CoverImage FlexEmbed desfocada FlexEmbed--2by1\"\n       [style.background-image]='\"url(\\\"\\/images\\/empresa\\/\" + empresa.capa +\"\\\")\" '\n  ></div>\n\n  <div class=\"cartao conteudo topo\">\n    <div class=\"dados_empresa linha\">\n      <img class=\"imagem_empresa\" src=\"/images/empresa/{{empresa.logo}}\"/>\n      <div class=\"detalhes_empresa\">\n      <span class=\"nome_empresa\">\n        {{empresa.nome}}\n      </span>\n        <span class=\"endereco\">\n        {{empresa.endereco}}\n      </span>\n        <a class=\"whatsapp\" [href]=\"'http://wa.me/55' +empresa.numeroWhatsapp?.whatsapp\">\n          <img class=\"icone tam1\" src='/assets/fidelidade/icones/icon-whatsapp-mini.png'>\n          <span>\n          {{empresa.numeroWhatsapp?.whatsapp | mask: '(99) 9-9999-9999'}}\n        </span>\n        </a>\n      </div>\n\n    </div>\n    <div class=\"descricao_empresa\">\n      {{empresa.descricao}}\n    </div>\n    <div class=\"row\">\n      <div class=\"col horario\"><div class=\"bolinha\" [ngClass]=\"{\n        'fechado': !estaAberto\n      }\"></div><div class=\"descricao\">{{descricaoHorario}}</div></div>\n      <a routerLink=\"/cliente\" routerLinkActive=\"active\">\n        <div class=\"col azul\">\n          <div class=\"fidelidade\">\n            <div class=\"coracao\" [inlineSVG]=\"'/assets/fidelidade/icones/icon-fidelidade-pink.svg'\" [removeSVGAttributes]=\"['fill', 'width', 'height']\" style=\"width: 12px; height: auto\"></div>\n          </div>\n          <span class=\"texto_azul\">\n          Meu Cartão\n        </span>\n\n        </div>\n\n      </a>\n    </div>\n\n  </div>\n  <div class=\"cartao semborda\">\n    <div class=\"row\">\n      <div class=\"col-12\" [ngClass]=\"{'col-md-8': empresa.instagram}\" >\n        <div class=\"menu\" *ngIf=\"empresa.destaques && empresa.destaques.length > 0\">\n          <h4>\n            {{empresa.tituloDestaques}}\n          </h4>\n\n          <div class=\"container-scroll\" *ngIf=\"carregou\">\n            <div class=\"row flex-row flex-nowrap flex-md-wrap\">\n              <div class=\"col-8 \" [ngClass]=\"{'col-md-4': empresa.instagram, 'col-md-3': !empresa.instagram}\"  *ngFor=\"let destaque of empresa.destaques\"  style=\"height: 300px;\" >\n                <div class=\"brinde linha\" >\n                  <div class=\"row h-100 justify-content-center align-items-center caixa_brinde\">\n                    <div class=\"nome_brinde_pontos\">{{destaque.nome}}</div>\n                    <div class=\"container_imagem\" (click)=\"exibaProdutoSelecionado(destaque)\">\n                      <div class=\"\" *ngIf=\"destaque.imagens && destaque.imagens.length > 0\">\n                        <img class=\"foto_brinde\" src=\"https://www.promokit.com.br/images/empresa/{{destaque.imagens[0].linkImagem}}\">\n                      </div>\n                    </div>\n                    <div class=\"preco_troca\">\n                      <h3 class=\"mt-1\" *ngIf=\"destaque.exibirPrecoNoSite && destaque.preco > 0.0\">\n                        R$ {{destaque.preco.toFixed(2).replace('.', ',')}}\n                      </h3>\n                      <h4 class=\"mt-1\" *ngIf=\"!destaque.exibirPrecoNoSite || destaque.preco == 0.0\">\n                        Super Oferta\n                      </h4>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"menu pt-3\" *ngIf=\"empresa.ambiente && empresa.ambiente.length > 0\">\n          <h4>\n            {{empresa.tituloFotos}}\n          </h4>\n\n          <div class=\"container-scroll\" *ngIf=\"carregou\">\n            <div class=\"row flex-row flex-nowrap flex-md-wrap\">\n              <div class=\"col-8\" *ngFor=\"let foto of empresa.ambiente; let i = index;\" [ngClass]=\"{'col-md-4': empresa.instagram, 'col-md-3': !empresa.instagram}\">\n                <div class=\"brinde linha\" >\n\n                  <div class=\"row h-100 caixa_brinde justify-content-center\">\n                    <div class=\"container_imagem justify-content-center align-items-center\"  style=\"height: 180px;\">\n                      <div class=\"\">\n                        <img class=\"foto_ambiente img-fluid\" src=\"https://www.promokit.com.br/images/empresa/{{foto.link}}\" (click)=\"exibirSlide(i)\">\n                      </div>\n                    </div>\n                    <div class=\"preco_troca\">\n                      <h4 class=\"mt-1\">\n                        <div class=\"nome_brinde_pontos\">{{foto.titulo}}</div>\n                      </h4>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"col-12 col-md-4\" *ngIf=\"empresa.instagram\">\n        <div class=\"menu\">\n          <h4>\n            Mais sobre nós\n          </h4>\n          <div class=\"sobre_nos\">\n            <div class=\"pb-1 linha\">\n              <iframe [src]=\"urlMaps\" frameborder=\"0\" style=\"border:0; width: 100%; height: 100%\" allowfullscreen></iframe>\n            </div>\n            <div class=\"linha\">\n              <div class=\"row mt-1 mb-1\">\n                <div *ngFor=\"let post of postsInstagram\" class=\"col-6 p-0\">\n                  <a href=\"{{post.link}}\" target=\"_blank\"><img class=\"img-fluid p-1\" src=\"{{post.imagem}}\" alt=\"Foto instagram\"/></a>\n                </div>\n              </div>\n            </div>\n            <div class=\"mt-1 mb-1 text-center cinza\">\n              <a href=\"https://instagram.com/{{empresa.instagram}}\" target=\"blank\">\n                <div class=\"icone insta tam1\" [inlineSVG]=\"'/assets/fidelidade/icones/instagram.svg'\" [removeSVGAttributes]=\"['fill']\" style=\"display: inline-block\"></div>\n                Veja mais no Instagram\n\n              </a>\n            </div>\n\n          </div>\n\n\n        </div>\n\n      </div>\n    </div>\n\n  </div>\n</div>\n<div [hidden]=\"!carregou || !exibirSlideShow\" class=\"slides-fotos\">\n  <div *ngIf=\"carregou\">\n\n    <div class=\"icon-fechar\" [inlineSVG]=\"'/assets/fidelidade/icones/icon-fechar.svg'\" [removeSVGAttributes]=\"['width', 'height']\" style=\"width: 32px; height: auto\" (click)=\"fecharSlides()\" ></div>\n    <h3>{{empresa.tituloFotos}}</h3>\n  </div>\n  <div *ngIf=\"carregou\" class=\"cartao descricao\">\n    <kendo-scrollview\n      [data]=\"fotosDaEmpresa\"\n      style=\"background: #000;text-align: center;\"\n      [width]=\"'100%'\"\n      [endless]=\"true\"\n      [height]=\"alturaSlide\"\n      [arrows]=\"true\"\n      [pageable]=\"true\">\n      <ng-template let-item=\"item\">\n        <h2 class=\"demo-title\">{{item.titulo}}</h2>\n        <img class=\"foto-brinde img-fluid\" src='{{item.url}}' alt='{{item.titulo}}' [ngStyle]=\"{minWidth: 400}\" draggable=\"false\" />\n      </ng-template>\n    </kendo-scrollview>\n  </div>\n</div>\n\n<div *ngIf=\"carregou && produtoSelecionado\" class=\"slides-produtos\">\n  <div class=\"container-itens\">\n    <div class=\"cartao descricao\">\n      <div class=\"icon-fechar\" [inlineSVG]=\"'/assets/fidelidade/icones/icon-fechar.svg'\" [removeSVGAttributes]=\"['width', 'height']\" style=\"width: 32px; height: auto\" (click)=\"fecharSlidesItens()\" ></div>\n      <h3>Detalhes do Item</h3>\n      <DIV class=\"linha\" *ngIf=\"produtoSelecionado.imagens && produtoSelecionado.imagens.length > 0\">\n        <img class=\"foto_brinde\" src=\"/images/empresa/{{produtoSelecionado.imagens[0].linkImagem}}\">\n      </DIV>\n      <h4>\n        {{produtoSelecionado.nome}}\n      </h4>\n      <span>{{produtoSelecionado.descricao}}</span>\n      <span class=\"preco_troca azul grande mt-1\" style=\"display: block\" *ngIf=\"produtoSelecionado.exibirPrecoNoSite && produtoSelecionado.preco > 0.0\">\n        {{produtoSelecionado.preco | currency:\"BRL\"}}\n      </span>\n      <span class=\"preco_troca azul grande mt-1\" style=\"display: block\" *ngIf=\"!produtoSelecionado.exibirPrecoNoSite || produtoSelecionado.preco == 0.0\">\n        Super Oferta\n      </span>\n      <a [href]=\"'http://wa.me/55' + empresa?.whatsapp + '?text=' + produtoSelecionado.mensagemPedido\">\n        <div class=\"botao_produto verde mt-3\">\n          <img class=\"icone tam1\" src='/assets/fidelidade/icones/icon-whatsapp-mini.png'> Pedir pelo WhatsApp\n        </div>\n      </a>\n    </div>\n  </div>\n</div>\n", "import {Component, OnInit, ViewChild} from '@angular/core';\nimport {ClienteService} from \"../services/cliente.service\";\nimport {DomSanitizer, SafeResourceUrl} from \"@angular/platform-browser\";\nimport {WindowScrollingService} from \"../window-scrolling.service\";\nimport {DeviceDetectorService} from \"ngx-device-detector\";\n\n@Component({\n  selector: 'app-site-empresa',\n  templateUrl: './site-empresa.component.html',\n  styleUrls: ['./site-empresa.component.scss']\n})\nexport class SiteEmpresaComponent implements OnInit {\n  empresaLogada = \"fibo\";\n  empresa: any = {};\n  carregou = false;\n  estaAberto: boolean;\n  descricaoHorario: string;\n  urlMaps: SafeResourceUrl;\n  exibirSlideShow = false;\n  fotosDaEmpresa: string[];\n  objetosFoto: any[];\n  exibirSlidesItens = false;\n  produtoSelecionado: any;\n\n  diasDaSemana = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];\n  mensagemErro: string;\n  possuiInstagram: boolean;\n  postsInstagram: any[];\n  alturaSlide = '400px';\n\n  constructor(private clienteService: ClienteService, private sanitizer: DomSanitizer,\n              private windowScrolling: WindowScrollingService, private dectorDevice: DeviceDetectorService) {\n    this.alturaSlide = this.dectorDevice.isMobile() ? '300px' : '500px';\n  }\n\n  ngOnInit() {\n    this.clienteService.obtenhaEmpresa().then((resposta) => {\n      this.empresa = resposta.empresa;\n\n      this.carregueInformacoesDeHorarioDeFuncionamento();\n\n      this.urlMaps = this.sanitizer.bypassSecurityTrustResourceUrl(this.empresa.linkMaps);\n      this.atualizeArraysFotos();\n\n      this.clienteService.obtenhaImagensInstagram().then((respInstagram) => {\n        this.possuiInstagram = true;\n        this.postsInstagram = respInstagram.posts;\n        this.carregou = true;\n      }).catch(erroInstagram => {\n        this.possuiInstagram = false;\n        this.carregou = true;\n        console.log(erroInstagram);\n\n      });\n    }).catch(erroEmpresa => {\n      this.carregou = true;\n      console.log(this.mensagemErro);\n      this.mensagemErro = \"Não foi possível carregar a empresa. Erro: \" + erroEmpresa;\n    });\n  }\n\n\n  abrirWhatsapp() {\n    window.location.href = \"http://wa.me/55\" + this.empresa.numeroWhatsapp.whatsapp\n  }\n\n  abrirGoogleMaps() {\n    window.location.href = this.empresa.linkMaps;\n  }\n\n  private carregueInformacoesDeHorarioDeFuncionamento() {\n    this.estaAberto = this.empresa.estaAberta;\n\n    if(!this.empresa.horariosHoje || this.empresa.horariosHoje.length === 0) {\n      this.descricaoHorario = \"Fechado\";\n      return;\n    }\n\n    this.descricaoHorario = this.diasDaSemana[this.empresa.horariosHoje[0].diaDaSemana];\n    let descricoesHorario = [];\n\n    for(let i = 0; i < this.empresa.horariosHoje.length; i++) {\n      let horarioHoje = this.empresa.horariosHoje[i];\n      if(!horarioHoje.funciona) {\n        descricoesHorario.push(\"Fechado\");\n        break;\n      }\n      descricoesHorario.push(horarioHoje.horarioAbertura.substr(0, 5)  + \" - \" + horarioHoje.horarioFechamento.substr(0, 5) )\n    }\n    this.descricaoHorario += \" \" + descricoesHorario.join(\" | \");\n  }\n\n  fecharSlides() {\n    this.exibirSlideShow = false;\n    this.atualizeArraysFotos()\n    this.windowScrolling.enable();\n  }\n\n  exibirSlide(posicao: number) {\n    this.windowScrolling.disable();\n\n    for(let i = 0; i < posicao; i++)\n      this.arrayRotate();\n\n    setTimeout(() => {\n      this.exibirSlideShow = true;\n    }, 0);\n\n  }\n\n  arrayRotate() {\n    this.fotosDaEmpresa.push(this.fotosDaEmpresa.shift());\n    this.objetosFoto.push(this.objetosFoto.shift());\n  }\n\n  private atualizeArraysFotos() {\n    this.fotosDaEmpresa = this.empresa.ambiente.map(foto => {\n      return {\n        url: \"https://www.promokit.com.br/images/empresa/\" + foto.link,\n        titulo: foto.titulo\n      };\n    });\n\n    this.objetosFoto = this.empresa.ambiente.map(foto => foto);\n  }\n\n  fecharSlidesItens() {\n    this.windowScrolling.enable();\n    this.produtoSelecionado = null;\n  }\n\n  exibaProdutoSelecionado(produto: any) {\n    this.windowScrolling.disable();\n    this.produtoSelecionado = produto;\n  }\n}\n", "import { CommonModule } from '@angular/common';\nimport { Injectable, Component, ElementRef, Input, Output, EventEmitter, ChangeDetectionStrategy, ViewEncapsulation, defineInjectable, NgModule } from '@angular/core';\n\nimport * as ɵngcc0 from '@angular/core';\n\nconst _c0 = [\"*\"];\nclass Timer {\n    constructor() {\n        this.fns = [];\n        this.commands = [];\n        this.ing = false;\n    }\n    start() {\n        if (this.ing === true)\n            return;\n        this.ing = true;\n        this.nextTime = +new Date();\n        this.process();\n    }\n    process() {\n        while (this.commands.length) {\n            this.commands.shift()();\n        }\n        let diff = +new Date() - this.nextTime;\n        const count = 1 + Math.floor(diff / 100);\n        diff = 100 - diff % 100;\n        this.nextTime += 100 * count;\n        let frequency, step, i, len;\n        for (i = 0, len = this.fns.length; i < len; i += 2) {\n            frequency = this.fns[i + 1];\n            // 100/s\n            if (0 === frequency) {\n                this.fns[i](count);\n                // 1000/s\n            }\n            else {\n                // 先把末位至0，再每次加2\n                frequency += 2 * count - 1;\n                step = Math.floor(frequency / 20);\n                if (step > 0) {\n                    this.fns[i](step);\n                }\n                // 把末位还原成1\n                this.fns[i + 1] = frequency % 20 + 1;\n            }\n        }\n        if (this.ing) {\n            setTimeout(() => this.process(), diff);\n        }\n    }\n    add(fn, frequency) {\n        this.commands.push(() => {\n            this.fns.push(fn);\n            this.fns.push(frequency === 1000 ? 1 : 0);\n            this.ing = true;\n        });\n    }\n    remove(fn) {\n        this.commands.push(() => {\n            const i = this.fns.indexOf(fn);\n            if (i !== -1) {\n                this.fns.splice(i, 2);\n            }\n            this.ing = this.fns.length > 0;\n        });\n    }\n}\nTimer.ɵfac = function Timer_Factory(t) { return new (t || Timer)(); };\nTimer.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: Timer, factory: Timer.ɵfac });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(Timer, [{\n        type: Injectable\n    }], function () { return []; }, null); })();\n\nclass CountdownConfig {\n    constructor() {\n        this.demand = false;\n        this.leftTime = 0;\n        this.template = '$!h!时$!m!分$!s!秒';\n        this.effect = 'normal';\n        this.varRegular = /\\$\\!([\\-\\w]+)\\!/g;\n        this.clock = ['d', 100, 2, 'h', 24, 2, 'm', 60, 2, 's', 60, 2, 'u', 10, 1];\n    }\n}\nCountdownConfig.ɵfac = function CountdownConfig_Factory(t) { return new (t || CountdownConfig)(); };\nCountdownConfig.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: CountdownConfig, factory: CountdownConfig.ɵfac, providedIn: 'root' });\nCountdownConfig.ngInjectableDef = defineInjectable({ factory: function CountdownConfig_Factory() { return new CountdownConfig(); }, token: CountdownConfig, providedIn: \"root\" });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(CountdownConfig, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], function () { return []; }, null); })();\n\nclass CountdownComponent {\n    constructor(el, timer, cog) {\n        this.el = el;\n        this.timer = timer;\n        this.cog = cog;\n        this.frequency = 1000;\n        this._notify = {};\n        this.hands = [];\n        this.left = 0;\n        this.paused = false;\n        /** 两种情况会触发：时间终止或调用 `stop()` */\n        this.stoped = false;\n        this.start = new EventEmitter();\n        this.finished = new EventEmitter();\n        this.notify = new EventEmitter();\n        this.event = new EventEmitter();\n    }\n    /** 开始，当 `demand: false` 时触发 */\n    begin() {\n        this.paused = false;\n        this.start.emit();\n        this.callEvent('start');\n    }\n    /** 重新开始 */\n    restart() {\n        if (!this.stoped)\n            this.destroy();\n        this.init();\n        this.callEvent('restart');\n    }\n    /** 停止 */\n    stop() {\n        if (this.stoped)\n            return;\n        this.stoped = true;\n        this.destroy();\n        this.callEvent('stop');\n    }\n    /** 暂停（限未终止有效） */\n    pause() {\n        if (this.stoped || this.paused)\n            return;\n        this.paused = true;\n        this.callEvent('pause');\n    }\n    /** 恢复 */\n    resume() {\n        if (this.stoped || !this.paused)\n            return;\n        this.paused = false;\n        this.callEvent('resume');\n    }\n    callEvent(action) {\n        this.event.emit({ action, left: this.left });\n    }\n    init() {\n        const me = this;\n        me.config = Object.assign({}, new CountdownConfig(), me.cog, me.config);\n        const el = me.el.nativeElement;\n        me.paused = me.config.demand;\n        me.stoped = false;\n        // 分析markup\n        const tmpl = el.innerHTML || me.config.template;\n        me.config.varRegular.lastIndex = 0;\n        el.innerHTML = tmpl.replace(me.config.varRegular, (str, type) => {\n            // 时钟频率校正.\n            if (type === 'u' || type === 's-ext')\n                me.frequency = 100;\n            // 生成hand的markup\n            let content = '';\n            if (type === 's-ext') {\n                me.hands.push({ type: 's' });\n                me.hands.push({ type: 'u' });\n                content =\n                    me.html('', 's', 'handlet') +\n                        me.html('.', '', 'digital') +\n                        me.html('', 'u', 'handlet');\n            }\n            else {\n                me.hands.push({ type: type });\n            }\n            return me.html(content, type, 'hand');\n        });\n        const clock = me.config.clock;\n        me.hands.forEach((hand) => {\n            const type = hand.type;\n            let base = 100, i;\n            hand.node = el.querySelector(`.hand-${type}`);\n            // radix, bits 初始化\n            for (i = clock.length - 3; i > -1; i -= 3) {\n                if (type === clock[i]) {\n                    break;\n                }\n                base *= clock[i + 1];\n            }\n            hand.base = base;\n            hand.radix = clock[i + 1];\n            hand.bits = clock[i + 2];\n        });\n        me.getLeft();\n        me.reflow(0, true);\n        // bind reflow to me\n        const _reflow = me.reflow;\n        me.reflow = (count = 0) => {\n            return _reflow.apply(me, [count]);\n        };\n        // 构建 notify\n        if (me.config.notify) {\n            me.config.notify.forEach((time) => {\n                if (time < 1)\n                    throw new Error(`the notify config must be a positive integer.`);\n                time = time * 1000;\n                time = time - (time % me.frequency);\n                me._notify[time] = true;\n            });\n        }\n        me.timer.add(me.reflow, me.frequency);\n        // show\n        el.style.display = 'inline';\n        this.timer.start();\n        return me;\n    }\n    destroy() {\n        this.timer.remove(this.reflow);\n        return this;\n    }\n    /**\n     * 更新时钟\n     */\n    reflow(count = 0, force = false) {\n        const me = this;\n        if (!force && (me.paused || me.stoped))\n            return;\n        me.left = me.left - me.frequency * count;\n        me.hands.forEach((hand) => {\n            hand.lastValue = hand.value;\n            hand.value = Math.floor(me.left / hand.base) % hand.radix;\n        });\n        me.repaint();\n        if (me._notify[me.left]) {\n            me.notify.emit(me.left);\n            me.callEvent('notify');\n        }\n        if (me.left < 1) {\n            me.finished.emit(0);\n            me.stoped = true;\n            me.callEvent('finished');\n            me.destroy();\n        }\n    }\n    /**\n     * 重绘时钟\n     */\n    repaint() {\n        const me = this;\n        if (me.config.repaint) {\n            me.config.repaint.apply(me);\n            return;\n        }\n        let content;\n        me.hands.forEach((hand) => {\n            if (hand.lastValue !== hand.value) {\n                content = '';\n                me.toDigitals(hand.value, hand.bits).forEach((digital) => {\n                    content += me.html(digital.toString(), '', 'digital');\n                });\n                hand.node.innerHTML = content;\n            }\n        });\n    }\n    /**\n     * 获取倒计时剩余帧数\n     */\n    getLeft() {\n        const me = this;\n        let left = me.config.leftTime * 1000;\n        const end = me.config.stopTime;\n        if (!left && end)\n            left = end - new Date().getTime();\n        me.left = left - (left % me.frequency);\n    }\n    /**\n     * 生成需要的html代码，辅助工具\n     */\n    html(con, className, type) {\n        switch (type) {\n            case 'hand':\n            case 'handlet':\n                className = type + ' hand-' + className;\n                break;\n            case 'digital':\n                if (con === '.') {\n                    className = type + ' ' + type + '-point ' + className;\n                }\n                else {\n                    className = type + ' ' + type + '-' + con + ' ' + className;\n                }\n                break;\n        }\n        return '<span class=\"' + className + '\">' + con + '</span>';\n    }\n    /**\n     * 把值转换为独立的数字形式\n     */\n    toDigitals(value, bits) {\n        value = value < 0 ? 0 : value;\n        const digitals = [];\n        // 把时、分、秒等换算成数字.\n        while (bits--) {\n            digitals[bits] = value % 10;\n            value = Math.floor(value / 10);\n        }\n        return digitals;\n    }\n    ngOnInit() {\n        this.init();\n        if (!this.config.demand)\n            this.begin();\n    }\n    ngOnDestroy() {\n        this.destroy();\n    }\n    ngOnChanges(changes) {\n        if (!changes.config.firstChange) {\n            this.restart();\n        }\n    }\n}\nCountdownComponent.ɵfac = function CountdownComponent_Factory(t) { return new (t || CountdownComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(Timer), ɵngcc0.ɵɵdirectiveInject(CountdownConfig)); };\nCountdownComponent.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: CountdownComponent, selectors: [[\"countdown\"]], hostVars: 2, hostBindings: function CountdownComponent_HostBindings(rf, ctx) { if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"count-down\", true);\n    } }, inputs: { config: \"config\" }, outputs: { start: \"start\", finished: \"finished\", notify: \"notify\", event: \"event\" }, features: [ɵngcc0.ɵɵNgOnChangesFeature], ngContentSelectors: _c0, decls: 1, vars: 0, template: function CountdownComponent_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n    } }, styles: [\"\\n      countdown {\\n        display: none;\\n      }\\n    \"], encapsulation: 2, changeDetection: 0 });\n/** @nocollapse */\nCountdownComponent.ctorParameters = () => [\n    { type: ElementRef },\n    { type: Timer },\n    { type: CountdownConfig }\n];\nCountdownComponent.propDecorators = {\n    config: [{ type: Input }],\n    start: [{ type: Output }],\n    finished: [{ type: Output }],\n    notify: [{ type: Output }],\n    event: [{ type: Output }]\n};\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(CountdownComponent, [{\n        type: Component,\n        args: [{ selector: 'countdown', template: `\n    <ng-content></ng-content>\n  `, host: { '[class.count-down]': 'true' }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\"\\n      countdown {\\n        display: none;\\n      }\\n    \"] }]\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: Timer }, { type: CountdownConfig }]; }, { start: [{\n            type: Output\n        }], finished: [{\n            type: Output\n        }], notify: [{\n            type: Output\n        }], event: [{\n            type: Output\n        }], config: [{\n            type: Input\n        }] }); })();\n\nclass CountdownModule {\n}\nCountdownModule.ɵfac = function CountdownModule_Factory(t) { return new (t || CountdownModule)(); };\nCountdownModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: CountdownModule });\nCountdownModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ providers: [Timer], imports: [CommonModule] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(CountdownModule, [{\n        type: NgModule,\n        args: [{\n                imports: [CommonModule],\n                providers: [Timer],\n                declarations: [CountdownComponent],\n                exports: [CountdownComponent]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(CountdownModule, { declarations: function () { return [CountdownComponent]; }, imports: function () { return [CommonModule]; }, exports: function () { return [CountdownComponent]; } }); })();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CountdownConfig as ɵa, CountdownComponent, Timer, CountdownModule };\n\n", "<div class=\"modal-content\">\n  <div class=\"modal-header\">\n    <h4 class=\"modal-title\" id=\"myModalLabel\">\n      Regras\n    </h4>\n    <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"fecheModal()\">\n      <span aria-hidden=\"true\">&times;</span>\n    </button>\n  </div>\n  <div #pnl class=\"modal-body\">\n\n    <h3>{{regra.titulo}}</h3>\n\n    <p *ngIf=\"regra.descricao\">{{regra.descricao}}</p>\n\n\n    <ul *ngFor=\"let plano of regra.planos; let i = index;\"  class=\" list-group mt-2\" >\n      <h5 *ngIf=\"regra.planos.length > 1\">{{i+1}} - {{plano.nome}}</h5>\n      <p> {{plano.descricao}}</p>\n      <li *ngFor=\"let item of plano.trocas\" class=\"list-group-item\">\n        <p>{{item}}</p>\n      </li>\n\n      <div class=\"extras\">\n        <p *ngFor=\"let extra of plano.extras\" class=\"mt-2\">\n          {{extra}}\n        </p>\n      </div>\n\n    </ul>\n\n   <div class=\"extras\">\n     <p *ngFor=\"let extra of regra.extras\"  >\n       {{extra}}\n     </p>\n   </div>\n  </div>\n\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport {ClienteService} from \"../services/cliente.service\";\nimport {ModalKendo} from \"../lib/ModalKendo\";\n\n@Component({\n  selector: 'app-regras',\n  templateUrl: './regras.component.html',\n  styleUrls: ['./regras.component.scss']\n})\nexport class RegrasComponent extends ModalKendo implements OnInit {\n  regra: any =  {};\n  constructor( private clienteService: ClienteService ) {\n    super()\n  }\n\n  ngOnInit() {\n    this.clienteService.obtenhaRegras().then( regra => {\n      this.regra = regra;\n    })\n  }\n\n}\n", "<div class=\"dados_cliente\">\n  <div class=\"caixa_titulo\">\n    <span class=\"titulo\"    *ngIf =\"!cliente.id || cliente.cartoes?.length==1 || cartaoLink\"><PERSON><PERSON></span>\n    <span class=\"titulo\"    *ngIf =\"cliente.cartoes && cliente.cartoes?.length > 1 && !cartaoLink\">Me<PERSON></span>\n    <span class=\"subtitulo\" *ngIf =\"carregou\"><div class=\"icone tam1\" [inlineSVG]=\"'/assets/fidelidade/icones/user.svg'\"\n                                                  [removeSVGAttributes]=\"['fill']\"></div> {{ cliente.nome }} | {{ cliente.telefone | telefone}}</span>\n  </div>\n</div>\n\n<div class=\"cartao conteudo\">\n  <div class=\"dados_empresa linha\" *ngIf=\"empresa\">\n      <a routerLink=\"/\">\n        <img class=\"imagem_empresa\" src=\"/images/empresa/{{empresa.logo}}\"/>\n      </a>\n      <div class=\"detalhes_empresa\">\n        <a routerLink=\"/\">\n          <span class=\"nome_empresa\">\n            {{empresa.nome}}\n          </span>\n        </a>\n        <span class=\"endereco\">\n            {{empresa.endereco}}\n          </span>\n        <a class=\"whatsapp\" target=\"_blank\" [href]=\"'http://wa.me/55' + empresa.numeroWhatsapp?.whatsapp\">\n          <img class=\"icone tam1\" src='/assets/fidelidade/icones/icon-whatsapp-mini.png'>\n          <span>\n            {{empresa?.numeroWhatsapp?.whatsapp | mask: '(99) 9-9999-9999'}}\n          </span>\n        </a>\n      </div>\n    </div>\n\n  <div class=\"carregando\" *ngIf=\"carregando\" >\n    <div class=\"spinner-border\" role=\"status\">\n    </div><bR><br>\n    <span>Carregando...</span>\n  </div>\n\n  <div class=\"carregando\" *ngIf=\"!carregando && !carregou\" >\n    <span style=\"color: red\">{{mensagemDeErro}}</span>\n  </div>\n\n  <div class=\"alert alert-success mt-1\" role=\"alert\" *ngIf=\"codigo === 'criado'\">\n    <button type=\"button\" class=\"close\" data-dismiss=\"alert\" aria-label=\"Close\">\n      <span aria-hidden=\"true\">×</span>\n    </button>\n    <span >\n      Seu cartao foi criado com sucesso!\n\n      <span  *ngIf=\"cliente.cartoes?.length  && !cliente.cartoes[0].plano.cartaoConsumo\">\n         Agora comece a juntar  {{cliente.cartoes[0].plano.tipoDeAcumulo}} para conseguir brindes, descontos.\n      </span>\n    </span>\n\n  </div>\n\n  <div *ngFor=\"let cartao of cliente.cartoes\" class=\"mt-3\">\n    <div *ngIf=\"cartao.plano.tipoDeAcumulo === tipoSelo && !cartao.plano.cartaoConsumo\">\n      <div   *ngIf=\"!cartaoLink || cartaoLink==cartao.id\">\n        <app-cartao-selo  [cartao]=\"cartao\"  > </app-cartao-selo>\n      </div>\n    </div>\n\n    <div *ngIf=\"cartao.plano.tipoDeAcumulo === tipoSelo && cartao.plano.cartaoConsumo\">\n      <div   *ngIf=\"!cartaoLink || cartaoLink==cartao.id\">\n        <app-cartao-consumo-selo  [cartao]=\"cartao\"  > </app-cartao-consumo-selo>\n      </div>\n    </div>\n\n    <div *ngIf=\"cartao.plano.tipoDeAcumulo === tipoPontos && !cartao.plano.cartaoConsumo\">\n      <div  [hidden]=\"(cartao.plano.tipoDeAcumulo == tipoSelo)\" *ngIf=\"!cartaoLink || cartaoLink==cartao.id\">\n        <app-cartao-pontos  [cartao]=\"cartao\" [empresa]=\"empresa\" > </app-cartao-pontos>\n      </div>\n    </div>\n\n    <div *ngIf=\"cartao.plano.tipoDeAcumulo === tipoReais\">\n      <div    *ngIf=\"!cartaoLink || cartaoLink==cartao.id\">\n        <app-cartao-cashback  [cartao]=\"cartao\"  > </app-cartao-cashback>\n      </div>\n    </div>\n  </div>\n\n  <div *ngIf=\"carregou && !cliente.id\">\n    <div [hidden]=\"tela !== 'INFORMAR_TELEFONE'\">\n      <h2>Informe seu telefone</h2>\n      Informe o seu número de telefone\n      <form [ngClass]=\"{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}\"  novalidate #frm=\"ngForm\" (ngSubmit)=\"onSubmit()\">\n        <div class=\"form-group mb-2\" >\n          <input [disabled]=\"enviado && mensagemAguarde\" [(ngModel)]=\"dadosContato.telefone\" #telefone=\"ngModel\" #txtTelefone\n                 type=\"tel\" appAutoFocus class=\"form-control ng-tns-c12-1 ui-inputtext ui-widget ui-state-default ui-corner-all\"\n                  placeholder=\"(__) _-____-____\" required  campoTelefone  name=\"telefone\"/>\n          <div class=\"invalid-feedback\">\n            <p *ngIf=\"telefone.errors?.required\">Celular é obrigatório</p>\n            <p *ngIf=\"telefone.errors?.campoTelefone\">Informe um número de celular válido</p>\n          </div>\n        </div>\n        <div class=\"form-group mb-2 text-center\">\n          <button class=\"btn btn-block\" type=\"submit\" [disabled]=\"enviando || (enviado && mensagemAguarde)\" [ngClass]=\"{disabled: enviando || (enviado && mensagemAguarde)}\"> Abrir meu Cartão </button>\n          <div class=\"alert alert-danger mt-2\" role=\"alert\" *ngIf=\"frm.submitted && mensagemErroEnvio && !frm.valid\" [innerHTML]=\"mensagemErroEnvio\">\n            {{mensagemErroEnvio}}\n          </div>\n          <div class=\"alert alert-danger mt-2\" role=\"alert\" *ngIf=\"frm.submitted && mensagemFalhaEnvio\" [innerHTML]=\"mensagemFalhaEnvio\">\n            {{mensagemErroEnvio}}\n          </div>\n        </div>\n      </form>\n    </div>\n    <div [hidden]=\"tela !== 'INFORMAR_NOME'\">\n      <div class=\"card\">\n        <div class=\"card-body\" style=\"padding: 0.8rem;\">\n          <label>Seu telefone</label>\n\n          <div id=\"cardCollpase1\" class=\"collapse show\">\n            <div>\n              <div class=\"row mt-1\">\n                <div class=\"col\">\n                  <h4 class=\"mt-0\"><span style=\"font-weight: bold;\">{{dadosContato.telefone | telefone}} </span></h4>\n                </div>\n              </div> <!-- end row -->\n            </div>\n          </div> <!-- collapsed end -->\n        </div> <!-- end card-body -->\n      </div>\n      <h3>Agora informe seus dados para completar o cadastro </h3>\n\n\n      <form [ngClass]=\"{'needs-validation': !frmCriarNome.submitted, 'was-validated': frmCriarNome.submitted}\"  novalidate #frmCriarNome=\"ngForm\" (ngSubmit)=\"onSubmitCriarCartao()\">\n        <div class=\"form-group mb-2\" >\n          <label for=\"nome\">\n            Informe seu nome\n          </label>\n          <input id=\"nome\" [disabled]=\"enviado && mensagemAguarde\" [(ngModel)]=\"dadosContato.nome\" #txtNome=\"ngModel\"\n                 type=\"text\" appAutoFocus class=\"form-control ng-tns-c12-1 ui-inputtext ui-widget ui-state-default ui-corner-all\"\n                 placeholder=\"Seu nome\" required  name=\"nome\"/>\n          <div class=\"invalid-feedback\">\n            <p *ngIf=\"txtNome.errors?.required\">Nome é obrigatório</p>\n          </div>\n        </div>\n        <div class=\"form-group mb-2\" >\n          <label for=\"dataNascimento\">\n            Informe sua data de nascimento\n          </label>\n          <kendo-datepicker #txtDataNascimento=\"ngModel\" [(ngModel)]=\"dadosContato.dataNascimento\" class=\"form-control\" id=\"dataNascimento\"\n                            name=\"dataNascimento\">\n          </kendo-datepicker>\n        </div>\n        <div class=\"form-group mb-2\" >\n          <label >\n            Informe seu gênero\n          </label>\n          <div class=\"mt-1 mb-1\" style=\"width:250px;\">\n            <input type=\"radio\" name=\"sexo\" id=\"sexohi\" class=\"k-radio right\" value=\"Homem\" [(ngModel)]=\"dadosContato.genero\" required kendoRadioButton/>\n            <label class=\"k-radio-label mr-1\" for=\"sexohi\">Masculino</label>\n            <input type=\"radio\" name=\"sexo\" id=\"sexomi\" class=\"k-radio right\" value=\"Mulher\" [(ngModel)]=\"dadosContato.genero\" required kendoRadioButton/>\n            <label class=\"k-radio-label\" for=\"sexomi\">Feminino</label>\n            <div class=\"invalid-feedback\"  >\n              Sexo é obrigatório\n            </div>\n          </div>\n        </div>\n        <div class=\"form-group mb-2 text-center\">\n          <button class=\"btn btn-block btn-success\" type=\"submit\" [disabled]=\"enviando || (enviado && mensagemAguarde)\" [ngClass]=\"{disabled: enviando || (enviado && mensagemAguarde)}\"> Criar Cartão </button>\n          <div class=\"alert alert-danger mt-2\" role=\"alert\" *ngIf=\"frmCriarNome.submitted && mensagemErroCriarCartao && !frmCriarNome.valid\" [innerHTML]=\"mensagemErroCriarCartao\">\n            {{mensagemErroCriarCartao}}\n          </div>\n          <div class=\"alert alert-danger mt-2\" role=\"alert\" *ngIf=\"frmCriarNome.submitted && mensagemErroCriarCartao\" [innerHTML]=\"mensagemErroCriarCartao\">\n            {{mensagemErroCriarCartao}}\n          </div>\n        </div>\n      </form>\n    </div>\n    <div [hidden]=\"tela !== 'INFORMAR_CODIGO'\" class=\"mt-2\">\n    <div class=\"form-group mb-2 text-center\" [hidden]=\"!enviado || !mensagemAguarde\">\n      {{mensagemAguarde}}\n    </div>\n    <app-frm-valide-codigo [parent]=\"this\"></app-frm-valide-codigo>\n\n    <div style=\"text-align: center\" [hidden]=\"validouCodigo\">\n      <countdown class=\"countdown-grande\" [config]=\"{leftTime: 60, template: '$!m!:$!s!'}\" (finished)=\"onFinished()\"></countdown>\n      <br>\n      <a href=\"#\" (click)=\"reenviarCodigo();\" *ngIf=\"habiliteReenviarCodigo\"> Renviar Código </a>\n    </div>\n  </div>\n  </div>\n</div>\n<div class=\"cartao semborda\" >\n  <div class=\"botao azul\" (click)=\"verRegras() \" *ngIf=\"carregou\">\n    Confira as regras\n  </div>\n  <div class=\"botao cinza\"  *ngIf=\"carregando\">\n    Confira as regras\n  </div>\n</div>\n<div class=\"rodape\">\n  <div  class=\"logo\" [inlineSVG]=\"'/assets/fidelidade/logo-promokit-vertical.svg'\" ></div>\n</div>\n", "import {AfterViewInit, Component, Input, OnInit, ViewChild} from '@angular/core';\nimport {ClienteService} from \"../services/cliente.service\";\nimport {ActivatedRoute, Router} from \"@angular/router\";\nimport {EnumTipoDeCartao} from \"../../../server/lib/emun/EnumTipoDeCartao\";\nimport {UntypedFormBuilder, NgForm} from \"@angular/forms\";\nimport * as textMask from 'vanilla-text-mask/dist/vanillaTextMask.js';\nimport {CountdownComponent} from \"ngx-countdown\";\nimport {ContatosService} from \"../services/contatos.service\";\n\nimport {RegrasComponent} from \"../regras/regras.component\";\n\nimport {ArmazenamentoService} from \"../services/armazenamento.service\";\nimport {DialogRef, DialogService} from \"@progress/kendo-angular-dialog\";\nimport {CadBrindeComponent} from \"../compartilhado/fidelidade/cad-brinde/cad-brinde.component\";\nimport {KendoPopupUtils} from \"../lib/KendoPopupUtils\";\n\ndeclare var $;\n\n@Component({\n  selector: 'app-cliente',\n  templateUrl: './cliente.component.html',\n  styleUrls: ['./cliente.component.css']\n})\nexport class ClienteComponent implements OnInit, AfterViewInit {\n  @ViewChild('frm')  frm: NgForm;\n  @ViewChild('txtTelefone', {static: true}) private telefoneCliente: any;\n  @ViewChild(CountdownComponent, {static: true}) counter: CountdownComponent;\n  @Input() component = false;\n  carregando = true;\n  carregou = false;\n  idCliente: string;\n  mensagemDeErro: string;\n  cliente: any = {};\n  empresa: any;\n  tipoSelo: EnumTipoDeCartao = EnumTipoDeCartao.Selos;\n  tipoPontos: EnumTipoDeCartao = EnumTipoDeCartao.Pontos;\n  tipoReais: EnumTipoDeCartao = EnumTipoDeCartao.Reais;\n  tipo: EnumTipoDeCartao;\n  buscarContato: boolean;\n  dadosContato: any = {};\n  enviado: boolean;\n  enviando: boolean;\n  naoTemContato: boolean;\n  mensagemErroEnvio: string;\n  mask: Array<string | RegExp>;\n  maskedInputController;\n  mensagemAguarde: string;\n  mensagemFalhaEnvio: string;\n  habiliteReenviarCodigo: boolean = false;\n  validouCodigo = false;\n  selecionarCartao = false;\n  cartaoLink;\n  tela = 'INFORMAR_TELEFONE';\n  mensagemErroCriarCartao = '';\n  criandoCartao = false;\n  codigo = '';\n  contato: any;\n\n  constructor(private route: ActivatedRoute, private clienteService: ClienteService,\n              private dialogService: DialogService,\n              private fb: UntypedFormBuilder, private router: Router, private contatosService: ContatosService,\n              private armazenamentoService: ArmazenamentoService) {\n    this.mask = ['(', /\\d/, /\\d/, ')', ' ', /\\d/, '-', /\\d/, /\\d/, /\\d/, /\\d/, '-', /\\d/, /\\d/, /\\d/, /\\d/];\n  }\n\n  ngAfterViewInit(): void {\n    setTimeout(() => {\n      if( this.counter ) {\n        this.counter.stop();\n      }\n      if( this.telefoneCliente ) {\n        this.maskedInputController = textMask.maskInput({\n          inputElement: this.telefoneCliente.nativeElement,\n          mask: this.mask\n        });\n      }\n    });\n  }\n\n  ngOnInit() {\n    if(this.component) return;\n\n    let tokenCliente = this.route.snapshot.params.token;\n    let idCartao = this.route.snapshot.params.cartao;\n    if(this.route.snapshot.params.c)\n      this.codigo = this.route.snapshot.params.c;\n\n    if(!tokenCliente)\n      this.setCliente(this.route.snapshot.params.idCliente, idCartao);\n    else\n      this.confirmeClientePeloToken(tokenCliente, idCartao)\n  }\n\n  exibaCliente(cliente: any, empresa: any){\n    this.cliente = cliente;\n    this.empresa = empresa;\n    this.carregou = true;\n    this.carregando = false;\n    this.cliente.cartoes.forEach( (cartao) => {\n      cartao.link = this.router.url;\n      if(!cartao.link.endsWith(cartao.id))\n        cartao.link  =  cartao.link  +  '/' + cartao.id\n      cartao.plano.brindes =  cartao.plano.brindes.sort((brinde1, brinde2) => (brinde1.valorEmPontos > brinde2.valorEmPontos) ? 1 : -1)\n    });\n  }\n\n  private setCliente(idCliente, idCartao: string){\n      this.idCliente = idCliente || this.armazenamentoService.carregue(\"idCliente\", false);\n\n      this.clienteService.obtenhaCliente(this.idCliente).then((resposta) => {\n        this.exibaCliente(resposta.cliente, resposta.empresa);\n        this.cartaoLink = idCartao;\n    }).catch(mensagem => {\n      this.carregando = false;\n      this.carregou = true;\n      if(mensagem.empresa) {\n        this.empresa = mensagem.empresa;\n        this.buscarContato = true;\n      }\n      else\n        this.mensagemDeErro = \"Houve um erro ao carregar os dados do cliente: \" + mensagem;\n    })\n  }\n  private confirmeClientePeloToken(token, idCartao){\n    this.clienteService.confirmarContato(token).then(contato => {\n      this.armazenamentoService.salveSemExpirar(\"idCliente\", contato.id);\n      this.armazenamentoService.salveSemExpirar(contato.id , idCartao);\n      this.cartaoLink = idCartao;\n      this.setCliente(contato.id, idCartao);\n      this.codigo = 'criado';\n    }).catch(mensagem => {\n      this.carregando = false;\n      this.carregou = false;\n      this.mensagemDeErro = mensagem;\n    })\n  }\n\n  verRegras() {\n\n    const windowRef: DialogRef = this.dialogService.open({\n      title: null,\n      content: RegrasComponent,\n      minWidth: 250,\n      width: window.innerWidth > 700 ? 700 : window.innerWidth,\n      maxHeight: window.innerHeight - 100,\n      cssClass: 'bsModal'\n    });\n\n\n    KendoPopupUtils.abriuPopupNgBootstrap(windowRef)\n\n  }\n\n  solicitarBrinde() {\n\n  }\n\n  onSubmit() {\n    this.enviando = true;\n    if ( !this.frm.valid ) {\n      this.mensagemErroEnvio = \"Existem erros no preenchimento. Por favor, verifique e tente novamente!\";\n      window.scrollTo(0, 0);\n      this.enviando = false;\n      return;\n    }\n    this.mensagemDeErro = null;\n    this.mensagemErroEnvio = null;\n    this.mensagemFalhaEnvio = null;\n    let dados = this.dadosContato;\n    this.clienteService.obtenhaClientePorTelefone(this.unmask(dados.telefone)).then((resposta: any) => {\n      console.log(resposta)\n      let contato = resposta.cliente\n\n      if(contato) {\n        this.enviando = false;\n        this.contato = contato;\n\n        if( this.contato.id ) {\n          this.armazenamentoService.salveSemExpirar(\"idCliente\", contato.id);\n          this.validouCliente();\n          const url = '/cliente?t=' + new Date().getTime();\n\n          window.location.href = url;\n        }\n      } else {\n        this.enviando = false;\n        this.validouTelefone();\n      }\n    }).catch((mensagem) => {\n      //Nesse fluxo, significa que estará criando um novo cartão\n      this.enviando = false;\n      this.mensagemFalhaEnvio = mensagem;\n    });\n  }\n\n  unmask(val) {\n    return val.replace(/\\D+/g, '');\n  }\n  onFinished() {\n    this.habiliteReenviarCodigo = true;\n    this.mensagemAguarde = null;\n    this.counter.config = {\n      leftTime: 60,\n      demand: false\n    }\n    return false;\n  }\n\n  reenviarCodigo() {\n    this.enviado = false;\n    this.habiliteReenviarCodigo = false;\n    return false;\n  }\n\n  validouCliente() {\n    this.validouCodigo = true;\n  }\n\n  onSubmitCriarCartao() {\n    this.criandoCartao =  true;\n\n    this.contatosService.salveNovoCartao({\n      contato: {\n        nome: this.dadosContato.nome,\n        telefone: this.unmask(this.dadosContato.telefone),\n        genero: this.dadosContato.genero,\n        dataNascimento: this.dadosContato.dataNascimento\n      }\n    }).then( resp =>   {\n      this.criandoCartao = false;\n      console.log('resposta');\n\n      console.log(resp);\n      this.armazenamentoService.salveSemExpirar(\"idCliente\", resp.id);\n\n      const url = '/cliente?c=criado&t=' + new Date().getTime();\n\n      window.location.href = url;\n    }).catch(erro => {\n      this.criandoCartao = false;\n    });\n  }\n\n  validouTelefone() {\n    this.tela = 'INFORMAR_NOME';\n  }\n\n  exibaExtrato() {\n\n  }\n}\n", "<div class=\"account-pages mt-5 mb-5\">\n  <div class=\"container\">\n    <div class=\"row justify-content-center\">\n      <div class=\"col-md-8 col-lg-6 col-xl-5\">\n        <div class=\"card bg-pattern\">\n\n          <div class=\"card-body p-4\">\n\n            <div class=\"text-center w-75 m-auto\">\n              <a href=\"index.html\">\n                <span><img src=\"assets/images/logo-dark.png\" alt=\"\" height=\"22\"></span>\n              </a>\n            </div>\n\n            <div class=\"mt-3 text-center\" *ngIf=\"confirmando\">\n              <img alt=\"\" src=\"/assets/fidelidade/icones/loyalty_card.svg\">\n\n              <h3>Ativando</h3>\n              <p class=\"text-muted font-14 mt-2\"> Aguarde enquanto ativamos seu cartão... </p>\n\n              <!--<a href=\"index.html\" class=\"btn btn-block btn-pink waves-effect waves-light mt-3\">Back to Home</a>-->\n            </div>\n\n            <div class=\"mt-3 text-center\"  *ngIf=\"!confirmando && confirmou\">\n              <img alt=\"\" src=\"/assets/fidelidade/icones/loyalty_card.svg\">\n\n              <h3>Parabéns, {{contato.nome}}!</h3>\n              <p class=\"text-muted font-14 mt-2\"> Seu cartão foi ativado com sucesso! <a [routerLink]=\"'/cliente'\">Clique aqui</a> para ver seu cartão!</p>\n\n              <!--<a href=\"index.html\" class=\"btn btn-block btn-pink waves-effect waves-light mt-3\">Back to Home</a>-->\n            </div>\n\n            <div class=\"mt-3 text-center\" *ngIf=\"!confirmando && !confirmou\">\n              <img alt=\"\" src=\"/assets/fidelidade/icones/loyalty_card.svg\">\n\n              <h3>Falha</h3>\n              <p class=\"text-muted font-14 mt-2\"> Houve um erro ao tentar ativar o cartão: {{mensagemErro}} </p>\n\n              <!--<a href=\"index.html\" class=\"btn btn-block btn-pink waves-effect waves-light mt-3\">Back to Home</a>-->\n            </div>\n\n\n\n          </div> <!-- end card-body -->\n        </div>\n        <!-- end card -->\n\n      </div> <!-- end col -->\n    </div>\n    <!-- end row -->\n  </div>\n  <!-- end container -->\n</div>\n", "import {Component, Input, OnInit} from '@angular/core';\nimport {ClienteService} from \"../services/cliente.service\";\nimport {ActivatedRoute} from \"@angular/router\";\nimport {ArmazenamentoService} from \"../services/armazenamento.service\";\n\n@Component({\n  selector: 'app-confirmar',\n  templateUrl: './confirmar.component.html',\n  styleUrls: ['./confirmar.component.scss']\n})\nexport class ConfirmarComponent implements OnInit {\n  confirmando = true;\n  private idCartao: string;\n  public contato: any;\n  public mensagemErro: string;\n  confirmou = false;\n\n  constructor(private clienteService: ClienteService, private activatedRoute: ActivatedRoute,\n              private armazenamentoService: ArmazenamentoService) {\n    let params: any = this.activatedRoute.snapshot.params;\n    this.idCartao = params.idCartao;\n  }\n\n  ngOnInit() {\n\n    this.clienteService.confirmarContato(this.idCartao).then(contato => {\n      this.confirmando = false;\n      this.confirmou = true;\n      this.contato  = contato;\n\n      this.armazenamentoService.salveSemExpirar(\"idCliente\", contato.id);\n    }).catch(mensagem => {\n      this.confirmando = false;\n      this.confirmou = false;\n      this.mensagemErro = mensagem;\n    })\n  }\n}\n", "import { NgModule } from '@angular/core';\nimport {RouterModule, Route, UrlSegment, UrlSegmentGroup} from '@angular/router';\nimport {SiteEmpresaComponent} from \"./site-empresa.component\";\nimport {ClienteComponent} from \"../cliente/cliente.component\";\nimport {ConfirmarComponent} from \"../confirmar/confirmar.component\";\nimport {AuthGuardService} from \"../guards/auth-guard.service\";\nimport {SiteEmpresaRouterComponent} from \"./site-empresa-router.component\";\nimport {MeusCartoesComponent} from \"../cliente/meus-cartoes/meus-cartoes.component\";\n\nconst routes: Route[] = [\n  {\n    path:  '',\n    component: SiteEmpresaRouterComponent,\n    children: [\n      { path: 'cliente', component: ClienteComponent},\n      { path: ':empresa', component: ClienteComponent},\n      { path: 'cliente/ativar/:token', component: ClienteComponent},\n      { path: 'cliente/:numero/extrato/:token', component: MeusCartoesComponent},\n      { path: 'cliente/:idCliente', canActivate: [AuthGuardService], component: ClienteComponent},\n      { path: 'cliente/:idCliente/:cartao', canActivate: [AuthGuardService], component: ClienteComponent},\n      { path: 'confirmar/:idCartao', component: ConfirmarComponent},\n      { path: '', component:  SiteEmpresaComponent},\n    ]\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class SiteEmpresaRoutingModule { }\n", "import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-site-empresa-router',\n  templateUrl: './site-empresa-router.component.html'\n\n})\nexport class SiteEmpresaRouterComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<router-outlet></router-outlet>\n", "import {Component, OnInit, ViewChild} from '@angular/core';\nimport {ActivatedRoute} from \"@angular/router\";\nimport {NgForm} from \"@angular/forms\";\nimport {ClienteComponent} from \"../cliente.component\";\nimport {ClienteService} from \"../../services/cliente.service\";\n\n@Component({\n  selector: 'app-meus-cartoes',\n  templateUrl: './meus-cartoes.component.html',\n  styleUrls: ['./meus-cartoes.component.scss']\n})\nexport class MeusCartoesComponent implements OnInit {\n  @ViewChild('telaCliente', {static: true})  telaCliente: ClienteComponent;\n  cliente: any = {};\n\n  constructor(private route: ActivatedRoute,  private clienteService: ClienteService) { }\n\n  ngOnInit() {\n    let token = this.route.snapshot.params.token;\n    let numero = this.route.snapshot.params.numero;\n\n    this.clienteService.obtenhaClientePorTelefone(numero, token).then( resposta => {\n      if(resposta.cliente){\n        this.telaCliente.exibaCliente(resposta.cliente  , resposta.empresa );\n        this.telaCliente.exibaExtrato();\n      }\n\n\n    })\n  }\n\n}\n", "<app-cliente #telaCliente [component]=\"true\"></app-cliente>\n", "<div class=\"account-pages mt-5 mb-5\">\n  <div class=\"container\">\n    <div class=\"row justify-content-center\">\n      <div class=\"col-12\">\n        <div class=\"card bg-pattern\">\n\n          <div class=\"card-body p-4\">\n\n            <div class=\"text-center w-75 m-auto\">\n              <a href=\"index.html\">\n                <span><img src=\"assets/images/logo-dark.png\" alt=\"\" height=\"22\"></span>\n              </a>\n            </div>\n\n            <div class=\"mt-3 text-center\" *ngIf=\"confirmando\">\n              <img alt=\"\" src=\"/assets/fidelidade/icones/loyalty_card.svg\">\n\n              <h3>Ativando</h3>\n              <p class=\"text-muted font-14 mt-2\"> Aguarde enquanto ativamos seu cartão... </p>\n\n              <!--<a href=\"index.html\" class=\"btn btn-block btn-pink waves-effect waves-light mt-3\">Back to Home</a>-->\n            </div>\n\n            <div class=\"mt-3 text-center\"  *ngIf=\"!confirmando && confirmou\">\n              <img alt=\"\" src=\"/assets/fidelidade/icones/loyalty_card.svg\">\n\n              <h3>Parabéns, {{contato.nome}}!</h3>\n              <p class=\"text-muted font-14 mt-2\"> Seu cartão foi ativado com sucesso! <a href=\"/cliente\">Clique aqui</a> para ver seu cartão!</p>\n\n              <!--<a href=\"index.html\" class=\"btn btn-block btn-pink waves-effect waves-light mt-3\">Back to Home</a>-->\n            </div>\n\n            <div class=\"mt-3 text-center\" *ngIf=\"!confirmando && !confirmou\">\n              <img alt=\"\" src=\"/assets/fidelidade/icones/loyalty_card.svg\">\n\n              <h3>Falha</h3>\n              <p class=\"text-muted font-14 mt-2\"> Houve um erro ao tentar ativar o cartão: {{mensagemErro}} </p>\n\n              <!--<a href=\"index.html\" class=\"btn btn-block btn-pink waves-effect waves-light mt-3\">Back to Home</a>-->\n            </div>\n\n\n\n          </div> <!-- end card-body -->\n        </div>\n        <!-- end card -->\n\n      </div> <!-- end col -->\n    </div>\n    <!-- end row -->\n  </div>\n  <!-- end container -->\n</div>\n", "import {Component, Input, OnInit} from '@angular/core';\n\n@Component({\n  selector: 'app-cartao-confirmado',\n  templateUrl: './cartao-confirmado.component.html',\n  styleUrls: ['./cartao-confirmado.component.scss']\n})\nexport class CartaoConfirmadoComponent implements OnInit {\n  confirmando = false;\n  private idCartao: string;\n\n  @Input()\n  contato: any;\n\n  mensagemErro: string;\n  @Input()\n  confirmou = false;\n\n  constructor() {\n  }\n\n  ngOnInit() {\n  }\n}\n", "<div *ngIf=\"contato == null\" class=\"mt-2\">\n  <div class=\"card\">\n    <div class=\"card-body\" style=\"padding: 0.8rem;\">\n      <label>Seu telefone</label>\n\n      <div id=\"cardCollpase1\" class=\"collapse show\">\n        <div>\n          <div class=\"row mt-1\">\n            <div class=\"col\">\n              <h4 class=\"mt-0\"><span style=\"font-weight: bold;\">{{parent.dadosContato.telefone}}</span></h4>\n            </div>\n          </div> <!-- end row -->\n        </div>\n      </div> <!-- collapsed end -->\n    </div> <!-- end card-body -->\n  </div>\n\n  <h2>Verificar Código</h2>\n\n  Informe o código que enviamos para o seu celular\n\n  <form [ngClass]=\"{'needs-validation': !frmValidarCodigo.submitted, 'was-validated': frmValidarCodigo.submitted}\"\n        novalidate #frmValidarCodigo=\"ngForm\" (ngSubmit)=\"onSubmitValidarCodigo()\">\n    <div class=\"form-group mb-2\" >\n      <input [(ngModel)]=\"codigo\" #ctrlCodigo=\"ngModel\" #txtCodigo  type=\"text\" appAutoFocus class=\"form-control ng-tns-c12-1 ui-inputtext ui-widget ui-state-default ui-corner-all\"\n             placeholder=\"______\" required name=\"codigo\"/>\n      <div class=\"invalid-feedback\">\n        <p *ngIf=\"ctrlCodigo.errors?.required\">Código é obrigatório</p>\n      </div>\n    </div>\n    <div class=\"form-group mb-2 text-center\">\n      <button class=\"btn btn-block\" type=\"submit\" [disabled]=\"enviando || (frmValidarCodigo.submitted && mensagemAguarde)\"\n              [ngClass]=\"{disabled: enviando || (frmValidarCodigo.submitted && mensagemAguarde)}\"> Verificar Código </button>\n      <div class=\"alert alert-danger mt-2\" role=\"alert\" *ngIf=\"mensagemErroEnvio && !frmValidarCodigo.valid\" [innerHTML]=\"mensagemErroEnvio\">\n        {{mensagemErroEnvio}}\n      </div>\n      <div class=\"alert alert-danger mt-2\" role=\"alert\" *ngIf=\"mensagemFalhaEnvio\" [innerHTML]=\"mensagemFalhaEnvio\">\n        {{mensagemErroEnvio}}\n      </div>\n    </div>\n    <div [hidden]=\"frmValidarCodigo.submitted && mensagemAguarde\">\n      Se ainda não possui cartão, visite nossa loja e crie seu cartão agora mesmo!\n    </div>\n  </form>\n</div>\n<app-cartao-confirmado *ngIf=\"contato\" [contato]=\"contato\" [confirmou]=\"contato != null\"></app-cartao-confirmado>\n", "import {AfterViewInit, Component, Input, OnInit, ViewChild} from '@angular/core';\nimport {NgForm} from \"@angular/forms\";\nimport * as textMask from \"vanilla-text-mask/dist/vanillaTextMask\";\nimport {ClienteService} from \"../services/cliente.service\";\nimport {ClienteComponent} from \"../cliente/cliente.component\";\nimport {ArmazenamentoService} from \"../services/armazenamento.service\";\n\n@Component({\n  selector: 'app-frm-valide-codigo',\n  templateUrl: './frm-valide-codigo.component.html',\n  styleUrls: ['./frm-valide-codigo.component.scss']\n})\nexport class FrmValideCodigoComponent implements OnInit, AfterViewInit {\n  @ViewChild('frmValidarCodigo')  frmValidarCodigo: NgForm;\n  @ViewChild('txtCodigo') private txtCodigo: any;\n\n  public maskCodigo: Array<string | RegExp>;\n  mensagemAguarde: string;\n  mensagemFalhaEnvio: string;\n  codigo = '';\n  private maskedInputController2: any;\n  mensagemErroEnvio = '';\n  enviando = false;\n  confirmando = false;\n  contato: any;\n  confirmou = true;\n\n  @Input()\n  parent: ClienteComponent;\n\n  constructor(private clienteService: ClienteService,\n              private armazenamentoService: ArmazenamentoService) {\n    this.maskCodigo = [/\\d/, /\\d/, /\\d/, /\\d/, /\\d/, /\\d/];\n  }\n\n  ngOnInit() {\n  }\n\n  ngAfterViewInit() {\n    setTimeout( () => {\n      this.maskedInputController2 = textMask.maskInput({\n        inputElement: this.txtCodigo.nativeElement,\n        mask: this.maskCodigo\n      });\n    });\n  }\n\n  onSubmitValidarCodigo() {\n    this.enviando = true;\n\n    if ( !this.frmValidarCodigo.valid ) {\n      this.mensagemErroEnvio = \"Existem erros no preenchimento. Por favor, verifique e tente novamente!\";\n      window.scrollTo(0, 0);\n      this.enviando = false;\n      return;\n    }\n\n    this.clienteService.valideCodigo(this.unmask(this.codigo),\n      this.parent.unmask(this.parent.dadosContato.telefone)).then((contato: any) => {\n      this.enviando = false;\n      this.contato = contato;\n\n      if( this.contato.id ) {\n        this.armazenamentoService.salveSemExpirar(\"idCliente\", contato.id);\n        this.parent.validouCliente();\n\n        this.mensagemAguarde =\n          \"Você receberá uma mensagem com um código para você acessar seu cartão. Informe o código no campo abaixo:\";\n      } else {\n        this.parent.validouTelefone();\n      }\n    }).catch((mensagem) => {\n      this.enviando = false;\n      //this.mensagemErroEnvio = mensagem;\n      this.mensagemFalhaEnvio = mensagem;\n    });\n  }\n\n  unmask(val) {\n    return val.replace(/\\D+/g, '');\n  }\n}\n", "<div class=\"list-unstyled timeline-sm\">\n  <div *ngFor=\"let acao of acoes\" class=\"timeline-sm-item\" >\n    <span class=\"timeline-sm-date horario\">{{acao.horarioAbreviado  }}\n      <span class=\"text-muted\" *ngIf=\"acao.ano\"><br>{{acao.ano}}</span>\n    </span>\n\n    <div class=\"row\"  >\n      <div  class=\"col-1\">\n        <app-icone-acao [acao]=\"acao\" [exibirDescricao]=\"false\"></app-icone-acao>\n      </div>\n      <div  class=\"col-11 text-muted\">\n\n        <span>{{acao.descricao}}  </span>\n        <div [ngClass]=\"{'text-success': acao.credito, 'text-danger': acao.debito}\">\n          <b>\n            <span *ngIf=\"acao.debito\">-</span>\n            {{acao.acumulado }}</b>\n        </div>\n\n      </div>\n\n    </div>\n\n  </div>\n\n\n</div>\n\n<button class=\"btn btn-block  btn-rounded  btn-outline-light mt-2\" (click)=\"carregueMais()\" *ngIf=\"!carregouTodos\">ver mais</button>\n<button class=\"btn btn-block  btn-rounded  btn-outline-light mt-2\"   *ngIf=\"carregouTodos\">isso é tudo</button>\n\n\n", "import {Component, Input, OnInit} from '@angular/core';\nimport {ContatosService} from \"../../services/contatos.service\";\ndeclare  var _;\n@Component({\n  selector: 'app-extrato-pontos',\n  templateUrl: './extrato-pontos.component.html',\n  styleUrls: ['./extrato-pontos.component.scss']\n})\nexport class ExtratoPontosComponent implements OnInit {\n  @Input() cartao = {}\n\n  acoes: any = [];\n  carregando = true;\n  inicio = 0;\n  total = 10;\n  carregouTodos  = false;\n  constructor(private contatosService: ContatosService) { }\n\n  ngOnInit() {\n    if(!this.acoes.length){\n      this.carregueAcoes();\n    }\n  }\n\n  carregueAcoes(){\n    this.contatosService.obtenhaUltimasAcoes(null, this.cartao, this.inicio, this.total, true).then( (resposta) => {\n      resposta.forEach( acao => this.acoes.push(acao))\n      this.carregouTodos =   resposta.length < this.total;\n\n    })\n  }\n\n  carregueMais() {\n    this.inicio += this.total;\n    this.carregueAcoes();\n  }\n}\n", "<div class=\"row\">\n  <div class=\"col-2\">\n\n  </div>\n  <div class=\"col-10\">\n\n    <div class=\"form-group\">\n      <label>   Filtrar por período:</label>\n      <kendo-dropdownlist  name=\"periodo\" [(ngModel)]=\"periodo\" [data]=\"periodos\" textField=\"descricao\"\n                             class=\"form-control\"  (ngModelChange)=\"busquePontuacaoVencidas()\">\n      </kendo-dropdownlist>\n\n    </div>\n  </div>\n</div>\n\n\n\n<table class=\"table table-striped table-sm\">\n     <thead>\n      <tr>\n        <th>Data da expiração</th>\n        <th  >\n          {{ cartao.plano.tipoDeAcumulo }}\n        </th>\n      </tr>\n     </thead>\n     <tbody *ngIf=\"pontuacaoVencer.length==0 && buscou\">\n        <tr>\n          <td colspan=\"2\">\n            <p >Não há pontos a expirar para período selecionado</p>\n          </td>\n        </tr>\n     </tbody>\n     <tbody>\n       <tr *ngFor=\"let pontuacao of pontuacaoVencer.pontuacoes\" class=\"timeline-sm-item\" >\n         <td class=\"horario\">\n           {{pontuacao.dataVencimento | date: 'dd/MM/yyyy'  }}\n         </td>\n         <td  >\n           <b>{{getPontos(pontuacao.pontos)}}</b>\n         </td>\n       </tr>\n     </tbody>\n</table>\n", "import {Component, Input, OnInit} from '@angular/core';\nimport {ContatosService} from \"../../services/contatos.service\";\nimport {formatCurrency, formatNumber} from \"@angular/common\";\ndeclare var moment;\n@Component({\n  selector: 'app-cartao-proximos-vencimentos',\n  templateUrl: './cartao-proximos-vencimentos.component.html',\n  styleUrls: ['./cartao-proximos-vencimentos.component.scss']\n})\nexport class CartaoProximosVencimentosComponent implements OnInit {\n  @Input() cartao: any  = {};\n  pontuacaoVencer: any = []\n  buscou = false;\n  periodo: any = {}\n  periodos: any = [  ];\n  formato = 'YYYYMMDD'\n  constructor(private contatosService: ContatosService) { }\n\n  ngOnInit() {\n\n    if(this.cartao.plano.validade){\n      // tslint:disable-next-line:radix\n        this.adicionePeriodosFiltro(this.cartao.plano.validade)\n\n    } else {\n      let final = moment(this.cartao.plano.vencimento);\n      let dias = moment(final).diff( moment() , 'd');\n\n      this.adicionePeriodosFiltro(dias)\n    }\n\n    this.periodo = this.periodos[this.periodos.length - 1];\n\n    this.busquePontuacaoVencidas();\n  }\n\n  adicionePeriodosFiltro(dias: any){\n\n    let periodo =  dias > 29 ?   Math.round(dias / 3 ) :  dias;\n\n    this.periodos.push({ descricao: periodo + \" dias\",\n      value: { inicio: 0, fim: moment().add(periodo, 'd').format(this.formato) }})\n\n    if(dias > 29){\n      this.periodos.push({ descricao: periodo * 2 + \" dias\",\n        value: { inicio: 0, fim: moment().add(periodo * 2, 'd').format(this.formato) }})\n      this.periodos.push({ descricao: periodo * 3 + \" dias\" ,\n        value: { inicio: 0, fim: moment().add(periodo * 3, 'd').format(this.formato) }})\n    }\n\n  }\n\n  getPontos(pontos: any) {\n\n    if(this.cartao && this.cartao.plano.tipoDeAcumulo === 'Reais'){\n      // @ts-ignore\n      return formatCurrency(pontos, 'pt-BR', 'R$');\n    }\n\n    return formatNumber(Math.floor(pontos), 'pt-BR');\n  }\n\n  busquePontuacaoVencidas() {\n    this.contatosService.obtenhaPontuacaoVencer(this.cartao, this.periodo.value.inicio, this.periodo.value.fim).then(resposta => {\n      this.pontuacaoVencer = resposta;\n      this.buscou = true;\n    })\n  }\n}\n", "<a routerLink=\"{{cartao.link}}\"><h4>{{cartao.plano?.tituloCartao}}</h4></a>\n<div class=\"linha\">\n  <div class=\"row\">\n    <div class=\"col\">\n      <div class=\"pontos\">\n\n        <div class=\"pontos-interno\">\n          <div class=\"icone estrela\" [inlineSVG]=\"'/assets/fidelidade/icones/star-icone.svg'\" [removeSVGAttributes]=\"['fill']\"></div>\n          {{cartao.pontos | number}}\n        </div>\n      </div>\n\n\n    </div>\n  </div>\n\n</div>\n\n<kendo-tabstrip class=\"nav-bordered\"  #tabs id=\"tabs\">\n\n  <kendo-tabstrip-tab [title]=\"'Brindes'\"  [id]=\"'brindes'\" [selected]=\"true\">\n    <ng-template kendoTabContent>\n\n      <div class=\"row margem-fim\">\n        <div class=\"col\">\n          <div class=\"minimo-troca\">\n            Troque por brindes a partir de {{cartao.plano.brindes[0].valorEmPontos}} pontos.\n          </div>\n        </div>\n\n      </div>\n\n      <div class=\"container-scroll\">\n        <div class=\"row flex-row flex-nowrap\">\n          <div class=\"col-sm-8 col-10\" *ngFor=\"let brinde of cartao.plano.brindes\">\n            <div class=\"brinde linha\" >\n\n              <div class=\"caixa_brinde\">\n                <div class=\"titulo\">\n                  <p class=\"nome_brinde_pontos\"  [ngClass]=\"{'font11':brinde.nome.length > 50}\" >{{brinde.nome}}</p>\n                  <p class=\"preco_troca\" [ngClass]=\"{  'nao_atingiu': brinde.valorEmPontos > cartao.pontos,\n                                                 'atingiu': brinde.valorEmPontos <= cartao.pontos }\">\n                    {{brinde.valorEmPontos}} pontos\n                  </p>\n                </div>\n                <div class=\"container_imagem\">\n                  <div class=\"\">\n                    <img class=\"foto_brinde\" src=\"https://fibo.promokit.com.br/images/empresa/{{brinde.linkImagem}}\">\n                  </div>\n\n                </div>\n\n              </div>\n              <div *ngIf=\"brinde.valorEmPontos > cartao.pontos\">\n                <div class=\"botao_troca   faltam_selos\">Faltam {{getPontosRestantes(brinde,cartao)}} pontos</div>\n              </div>\n              <div *ngIf=\"brinde.valorEmPontos <= cartao.pontos\">\n\n                <div class=\"botao_troca pode_trocar cpointer\" (click)=\"solicitarBrinde(brinde, cartao )\">\n                  <div class=\"icone estrela\" [inlineSVG]=\"'/assets/fidelidade/icones/star-icone.svg'\" [removeSVGAttributes]=\"['fill', 'heigth', 'width']\"></div>\n                  Solicitar brinde\n                </div>\n              </div>\n\n            </div>\n\n\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  </kendo-tabstrip-tab>\n\n  <kendo-tabstrip-tab [title]=\"'Extrato'\"  [id]=\"'extrato'\">\n    <ng-template kendoTabContent>\n\n      <app-extrato-pontos [cartao]=\"cartao\"  ></app-extrato-pontos>\n\n    </ng-template>\n  </kendo-tabstrip-tab>\n\n  <kendo-tabstrip-tab [title]=\" 'Expirando'\"  [id]=\"'expirando'\" *ngIf=\"cartao.plano.validade || cartao.plano.vencimento\" id=\"3\" >\n    <ng-template kendoTabContent>\n\n      <app-cartao-proximos-vencimentos [cartao]=\"cartao\"  ></app-cartao-proximos-vencimentos>\n\n    </ng-template>\n  </kendo-tabstrip-tab>\n\n</kendo-tabstrip>\n\n", "import { Component, Input, OnInit, ViewChild, Directive } from '@angular/core';\nimport {Router} from \"@angular/router\";\nimport { TabStripComponent } from '@progress/kendo-angular-layout';\n\n@Directive()\nexport class TelaCartaoComTabs  {\n  @ViewChild('tabs', {static: true}) tabs: TabStripComponent;\n\n  constructor() {\n    if(location.pathname.indexOf('/extrato/') > 0){\n      this.selecioneTabExtrato();\n    }\n  }\n  selecioneTabExtrato(){\n    setTimeout ( () => {\n      this.tabs.selectTab(1);\n    }, 0)\n  }\n}\n\n\n@Component({\n  selector: 'app-cartao-pontos',\n  templateUrl: './cartao-pontos.component.html',\n  styleUrls: ['./cartao-pontos.component.scss']\n})\nexport class CartaoPontosComponent extends TelaCartaoComTabs implements OnInit {\n  @Input() cartao: any = { pontos: 0  }\n  @Input() empresa: any = {    }\n\n  constructor(private router: Router) { super() }\n  ngOnInit() {\n    this.configurePontos();\n  }\n\n  solicitarBrinde(brinde: any, cartao: any) {\n\n    let descricaoPontos = cartao.plano.tipoDeAcumulo.toLowerCase();\n\n    let texto = String(`Gostaria de trocar meus *${descricaoPontos}* pelo brinde *${brinde.nome}*`)\n\n    let link = String(`https://wa.me/55${this.empresa?.whatsapp}?text=${texto}`)\n\n   window.open(link)\n  }\n\n  private configurePontos() {  }\n\n  getPontosRestantes(brinde: any, cartao: any) {\n    let pontosRestantes: number =    brinde.valorEmPontos - cartao.pontos ;\n\n\n   return pontosRestantes.toFixed(2);\n  }\n\n\n}\n\n", "<a routerLink=\"{{cartao.link}}\"><h4>{{cartao.plano?.tituloCartao}}</h4></a>\n<div  *ngFor=\"let brinde of cartao.plano?.brindes; let indice = index\">\n  <div class=\"slide\">\n    <div class=\"pontuacao \"  >\n      <div class=\"row\" [ngClass]=\"{\n          'mb-1': i < obtenhaQuantidadeDeLinhas ( brinde) - 1,\n          'mb-2': i == obtenhaQuantidadeDeLinhas ( brinde) -1\n      }\" *ngFor=\"let selos of listaMatrizSelos[indice]; let i = index\">\n\n        <div class=\"col\" *ngFor=\"let selo of selos\" style=\"padding-left: 0px;padding-right: 0px;\">\n          <div class=\"container_selo\" [ngClass]=\"{\n                'on': selo.status == 1,\n                'off': selo.status == 0,\n                'sem': selo.status == -1\n              }\">\n            <div class=\"icone selo\" [inlineSVG]=\"'/assets/fidelidade/icones/icon-selo.svg'\" [removeSVGAttributes]=\"['fill']\"></div>\n            <div class=\"valor\"><span>{{selo.valor}}</span></div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"brinde linha\"  >\n      <span class=\"preco_troca\">Atinja {{brinde.valorEmPontos}}  selos e troque por {{brinde.artigo}}</span>\n      <div>\n        <div class=\"nome_brinde\">{{brinde.nome}}</div>\n        <img class=\"foto_brinde\" src=\"/images/empresa/{{brinde.linkImagem}}\">\n        <div *ngIf=\"brinde.valorEmPontos > cartao.pontos\">\n          <div class=\"faltam_selos\">Junte mais {{brinde.valorEmPontos - cartao.pontos  == 1 ? '1 selo' : brinde.valorEmPontos - cartao.pontos + ' selos'}}</div>\n        </div>\n        <div *ngIf=\"brinde.valorEmPontos <= cartao.pontos\">\n          <div class=\"faltam_selos\">Parabéns! Você já pode fazer a troca!</div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n</div>\n\n<kendo-tabstrip class=\"nav-bordered mt-2\"  >\n  <kendo-tabstrip-tab [title]=\" 'Extrato' \"  [selected]=\"true\">\n    <ng-template kendoTabContent>\n\n      <app-extrato-pontos [cartao]=\"cartao\"  ></app-extrato-pontos>\n\n    </ng-template>\n  </kendo-tabstrip-tab>\n\n  <kendo-tabstrip-tab [title]=\" 'Expirando' \" *ngIf=\"cartao.plano.validade || cartao.plano.vencimento\" >\n    <ng-template kendoTabContent>\n\n      <app-cartao-proximos-vencimentos [cartao]=\"cartao\"  ></app-cartao-proximos-vencimentos>\n\n    </ng-template>\n  </kendo-tabstrip-tab>\n\n</kendo-tabstrip>\n", "import {Component, Input, OnInit} from '@angular/core';\nimport {Brinde} from \"../../../../server/domain/obj/Brinde\";\nimport {Router} from \"@angular/router\";\n\n@Component({\n  selector: 'app-cartao-selo',\n  templateUrl: './cartao-selo.component.html',\n  styleUrls: ['./cartao-selo.component.scss']\n})\nexport class CartaoSeloComponent implements OnInit {\n  @Input() cartao: any = { pontos: 0}\n\n  matrizSelos: Array<Array<any>>;\n  listaMatrizSelos = [];\n  constructor(private router: Router) { }\n\n  ngOnInit() {\n    this.configureSelos();\n  }\n\n  obtenhaMatrizSelos(inicio, pontosSobrando, minimoParaTroca: number): Array<Array<any>> {\n    let quantidadeLinhas = Math.ceil(minimoParaTroca / 5);\n    let selosPorLinha = Math.ceil(minimoParaTroca / quantidadeLinhas)\n    this.matrizSelos = [];\n\n    if(quantidadeLinhas > 10)\n      quantidadeLinhas = 10\n\n    for(let linha = 0; linha < quantidadeLinhas; linha++) {\n      let selos = [];\n\n      for (let coluna = 0; coluna < selosPorLinha; coluna++)  {\n        let i = coluna + (linha * selosPorLinha);\n        let selo = {\n          status: 1,\n          valor: inicio + i + 1\n        }\n\n        if(i < pontosSobrando)\n          selo.status = 1;\n        else if(i < minimoParaTroca)\n          selo.status = 0;\n        else\n          selo.status = -1;\n\n        selos.push(selo)\n      }\n\n      if(selos.length === 2) {\n        let selo = {\n          status: -1,\n          valor: 0\n        }\n\n        selos.unshift(selo);\n\n        selo = {\n          status: -1,\n          valor: 0\n        }\n\n        selos.push(selo)\n      }\n\n      this.matrizSelos.push(selos);\n    }\n\n\n    return this.matrizSelos;\n  }\n\n  private configureSelos() {\n    this.listaMatrizSelos = [];\n\n    if(!this.cartao.plano.brindes.length) return;\n\n    let pontosNecessarios = this.cartao.plano.brindes[0].valorEmPontos;\n    let pontosQueJaPossui = this.cartao.pontos > pontosNecessarios ? pontosNecessarios : this.cartao.pontos;\n    let pontosRestantes = pontosQueJaPossui === pontosNecessarios ? this.cartao.pontos - pontosNecessarios : 0;\n    let totalPontos = 0;\n    this.listaMatrizSelos.push(this.obtenhaMatrizSelos(totalPontos, pontosQueJaPossui, pontosNecessarios));\n\n    totalPontos = pontosNecessarios;\n\n    for (let i = 1; i < this.cartao.plano.brindes.length; i++) {\n      pontosNecessarios = this.cartao.plano.brindes[i].valorEmPontos - totalPontos;\n      pontosQueJaPossui = pontosRestantes > pontosNecessarios ? pontosNecessarios : pontosRestantes\n      pontosRestantes = pontosRestantes > pontosNecessarios ? pontosRestantes - pontosNecessarios : 0\n\n\n      this.listaMatrizSelos.push(this.obtenhaMatrizSelos(totalPontos, pontosQueJaPossui, pontosNecessarios));\n      totalPontos = this.cartao.plano.brindes[i].valorEmPontos;\n    }\n  }\n  obtenhaQuantidadeDeLinhas(brinde: Brinde) {\n    return Math.ceil(brinde.valorEmPontos / 5);\n  }\n}\n", "<a routerLink=\"{{cartao.link}}\"><h4><PERSON><PERSON><PERSON> {{cartao.plano?.nome}}</h4></a>\n\n<div class=\"   mb-2\">\n  <div class=\"row\">\n    <div class=\"col\">\n      <div class=\"pontos\">\n\n        <div class=\"pontos-interno\">\n          <div class=\"icone estrela\" [inlineSVG]=\"'/assets/fidelidade/icones/star-icone.svg'\" [removeSVGAttributes]=\"['fill']\"></div>\n          {{cartao.pontos |  currency: \"BRL\"}}\n        </div>\n      </div>\n\n\n    </div>\n  </div>\n  <div class=\"row margem-fim\">\n    <div class=\"col\">\n      <div class=\"minimo-troca\" *ngIf=\"cartao.plano.brindes.length && cartao.plano.brindes[0].valorEmPontos\">\n        Utilize seus créditos a partir de {{cartao.plano.brindes[0].valorEmPontos | currency: \"BRL\"}}  .\n      </div>\n    </div>\n\n  </div>\n</div>\n\n<kendo-tabstrip class=\"nav-bordered mt-2\"  >\n  <kendo-tabstrip-tab [title]=\" 'Extrato' \" [selected]=\"true\">\n    <ng-template kendoTabContent>\n\n      <app-extrato-pontos [cartao]=\"cartao\"  ></app-extrato-pontos>\n\n    </ng-template>\n  </kendo-tabstrip-tab>\n\n  <kendo-tabstrip-tab [title]=\" 'Expirando' \" *ngIf=\"cartao.plano.validade || cartao.plano.vencimento\" >\n    <ng-template kendoTabContent>\n\n      <app-cartao-proximos-vencimentos [cartao]=\"cartao\"  ></app-cartao-proximos-vencimentos>\n\n    </ng-template>\n  </kendo-tabstrip-tab>\n\n</kendo-tabstrip>\n", "import {Component, Input, OnInit} from '@angular/core';\nimport {Router} from \"@angular/router\";\n\n@Component({\n  selector: 'app-cartao-cashback',\n  templateUrl: './cartao-cashback.component.html',\n  styleUrls: ['./cartao-cashback.component.scss']\n})\nexport class CartaoCashbackComponent implements OnInit {\n\n  @Input() cartao: any = { pontos: 0  }\n  constructor(private router: Router) { }\n  ngOnInit() {}\n}\n", "<a routerLink=\"{{cartao.link}}\"><h4>{{cartao.plano?.tituloCartao}}</h4></a>\n\n<div class=\"slide\">\n  <div class=\"pontuacao \"  >\n    <div class=\"row\" [ngClass]=\"{\n          'mb-1': i < obtenhaQuantidadeDeLinhas() - 1,\n          'mb-2': i == obtenhaQuantidadeDeLinhas() -1\n      }\" *ngFor=\"let selos of listaMatrizSelos[0]; let i = index\">\n\n      <div class=\"col\" *ngFor=\"let selo of selos\" style=\"padding-left: 0px;padding-right: 0px;\">\n        <div class=\"container_selo\" [ngClass]=\"{\n                'on': selo.status == 1,\n                'off': selo.status == 0,\n                'sem': selo.status == -1\n              }\">\n          <div class=\"icone selo\" [inlineSVG]=\"'/assets/fidelidade/icones/icon-selo.svg'\" [removeSVGAttributes]=\"['fill']\"></div>\n          <div class=\"valor\"><span>{{selo.valor}}</span></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n\n  <kendo-tabstrip class=\"nav-bordered mt-2\"   #tabs  >\n\n    <kendo-tabstrip-tab [title]=\"'Brindes'\"  [id]=\"'brindes'\" [selected]=\"true\">\n      <ng-template kendoTabContent>\n\n        <div class=\"brinde linha\"   *ngFor=\"let brinde of cartao.plano?.brindes; let indice = index\">\n          <span class=\"preco_troca\">{{brinde.valorEmPontos}}  {{brinde.valorEmPontos <= 1 ? 'selo' : 'selos' }}</span>\n          <div>\n            <div class=\"nome_brinde\">{{brinde.nome}}</div>\n            <img class=\"foto_brinde\" src=\"/images/empresa/{{brinde.linkImagem}}\">\n\n\n          </div>\n        </div>\n\n      </ng-template>\n    </kendo-tabstrip-tab>\n\n    <kendo-tabstrip-tab [title]=\" 'Extrato' \"  [id]=\"'extrato'\">\n      <ng-template kendoTabContent>\n\n        <app-extrato-pontos [cartao]=\"cartao\"  ></app-extrato-pontos>\n\n      </ng-template>\n    </kendo-tabstrip-tab>\n\n    <kendo-tabstrip-tab [title]=\"'Expirando' \" *ngIf=\"true\" [id]=\"'expirando'\">\n      <ng-template kendoTabContent>\n\n        <app-cartao-proximos-vencimentos [cartao]=\"cartao\"  ></app-cartao-proximos-vencimentos>\n\n      </ng-template>\n    </kendo-tabstrip-tab>\n\n  </kendo-tabstrip>\n\n</div>\n", "import {Component, Input, OnInit} from '@angular/core';\nimport {TelaCartaoComTabs} from \"../cartao-pontos/cartao-pontos.component\";\n\n@Component({\n  selector: 'app-cartao-consumo-selo',\n  templateUrl: './cartao-consumo-selo.component.html',\n  styleUrls: [ '../cartao-selo/cartao-selo.component.scss' , './cartao-consumo-selo.component.scss']\n})\nexport class CartaoConsumoSeloComponent extends TelaCartaoComTabs implements OnInit {\n  @Input() cartao: any = { pontos: 0};\n  matrizSelos: Array<Array<any>>;\n  listaMatrizSelos = [];\n  pontosNecessarios = 12;\n  qtdePorLinha = 5;\n\n  constructor() { super() }\n\n  ngOnInit() {\n    this.configureSelos();\n  }\n\n  private configureSelos() {\n    this.listaMatrizSelos = [];\n\n    if(this.cartao.pontos > this.pontosNecessarios)\n      this.pontosNecessarios = this.cartao.pontos;\n\n    let totalPontos = 0;\n    this.listaMatrizSelos.push(this.obtenhaMatrizSelos(totalPontos, this.cartao.pontos));\n\n  }\n\n  obtenhaMatrizSelos(inicio, pontosSobrando): Array<Array<any>> {\n    let quantidadeLinhas = Math.ceil(this.pontosNecessarios / this.qtdePorLinha);\n    let selosPorLinha = Math.ceil(this.pontosNecessarios / quantidadeLinhas)\n    this.matrizSelos = [];\n\n    for(let linha = 0; linha < quantidadeLinhas; linha++) {\n      let selos = [];\n\n      for (let coluna = 0; coluna < selosPorLinha; coluna++)  {\n        let i = coluna + (linha * selosPorLinha);\n        let selo = {\n          status: 1,\n          valor: inicio + i + 1\n        }\n\n        if(i < pontosSobrando)\n          selo.status = 1;\n        else if(i < this.pontosNecessarios)\n          selo.status = 0;\n        else\n          selo.status = -1;\n\n        selos.push(selo)\n      }\n\n      if(selos.length === 2) {\n        let selo = {\n          status: -1,\n          valor: 0\n        }\n\n        selos.unshift(selo);\n\n        selo = {\n          status: -1,\n          valor: 0\n        }\n\n        selos.push(selo)\n      }\n\n      this.matrizSelos.push(selos);\n    }\n\n\n    return this.matrizSelos;\n  }\n\n  obtenhaQuantidadeDeLinhas( ) {\n    return Math.ceil(this.pontosNecessarios / this.qtdePorLinha);\n  }\n}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { SiteEmpresaRoutingModule } from './site-empresa-routing.module';\nimport {SiteEmpresaComponent} from \"./site-empresa.component\";\nimport {ClienteComponent} from \"../cliente/cliente.component\";\nimport {InlineSVGModule} from \"ng-inline-svg\";\nimport {NgxMaskModule} from \"ngx-mask\";\nimport {FormsModule, ReactiveFormsModule} from \"@angular/forms\";\nimport {ConfirmarComponent} from \"../confirmar/confirmar.component\";\nimport {FidelidadeModule} from \"../fidelidade/fidelidade.module\";\nimport {CountdownModule} from \"ngx-countdown\";\nimport {MaskedTextBoxModule, NumericTextBoxModule} from \"@progress/kendo-angular-inputs\";\nimport {FrmValideCodigoComponent} from \"../frm-valide-codigo/frm-valide-codigo.component\";\nimport {CartaoConfirmadoComponent} from \"../cliente/cartao-confirmado/cartao-confirmado.component\";\nimport {SiteEmpresaRouterComponent} from \"./site-empresa-router.component\";\nimport {CartaoPontosComponent} from \"../cliente/cartao-pontos/cartao-pontos.component\";\nimport {CartaoSeloComponent} from \"../cliente/cartao-selo/cartao-selo.component\";\nimport {RegrasComponent} from \"../regras/regras.component\";\nimport {CartaoCashbackComponent} from \"../cliente/cartao-cashback/cartao-cashback.component\";\nimport {CartaoConsumoSeloComponent} from \"../cliente/cartao-consumo-selo/cartao-consumo-selo.component\";\nimport {ExtratoPontosComponent} from \"../componentes/extrato-pontos/extrato-pontos.component\";\nimport {CartaoProximosVencimentosComponent} from \"../componentes/cartao-proximos-vencimentos/cartao-proximos-vencimentos.component\";\nimport {DropDownListModule} from \"@progress/kendo-angular-dropdowns\";\nimport {MeusCartoesComponent} from \"../cliente/meus-cartoes/meus-cartoes.component\";\nimport {ScrollViewModule} from \"@progress/kendo-angular-scrollview\";\nimport {CompartilhadoModule} from \"../compartilhado/compartilhado.module\";\nimport {DatePickerModule} from \"@progress/kendo-angular-dateinputs\";\nimport {TabStripModule} from \"@progress/kendo-angular-layout\";\n\n@NgModule({\n    declarations: [\n        SiteEmpresaComponent,\n        ClienteComponent,\n        ConfirmarComponent,\n        FrmValideCodigoComponent, ExtratoPontosComponent, CartaoProximosVencimentosComponent,\n        CartaoSeloComponent, CartaoPontosComponent, CartaoCashbackComponent,\n        CartaoConfirmadoComponent, CartaoConsumoSeloComponent, MeusCartoesComponent,\n        SiteEmpresaRouterComponent, RegrasComponent\n    ],\n    imports: [\n        CommonModule,\n        ScrollViewModule,\n        SiteEmpresaRoutingModule,\n        InlineSVGModule,\n        FormsModule,\n        CountdownModule,\n        DropDownListModule,\n        ReactiveFormsModule,\n        NgxMaskModule,\n        FidelidadeModule,\n        MaskedTextBoxModule,\n        NumericTextBoxModule,\n        CompartilhadoModule,\n        DatePickerModule,\n        TabStripModule\n    ]\n})\nexport class SiteEmpresaModule { }\n"], "x_google_ignoreList": [3]}