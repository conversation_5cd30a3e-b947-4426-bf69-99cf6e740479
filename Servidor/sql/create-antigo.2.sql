CREATE TABLEalter table config_meio_pagamento add column antifraude_desabilitado bit(1) default null;

alter table comanda add column desconto decimal(9,2) NOT NULL default 0;

/** novo multipedido */
alter table pedido add column tipo varchar(25) default 'pedido';
alter table pedido add column multipedido_id bigint null;
alter table pedido add FOREIGN KEY (multipedido_id) REFERENCES pedido(id);

alter table grupo_de_lojas add column multipedido bit(1) default null;


alter table forma_de_pagamento add column notificar_novo_pedido bit(1) default true;
alter table forma_de_pagamento add column notificar_confirmacao_pagamento bit(1) default true;

alter table cupom add column mensagem_minimo longtext null;
alter table cupom add column minimo_apenas_mesmo_tamanho bit(1) null;

alter table comanda add column pontos_ganhos decimal(9,2) null;
alter table pontuacao_registrada add column comanda_id bigint(20) null;
alter table pontuacao_registrada add unique(comanda_id);
alter table integracao_pedido_fidelidade add column pontuar_mesas bit(1) null;


CREATE TABLE cupom_horario (
 id varchar(255) not null primary key,
  cupom_id bigint(20) NOT NULL,
  dia int not null,
      CONSTRAINT   FOREIGN KEY (cupom_id) REFERENCES cupom(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE forma_pagamento_grupo_de_para (
  id bigint(20) not null auto_increment,
   grupo_de_lojas_id bigint(20) NOT NULL,
   empresa_principal_id bigint(20) NOT NULL,
   de_id  bigint(20) NOT NULL,
   para_id  bigint(20) NOT NULL,
         CONSTRAINT   FOREIGN KEY (de_id) REFERENCES forma_de_pagamento(id),
         CONSTRAINT   FOREIGN KEY (para_id) REFERENCES forma_de_pagamento(id),
     unique(empresa_principal_id, para_id),
      primary key (id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table regra_da_promocao add column quantidade bigint(20) null;

alter table regra_da_promocao add column tamanho_pizza_comprar_id  bigint(20) null;
alter table regra_da_promocao add column tamanho_pizza_ganhar_id bigint(20) null;


alter table regra_da_promocao add FOREIGN KEY (tamanho_pizza_comprar_id) REFERENCES produto_template_tamanho(id);
alter table regra_da_promocao add FOREIGN KEY (tamanho_pizza_ganhar_id) REFERENCES produto_template_tamanho(id);


alter table sessao_link_saudacao add column mensagem_enviada_id bigint(20) null;


create index idx_sessao_link_saudacao_empresa_hash on sessao_link_saudacao(empresa_id, hash);
create index idx_sessao_link_saudacao_campanha_mensagem_enviada on sessao_link_saudacao(campanha_id, mensagem_enviada_id);
create index idx_sessao_link_saudacao_mensagem_enviada on sessao_link_saudacao(mensagem_enviada_id);
create index idx_msg_enviada_empresa_campanha on mensagem_enviada(empresa_id, campanha_id);

alter table sessao_link_saudacao add foreign key(mensagem_enviada_id) references mensagem_enviada(id);

alter table contrato add column data_proximo_vencimento date  default null;

CREATE TABLE historico_contrato (
  id bigint NOT NULL AUTO_INCREMENT,
  horario datetime NOT NULL,
  descricao varchar(255) NOT NULL,
  contrato_id bigint NOT NULL,
  operador_id bigint     NULL,
  dados varchar(255)   NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (contrato_id) REFERENCES contrato (id),
  CONSTRAINT   FOREIGN KEY (operador_id) REFERENCES usuario (id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


alter table produto_template_opcao add column semborda bit(1) null;

alter table config_impressao add column emitir_beep bit(1) default false;
alter table config_impressao add column duracao_beep bigint(20) default null;
alter table config_impressao add column quantidade_beeps bigint(20) default null;


create table catalogo(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) NOT NULL,
  ativo bit(1) not null,
  criacao datetime not null,
  atualizacao datetime null,
  desativacao datetime null,

  criador_id bigint(20) null,

  primary key(id),
  foreign key(criador_id) references usuario(id)
);

insert into catalogo select id, nome, true, now(), now(), null, responsavel_id from empresa;

alter table produto add column catalogo_id bigint(20) null;

update produto set catalogo_id = empresa_id;
alter table produto modify column catalogo_id bigint(20) not null;

ALTER TABLE produto ADD CONSTRAINT fk_produto_Catalogo FOREIGN KEY (catalogo_id) REFERENCES catalogo (id);


alter table empresa add column catalogo_id bigint(20) null;
update empresa set catalogo_id = id;
alter table empresa modify column catalogo_id bigint(20) not null;
ALTER TABLE empresa ADD CONSTRAINT fk_empresa_Catalogo FOREIGN KEY (catalogo_id) REFERENCES catalogo (id);

alter table adicional_produto add column catalogo_id bigint(20) null;
update adicional_produto set catalogo_id = empresa_id;
ALTER TABLE adicional_produto ADD CONSTRAINT fk_adicional_Catalogo FOREIGN KEY (catalogo_id) REFERENCES catalogo (id);

alter table produto_adicional_produto add column catalogo_id bigint(20) null;
update produto_adicional_produto set catalogo_id = empresa_id;
alter table produto_adicional_produto modify column catalogo_id bigint(20) not null;
ALTER TABLE produto_adicional_produto ADD CONSTRAINT fk_produto_adicional_Catalogo FOREIGN KEY (catalogo_id) REFERENCES catalogo (id);

alter table produto_template add column catalogo_id bigint(20) null;
update produto_template set catalogo_id = empresa_id;
alter table produto_template modify column catalogo_id bigint(20) not null;
ALTER TABLE produto_template ADD CONSTRAINT fk_produto_template_Catalogo FOREIGN KEY (catalogo_id) REFERENCES catalogo (id);

create table catalogo_da_rede(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  catalogo_id bigint(20) not null,
  rede varchar(255) not null,

   primary key (id),
  foreign key (catalogo_id) references catalogo(id)
);

alter table categoria add column catalogo_id bigint(20) null;
update categoria set catalogo_id = empresa_id;
alter table categoria modify column catalogo_id bigint(20) not null;
ALTER TABLE categoria ADD CONSTRAINT fk_categoria_Catalogo FOREIGN KEY (catalogo_id) REFERENCES catalogo (id);


alter table produto modify column empresa_id bigint(20) null;
alter table categoria modify column empresa_id bigint(20) null;
alter table produto_adicional_produto modify column empresa_id bigint(20) null;


create table produto_na_empresa(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  produto_id bigint(20) not null,
  empresa_id bigint(20) not null,
  preco decimal(9,2) not null,
  disponibilidade int not null default 0,
  tem_estoque bit(1) not null default true,
  exibir_preco_site bit(1) not null default true,
  exibir_preco_no_cardapio bit(1) not null default true,
  disponivel_na_mesa bit(1) not null default true,
  disponivel_para_delivery bit(1) not null default true,
  mensagem_pedido varchar(255) null,
  primary key (id),
  foreign key(produto_id) references produto(id),
  foreign key(empresa_id) references empresa(id),
  constraint produto_empresa_unicos unique(produto_id, empresa_id)
);

alter table catalogo add column preco_por_empresa bit(1) not null default false;
alter table catalogo add column disponibilidade_por_empresa bit(1) not null default false;


create table opcao_na_empresa(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  opcao_id bigint(20) not null,
  empresa_id bigint(20) not null,
  valor decimal(9,2) not null,
  disponivel bit(1) not null default true,
  primary key (id),
  foreign key(opcao_id) references opcao_adicional_produto(id),
  foreign key(empresa_id) references empresa(id),
  constraint opcao_empresa_unicos unique(opcao_id, empresa_id)
);


alter table mesa add column somente_leitura bit(1) default false;

alter table impressora add column nao_imprime_pedidos_mesa bit(1) default false;
alter table impressora add column nao_imprime_pedidos_delivery bit(1) default false;

alter table cardapio add column limite_produtos int null;


alter table produto_template add column nome_categoria_montar varchar(100) null;


alter table grupo_de_lojas add column encontrar_loja_mais_proxima bit(1) default false;

create table avaliacao_pedido(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  pedido_id bigint(20) not null,
  nota int not null,
  empresa_id bigint(20) not null,
  gostou_da_entrega  bit(1) not null default true,
  comentario varchar(300) null,
  data date not null,
  primary key (id),
  foreign key(pedido_id) references pedido(id),
  foreign key(empresa_id) references empresa(id)
);


CREATE TABLE promocao_horario (
 id varchar(255) not null primary key,
  promocao_id bigint(20) NOT NULL,
  dia int not null,
      CONSTRAINT   FOREIGN KEY (promocao_id) REFERENCES promocao(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table forma_de_pagamento add column possui_desconto bit(1) null default false;
alter table forma_de_pagamento add column desconto decimal(9,2) null default 0;

alter table pedido add column desconto_forma_de_pagamento decimal(9,2) null;


insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  false, id, 'Avaliar Pedido',  '[NomeContato], o que achou do seu último pedido?

Conte aqui para nós. Sua opinião é muito importante.

Clique no link abaixo para avaliar.
[Link_Avaliar]', true, 7,-1
        from empresa where removida is not true;

create index idx_horario on pedido(horario);

create table tarefa_mensagem_avaliar_pedido (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  empresa_id bigint(20) not null,
  pedido_id bigint(20) not null,
  mensagem_avaliar_pedido_id bigint(20) null,
  horario datetime NOT NULL,
  primary key (id),
  foreign key(pedido_id) references pedido(id),
  foreign key(empresa_id) references empresa(id)
);


alter table cupom add column quantidade  int  null;
alter table empresa_campo_extra add column opcional bit(1) default null;
alter table empresa_formas_de_entrega add column taxa_fixa decimal(9,2) null;

create table aviso_de_sistema(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  tipo varchar(255) not null,
  mensagem varchar(255) not null,
  entregue bit(1) not null default false,
  empresa_id bigint(20) not null,
  primary key (id),
  foreign key(empresa_id) references empresa(id)
);

alter table empresa add column avisos_de_mesa bit(1) not null default false;


  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  false, empresa.id, 'Comanda Fechada',  'A sua comanda [StatusPedido]', true, -1,-1
        from empresa;


alter table rede add column grupo varchar(255) null;


alter table empresa_formas_de_entrega add column arredondar_distancias bit(1) null default 0;

create table template_de_mensagem(
  id bigint(20) NOT NULL auto_increment,
  nome varchar(255) not null,
  mensagem longtext not null,
  empresa_id bigint(20) not null,

  primary key (id),
  foreign key(empresa_id) references empresa(id)
);

alter table template_de_mensagem add column rede_id bigint(20) null;
alter table template_de_mensagem modify column empresa_id bigint(2) null;

alter table rede add column grupo varchar(255) null;
alter table empresa add column status_pedido_ao_aceitar varchar(25) null;

alter table adicional_produto add column combo bit(1) null;


alter table opcao_adicional_produto add column produto_id bigint(20) null;
ALTER TABLE opcao_adicional_produto ADD CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto (id);

alter table opcao_adicional_produto add column produto_tamanho_id bigint(20) null;
ALTER TABLE opcao_adicional_produto ADD CONSTRAINT   FOREIGN KEY (produto_tamanho_id) REFERENCES produto_tamanho (id);



insert into forma_de_pagamento_integrada_nova(sistema, descricao,codigo)
    values ('totvs', 'Dinheiro', '0'),
           ('totvs', 'Cheque',  '1'),
           ('totvs', 'Cartão de Credito',  '2'),
           ('totvs', 'Cartão de Debito',  '3'),
           ('totvs', 'Ticket',  '4');


alter table empresa add column favicon varchar(255) null;

create table entregador(
  id bigint(20) NOT NULL auto_increment,
  nome varchar(255) not null,
  email varchar(255)  null,
  telefone varchar(255) not null,
  codigo varchar(255) null,
  ativo bit(1) not null default true,
  excluido bit(1) null,
  empresa_id bigint(20) not null,


  primary key (id),
  foreign key(empresa_id) references empresa(id)
);

alter table pedido add column entregador_id bigint(20) null;

ALTER TABLE pedido ADD CONSTRAINT fk_entregador
FOREIGN KEY(entregador_Id) REFERENCES entregador (id);

insert into modulo (nome) values ('entregadores');



alter table cupom add column selecionavel bit(1) null;


alter table empresa add column agrupar_categorias_pizza bit(1) null;


alter table empresa_formas_de_entrega add column priorizar_localizacao bit(1) null default false;

alter table produto_na_empresa add column novo_preco decimal(9,2) null;
alter table produto_na_empresa add column destaque   bit(1) default null;


create table sistema_integrado(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) not null,
  site varchar(255),
  tipo varchar(255) not null,

  primary key (id)
);

alter table cliente_api add column sistema_integrado_id bigint(20);
alter table cliente_api add foreign key  (sistema_integrado_id) references sistema_integrado(id);

 insert into sistema_integrado(nome, site, tipo) values ('Foody Delivery', null, 'entrega'),
  ('Winfood', null, 'pdv'), ('VKN', null, 'pdv'), ('Cloudify', null, 'pdv'),
   ('Moovery', null, 'entrega'), ('Titan', null, 'erp');


 alter table config_impressao add column imprimir_online_nao_pago bit(1) null default false;



create table integracao_fidelidade(
 id bigint(20) NOT NULL auto_increment,
 sistema varchar(50) not null,
 loja int null,
 data_ativacao datetime not null,
 empresa_id bigint(20) not null,
 operador_id bigint(20) not null,
 primary key (id),
   foreign key(empresa_id) references empresa(id),
   foreign key(operador_id) references usuario(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table cashback_reserva(
 id bigint(20) NOT NULL auto_increment,
 sistema varchar(50) not null,
 id_reserva int not null,
 id_cliente int not null,
 valor decimal(9,2) not null,
 id_empresa int null,
 id_marca int null,
 id_loja int null,
 id_fidelidade int null,
 versao_fidelidade int null,
  primary key (id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table pagamento_pedido add column cashback_reserva_id bigint(20) null;
ALTER TABLE pagamento_pedido ADD CONSTRAINT   FOREIGN KEY (cashback_reserva_id) REFERENCES cashback_reserva (id);


insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    values(true, 1045, 'Avaliar Pedido',  '[NomeContato], o que achou do seu último pedido?

Conte aqui para nós. Sua opinião é muito importante.

Clique no link abaixo para avaliar.
[Link_Avaliar]', true, 7,-1);


alter table usuario add column assinar_mensagens bit(1) default false;


create table tag(
 id bigint(20) NOT NULL auto_increment,
  nome varchar(100) not null,
  empresa_id bigint(20) null,
  primary key (id),
     foreign key(empresa_id) references empresa(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


create table contato_tag(
   contato_id bigint(20) NOT NULL,
   tag_id bigint(20) NOT NULL,
      foreign key(contato_id) references contato(id),
      foreign key(tag_id) references tag(id)
);


alter table empresa_formas_de_entrega add column intervalo_agendamento bigint(2) null default 30;

alter table usuario add column assinar_mensagens bit(1) default false;


/*Id da empresa para criar as notificações*/

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
values(true, 1, 'Etapa Atendeu',  'Olá [NomeContato], tudo bem? Nos falamos agora há pouco por telefone.' , true, -1,-1);

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
values(true, 1, 'Etapa Não Atendeu',  'Olá [NomeContato], tudo bem? Tentei te ligar e não consegui, está podendo falar?' , true, -1,-1);

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
values(true, 1, 'Etapa 2° Contato (Ligar)',  'Oi, como vai? Sou consultor da Promokit, estou tentando falar com [NomeContato]. Consigo por esse número?' , true, -1,-1);

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
values(true, 1, 'Etapa 3° Contato',  'Olá [NomeContato], tudo bem?

Nós temos ajudado empresas do mesmo segmento que a sua  a aumentar e personalizar as vendas pelo Whatsapp. O que acha de trocarmos uma ideia?' , true, -1,-1);

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
values(true, 1, 'Etapa 4° Contato',  'Olá [NomeContato], que tal revolucionar seu atendimento com o Meucardapio.ai?!

Hoje temos várias cases de sucesso que podem comprovar isso, entre os nossos clientes temos toda a rede China In Box e vários outros!

Para ver funcionando, acesse o instagram Oficial da China https://www.instagram.com/chinainboxoficial/. Clique no Link da Bio e depois em Peça Pelo Whatsapp.

Chega mais, vamos marcar uma demonstração pra você conhecer!' , true, -1,-1);

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
values(true, 1, 'Etapa 5° Contato',  '[NomeContato], O La Churras tinha uma imensa dificuldade em gerenciar os pedidos que recebia pelo WhatsApp. Quer saber como ele conseguiu resolver esse problema usando o MeuCardapio.ai?

Olha o que eles postaram sobre a gente
https://www.youtube.com/watch?v=DTXyufvUrPc' , true, -1,-1);

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
values(true, 1, 'Etapa 6° Contato Finalização',  'Olá [NomeContato]! Tenho tentado contato há algum tempo e não consegui. Você não tem interesse no momento em melhorar e automatizar o seu próprio delivery?

Não sei se está sem tempo ou se a dificuldade de gerenciar pedidos no Whatsapp não é sua prioridade neste momento.

De toda forma, deixo esse meu contato a disposição caso você queira em algum momento saber mais sobre esse assunto' , true, -1,-1);


alter table pausa_programada add column mensagem varchar(255) null;

alter table banner add column validade date null;

alter table mensagem_bot add column chat_id varchar(100) null;


insert into notificacao (ativada, mensagem, empresa_id, tipo_de_notificacao, pode_desativar, qtde_dias_nova_notificacao) select false ativada,
"Você foi selecionado como entregador do pedido: *#[CodigoPedido]*\n\nVeja os detalhes do pedido:\n\n[DadosPedido]" mensagem,  e.id empresa_id,
'Pedido Associado a Entregador' tipo_de_notificacao, true pode_Desativar, -1 qtde_dias_nova_notificacao
from empresa e join empresa_modulo em on e.id = em.empresa_id where em.modulo_id = 8;


alter table raio_de_cobranca add column desativado bit(1) null;
alter table alcance add column desativado bit(1) null;


create table traducao_mensagem_bot(
  id bigint(20) NOT NULL auto_increment,
  nome varchar(100) not null,
  descricao varchar(255) not null,
  template varchar(255) not null,
  mensagem text not null,
  empresa_id bigint(20) null,
  ativo bit(1) null default 0,
  primary key (id),
 foreign key(empresa_id) references empresa(id)
) ENGINE=InnoDB AUTO_INCREMENT=28285 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

create table mensagem_padrao_bot(
  id bigint(20) NOT NULL auto_increment,
  nome varchar(100) not null,
  descricao varchar(255) not null,
  template varchar(255) not null,
  mensagem text not null,
  empresa_id bigint(20) null,
  primary key (id),
 foreign key(empresa_id) references empresa(id)
);


insert into mensagem_padrao_bot (nome, template, descricao, mensagem)
values('Mensagem saudação', '[MensagemSaudacao]', 'Resposta para a primeira mensagen enviada pelo usuário.',
'Seja bem-vindo(a) à [Empresa]!

Em que posso ajudar hoje? Aqui estão algumas opções:

 1. Ver *cardápio completo* e fazer um pedido online
 2. Conhecer nossas *promoções* atuais
 3. Nosso *endereço* completo
 4. Falar com um *atendente*
 5. Repetir Pedido

[Link_Cardapio]
');

insert into mensagem_padrao_bot (nome, template, descricao, mensagem)
values('Mensagem saudação para clientes antigos', '[MensagemClienteAntigo]', 'Complemento da mensagem de saudação e é enviada para clientes que já fizeram pedidos',
'Bem-vindo de volta! Se você desejar repetir um dos seus últimos pedidos, responda com *repetir* pedido?');

insert into mensagem_padrao_bot (nome, template, descricao, mensagem)
values('Cumprimento', '[CUMPRIMENTO]', 'Responder a cumprimento do cliente',
'Olá, [NomeContato]');

insert into mensagem_padrao_bot (nome, template, descricao, mensagem)
values('Atendente', '[ATENDENTE]', 'Mensagem avisando o cliente que o bot avisou um atendente',
'Por favor, aguarde enquanto eu conecto você com um atendente, [NomeContato].');

insert into mensagem_padrao_bot (nome, template, descricao, mensagem)
values('Endereço', '[ENDERECO]', 'Informe o endereço da empresa',
'Endereço da Empresa');

insert into mensagem_padrao_bot (nome, template, descricao, mensagem)
values('Promoções', '[PROMOCOES]', 'Informe as promoções vigentes da empresa',
'Essas são as nossas promoções atuais');

insert into mensagem_padrao_bot (nome, template, descricao, mensagem)
values('Ver Cardápio', '[CARDAPIO]', 'Informe as promoções vigentes da empresa',
'Para ver nosso cardápio digital e fazer um pedido acesse:

[Link_Cardapio]');

alter table empresa_formas_de_entrega add column nao_usar_cidade_padrao bit(1) null;


insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
values(true, 1, 'Notificar Clientes Vendedor',  'Mensagem' , true, -1,-1);
insert into campo_extra(nome) values ('datanascimento');


delete from acao_contato where acao_contato.pontuacao_registrada_id in (select id from pontuacao_registrada where not exists (select 1 from cartao where pontuacao_registrada.cartao_id = cartao.id));
delete from pontuacao_registrada_atividade where pontuacao_registrada_atividade.pontuacao_registrada_id in (select id from pontuacao_registrada where not exists (select 1 from cartao where pontuacao_registrada.cartao_id = cartao.id));
delete from pontuacao_registrada where not exists (select 1 from cartao where pontuacao_registrada.cartao_id = cartao.id);

alter table pontuacao_registrada add FOREIGN KEY (cartao_id) REFERENCES cartao(id);
alter table brinde_resgatado add FOREIGN KEY (cartao_id) REFERENCES cartao(id);

delete from brinde_resgatado where not exists (select 1 from cartao where brinde_resgatado.cartao_id = cartao.id);
alter table brinde_resgatado add FOREIGN KEY (cartao_id) REFERENCES cartao(id);





alter table campanha add column versao smallint default 0;
alter table mensagem_enviada add column versao_campanha smallint default 0;

create index idx_msg_enviada_campanha on mensagem_enviada(campanha_id);

alter table rede add column empresa_id bigint(20) null;
alter table rede add FOREIGN KEY (empresa_id) REFERENCES empresa(id);

update empresa set admin_rede = 'redealsultan' where id = 995;
update empresa set admin_rede = 'chinatrendfoods' where id = 650;

// crie coluna descricao na tabela rede
alter table rede add column descricao varchar(255) null;
update rede set descricao = concat(upper(left(nome,1)), substring(nome,2));


create table categoria_na_empresa(
  id bigint NOT NULL AUTO_INCREMENT,
  categoria_id bigint not null,
  empresa_id bigint not null,
  disponivel bit(1) not null default true,
  primary key (id),
  foreign key(categoria_id) references categoria(id),
  foreign key(empresa_id) references empresa(id),
  constraint categoria_empresa_unicos unique(categoria_id, empresa_id)
);
create table mensagem_padrao_bot(
  id bigint(20) NOT NULL auto_increment,
  nome varchar(100) not null,
  descricao varchar(255) not null,
  template varchar(255) not null,
  mensagem text not null,
  empresa_id bigint(20) null,
  primary key (id),
 foreign key(empresa_id) references empresa(id)ele
);


// quero criar uma tabela no mysql para salvar prompts e respostas
create table prompt_resposta(
  id bigint(20) NOT NULL auto_increment,
  prompt varchar(255) not null,
  resposta varchar(255) not null,
  empresa_id bigint(20) null,
  primary key (id),
 foreign key(empresa_id) references empresa(id)
);

// criar uma tabela no mysql para salvar templates de prompts
CREATE TABLE template_prompt (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(100) NOT NULL,
  tipo varchar(100) NOT NULL,
  descricao varchar(255) NOT NULL,
  template text NOT NULL,
  PRIMARY KEY (id)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

// crie um insert para a tabela template_prompt
INSERT INTO template_prompt (nome, descricao, template)
VALUES ('Padrão Campanhas Marketing', 'Modelo de mensagem para campanhas de marketing via WhatsApp', 'Por favor, crie uma mensagem de marketing para uma campanha de vendas via WhatsApp para a empresa ${empresa.nome}, seguindo as seguintes regras:

Inclua o nome de contato ([NomeContato]) em negrito e o link do cardápio digital ([Link_Cardapio]) para os clientes poderem fazer pedidos.
Pergunte como o cliente está, saudando-o cordialmente.
Se houver cupom no tema abaixo, adicione ele em negrito na mensagem.
Deixe uma linha em branco antes e depois d o link do cardápio digital.
Utilize sintaxe de emojis do Whatsapp
Adicione negrito e itálico na mensagem usando as regras do Whatsapp.
Sempre coloque o nome dos cupons em negrito, usando * antes e depois do nome.
Organize o texto de forma clara e fácil de ler.
Não precisa dizer que a mensagem é da ${empresa.nome}.
Refira-se ao cardápio digital, e não ao site.
O código do cupom deve começar sempre com CUPOM::');



// crie uma table no mysql para salvar as mensagens geradas pela IA
create table mensagem_gerada_ia(
  id bigint(20) NOT NULL auto_increment,
  prompt text not null,
  mensagem text not null,
  empresa_id bigint(20) null,
  data_criacao TIMESTAMP DEFAULT NOW() NOT NULL,
  primary key (id),
 foreign key(empresa_id) references empresa(id)
);

//cria um tabela mysql para armazenar as mensagens de saudação com horário de criação e data a tabela deve permitir caracters unicode
CREATE TABLE mensagem_saudacao (
  id INT PRIMARY KEY AUTO_INCREMENT,
  mensagem TEXT NOT NULL,
  data_criacao TIMESTAMP DEFAULT NOW() NOT NULL
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE estado_chatbot (
  id INT PRIMARY KEY AUTO_INCREMENT,
  telefone VARCHAR(20) NOT NULL,
  ultima_interacao DATETIME NOT NULL,
  intent varchar(100) null,
  mensagem text null,
  comando varchar(100) null,
  resposta text null,
  empresa_id bigint(20) null,
  atendente bit(1) not null default 0
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


INSERT INTO template_prompt (nome, descricao, template)
VALUES ('prompt_chatbot_duvidas', 'Prompt chatbot de dúvidas', '[SEMPRE] Você só conhece as seguintes informações:
[HORARIO_ATENDIMENTO] Hora atual: ${new Date().toLocaleDateString("pt-BR", {weekday: "long"})} ${new Date().toLocaleTimeString("pt-BR")}.
[ATENDENTE] O atendimento é via mensagens de texto no whatsapp
[SEMPRE] Possível nome do cliente: [Nome]
[SEMPRE] Link do cardápio digital deve ser sempre [LINK].
[SEMPRE] O cardápio digital só permite fazer pedidos
[CUMPRIMENTO] Responda calorisamente o cliente e pergunte o que ele precisa
[ENVIAR_CARDÁPIO] Se pedir o cardápio digital, enviar o link do cardápio.
[PROMOCOES] Sobre promoções, o bot sabe que as seguintes estão ativas: Copo de salgados de R$ 9,00 por R$ 8,00, Coxinha de R$ 7,00 por R$ 6,00.
[PEDIRPELOZAP,PEDIRCITOUPRODUTO,FAZERPEDIDO] O bot não sabe o que está disponível no cardápio digital, qualquer pergunta sobre o tema deve ser que o cliente deve verificar no cardápio digital
[PEDIRPELOZAP] Se quiser pedir por aqui, responda com o link cardápio e cite vantagens, mas pergunte se o cliente quer falar com um atendente.
[PROBLEMAS] Ajuda, transfira para um atendente.
[Horario_Atendimento] Horário de atendimento da loja: Segunda a Sexta, das 18:00 às 00:00. Responda baseado nesse horário
[FazerPedido,ENVIAR_CARDAPIO] Novos Pedidos: Enviar o link do cardápio e pedir para fazer por ele.
[INFORMACOES_PEDIDO] Informações sobre Pedidos: Perguntar se deseja falar um atendente.
[INFORMACOES_PEDIDO] Tempo de Entrega:  20 a 30 minutos.
[INFORMACOES_PEDIDO] Se a resposta pergunta sobre falar com atendente, termine a resposta com [ATENDENTE].
[ATENDENTE] Se o cliente pedir para falar ou chamar um atendente, o bot deve informar vai transferir para o atendent e finalizar com [FimBot]
[ENDERECO] Endereço da Loja: Avenida Elias Maluf, 1082 - Wanel Ville, Sorocaba - SP.
[ENTREGA] Formas de entrega: Fazemos delivery ou retiradas na loja (ir buscar).
[ENTREGA] Para verificar se entrega em um endereço, precisa acessar o link do cardápio
[PROBLEMAS] Se o cliente disser que está ocorrendo um erro no site, peça desculpas, informe que está transferindo para um atendente e finalize o bot

[SEMPRE] A resposta do bot é criativa, precisa, com emojis whatsapp e na primeira pessoa do singular.');


insert into template_prompt (nome, descricao, template)
  values('prompt_classificar_intent', 'Prompt para classificar a intent', 'Você pré-classifica mensagens que serão enviadas para um bot de restaurante através do Whatsapp. As mensagens são escritas por pessoas dialogando com o bot

Intents do bot de restaurante:
PEDIRCITOUPRODUTO CUMPRIMENTO AGRADECIMENTO ENVIAR_CARDAPIO FAZERPEDIDO  INFORMACOES_PEDIDO PROMOCOES
TAXASDEENTREGA ATENDENTE HORARIO_ATENDIMENTO PEDIR_USANDO_BOT PROBLEMAS ENTREGA ENDERECO FORA_DO_ESCOPO

Dentre as intents acima, responda com apenas uma palavra a classificação da frase abaixo:');


CREATE TABLE configuracoes_mia (
id INT PRIMARY KEY AUTO_INCREMENT,
empresa_id BIGINT(20) NOT NULL,
nome VARCHAR(100) NOT NULL,
telefones_teste TEXT NULL,
status VARCHAR(50) NOT NULL DEFAULT 'DESATIVADA',
FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE chamada_ia (
id bigint(20) NOT NULL AUTO_INCREMENT,
api varchar(50) NOT NULL,
codigo int not null default 0,
prompt mediumtext not null,
mensagem mediumtext NOT NULL,
resposta mediumtext NOT NULL,
prompt_tokens int not null default 0,
completion_tokens int not null default 0,
total_tokens int not null default 0,
data_criacao date NOT NULL,
horario_criacao time NOT NULL,
empresa_id bigint(20) NOT NULL,
tempo_chamada float NOT NULL,
PRIMARY KEY (id),
FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


alter table catalogo add column compartilhado bit(1) not null default false;
update catalogo set compartilhado = true where catalogo.id in (select catalogo_id from catalogo_da_rede);


ALTER TABLE sessao_link_saudacao ADD INDEX idx_telefone (telefone);
ALTER TABLE sessao_link_saudacao ADD INDEX idx_empresa_telefone (empresa_id, telefone);

alter table contato add column codigo_pais varchar(6) not null default "+55";
alter table sessao_link_saudacao add column codigo_pais varchar(6) not null default "+55";


alter table grupo_de_lojas add column nao_listar_lojas bit(1) default false;

CREATE TABLE qr_code (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  nome VARCHAR(255) NOT NULL,
  url VARCHAR(255) NOT NULL,
  horario_criacao DATETIME NOT NULL,
  removido BIT(1) NOT NULL DEFAULT 0,
  empresa_id BIGINT(20) NOT NULL,
  PRIMARY KEY (id),
  FOREIGN KEY (empresa_id) REFERENCES empresa (id)
);

alter table forma_de_pagamento add column habilitar_retirada bit(1) not null default true;
alter table forma_de_pagamento add column habilitar_entrega bit(1) not null default true;


CREATE TABLE mensagem_whatsapp_web (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  id_whatsapp VARCHAR(255) NOT NULL,
  id_destinatario VARCHAR(255) NOT NULL,
  id_remetente VARCHAR(255) NOT NULL,
  id_chat VARCHAR(255) NOT NULL,
  nome_chat VARCHAR(255) NOT NULL,
  nome_contato VARCHAR(255) NOT NULL,
  conteudo TEXT NOT NULL,
  timestamp BIGINT(20) NOT NULL,
  tipo VARCHAR(20) NOT NULL,
  empresa_id BIGINT(20) NOT NULL,
  PRIMARY KEY (id),
  FOREIGN KEY (empresa_id) REFERENCES empresa(id)
);

ALTER TABLE configuracoes_mia
ADD COLUMN comportamento_fora_do_escopo VARCHAR(255) NOT NULL DEFAULT 'CHAMAR_ATENDENTE';


//tempo padrão são 30 minutos e a coluna tempo_pausa_mia é em segundos
ALTER TABLE configuracoes_mia ADD COLUMN tempo_pausa_mia int DEFAULT 1800;

ALTER TABLE cupom add column listavel bit(1) not null default true;

<!-- integracao opendelivery -->
create table disponibilidade(
  id bigint(20) NOT NULL auto_increment,
  data_inicio datetime null,
  data_fim datetime null,
  codigo_externo varchar (150) null,
  catalogo_id bigint(20) not null,
  primary key (id),
  foreign key(catalogo_id) references catalogo(id)
);

create table disponibilidade_periodo(
  id bigint(20) NOT NULL auto_increment,
  hora_inicio time   NULL,
  hora_fim time   NULL,
  disponibilidade_id bigint(20)  not null,
  primary key (id),
  foreign key( disponibilidade_id) references  disponibilidade(id)
);

create table disponibilidade_periodo_dia(
  id bigint(20) NOT NULL auto_increment,
  dia int NOT NULL,
  disponibilidade_periodo_id  bigint(20)  not null,
  primary key (id),
  foreign key( disponibilidade_periodo_id) references  disponibilidade_periodo(id)
);

create table produto_disponibilidade(
  produto_id bigint(20) NOT NULL,
  disponibilidade_id bigint(20) NOT NULL,
   foreign key(produto_id) references produto(id),
   foreign key(disponibilidade_id) references disponibilidade(id)
);

create table integracao_opendelivery(
  id bigint(20) NOT NULL auto_increment,
  cliente_id varchar(255)   null,
  merchant_api_key varchar(100) null,
  merchant_base_url varchar (100) null,
  merchant_webhook  varchar (100) null,
  merchant_id varchar(200) null,
  merchant_name varchar(200) null,
  empresa_id bigint(20) not null,
  horario_cadastro datetime not null,
  operador_id bigint(20) not null,
  horario_ativacao datetime null,
  ativa bit(1) default 0,
  primary key (id),
     foreign key(empresa_id) references empresa(id),
     foreign key(cliente_id) references cliente_api(id),
     foreign key(operador_id) references usuario(id)
)  engine=innodb;


create table order_event(
  id bigint(20) NOT NULL auto_increment,
  event_id varchar(255) not null,
  event_type varchar(50) not null,
  created_at datetime not null,
  order_id varchar(255),
  source_app_id varchar(100)  null,
  reconhecido bit(1) default null,
  empresa_id bigint(20) not null,
  unique(event_id),
    primary key (id),
      foreign key(empresa_id) references empresa(id)
)  engine=innodb;

create table opendelivery_method(
  id bigint(20) NOT NULL auto_increment,
  codigo varchar(50) not null,
  descricao varchar(50) not null,
  tipo_bandeira varchar(5) null,
  primary key (id)
)  engine=innodb;

create table pedido_cancelado(
 id bigint(20) NOT NULL auto_increment,
 pedido_id bigint(20) not null,
 motivo longtext not null,
 codigo varchar(50) null,
 horario datetime not null,
 modo   ENUM('AUTO', 'MANUAL'),
 operador_id bigint(20) null,
   primary key (id),
       foreign key(pedido_id) references pedido(id),
       foreign key(operador_id) references usuario(id)

)  engine=innodb;

insert into opendelivery_method(codigo, descricao, tipo_bandeira)
    values( 'CREDIT', 'Crédito', 'C'),
          ( 'DEBIT', 'Débito','D'),
           ('MEAL_VOUCHER', 'Vale Refeição', 'T'),
           ('FOOD_VOUCHER', 'Vale Alimentaçao', 'T'),
           ('CASH', 'Dinheiro', null),
           ('PIX', 'Pix', null),
           ('OTHER', 'Outra', null);


ALTER TABLE forma_de_pagamento add column  opendelivery_method varchar(50) null;
ALTER TABLE forma_de_pagamento add column opendelivery_brand varchar(50) null;
ALTER TABLE forma_de_pagamento add column opendelivery_method_info varchar(50) null;

ALTER TABLE opcao_adicional_produto ADD COLUMN qtde_maxima int null;

update cliente_api set identificador = 'gcomapi' where nome ='Gcom api';


alter table comanda add column codigo_pdv varchar(10) null;
alter table disponibilidade add column removida bit(1)   null;

alter table empresa add column modelo_catalogo_da_rede_id bigint null;
ALTER TABLE empresa ADD FOREIGN KEY (modelo_catalogo_da_rede_id) REFERENCES catalogo_da_rede (id);


CREATE TABLE template_de_prompt_db (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  nome VARCHAR(255) NOT NULL,
  descricao LONGTEXT NOT NULL,
  empresa_id BIGINT(20),
  tipo ENUM('global', 'empresa') NOT NULL,

  PRIMARY KEY (id),
  FOREIGN KEY (empresa_id) REFERENCES empresa (id)
);

CREATE TABLE trecho_de_prompt (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  intent VARCHAR(255) NOT NULL,
  texto LONGTEXT NOT NULL,
  tipo ENUM('global', 'empresa') NOT NULL DEFAULT 'empresa',
  template_id BIGINT(20) NOT NULL,
  empresa_id BIGINT(20),

  PRIMARY KEY (id),
  FOREIGN KEY (template_id) REFERENCES template_de_prompt_db(id),
  FOREIGN KEY (empresa_id) REFERENCES empresa(id)
);

CREATE TABLE trecho_de_prompt_empresa (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  trecho_de_prompt_id BIGINT(20) NOT NULL,
  empresa_id BIGINT(20) NOT NULL,
  ativo BOOLEAN NOT NULL,

  PRIMARY KEY (id),
  FOREIGN KEY (trecho_de_prompt_id) REFERENCES trecho_de_prompt(id),
  FOREIGN KEY (empresa_id) REFERENCES empresa(id)
);

ALTER TABLE trecho_de_prompt_empresa
ADD UNIQUE INDEX idx_trecho_de_prompt_empresa (trecho_de_prompt_id, empresa_id);

/*
DROP TABLE IF EXISTS trecho_de_prompt_empresa;
DROP TABLE IF EXISTS trecho_de_prompt;
DROP TABLE IF EXISTS template_de_prompt_db;
*/

INSERT INTO template_de_prompt_db (nome, descricao, tipo, empresa_id)
VALUES ('prompt_chatbot_duvidas', 'Prompt chatbot de dúvidas', 'global', NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Você é a ${nomeMia}, um bot de dúvidas da ${empresa.nome}.', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'A resposta do bot da empresa ${empresa.nome} é curta, educada, criativa, precisa, com emojis whatsapp e na primeira pessoa do singular.', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Você só conhece as seguintes informações:', 'global', 1, NULL);

-- Instrução para a intent HORARIO_ATENDIMENTO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('HORARIO_ATENDIMENTO', 'Hora atual: ${horario}.', 'global', 1, NULL);

-- Instrução para a intent ATENDENTE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('ATENDENTE', 'O atendimento é via mensagens de texto no whatsapp.', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Possível nome do cliente: ${contato.nome}.', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Não invente ou crie produtos nas suas respostas, você não sabe o que tem no cardápio.', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Link do cardápio digital deve ser sempre link [LINK].', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Pedidos só devem ser feitos através do cardápio digital.', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Você não sabe o que tem no cardápio digital.', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Você não tira dúvidas do cardápio, apenas informa o link.', 'global', 1, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Você envia o link do cardápio sempre que o cliente quiser pedir.', 'global', 1, NULL);

-- Instrução para a intent CUMPRIMENTO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('CUMPRIMENTO', 'Responda calorosamente o cliente, envie o [LINK] do cardápio digital e pergunte o que ele precisa.', 'global', 1, NULL);

-- Instrução para a intent ENVIAR_CARDÁPIO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('ENVIAR_CARDÁPIO', 'Se pedir o cardápio digital, enviar o link do cardápio.', 'global', 1, NULL);

-- Instrução para a intent PROMOCOES
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('PROMOCOES', 'Sobre as promoções: ${promocoes}.', 'global', 1, NULL);

-- Instrução para a intent INFORMACOES_CARDAPIO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('INFORMACOES_CARDAPIO', 'Perguntas sobre o cardápio você diz que não sabe a resposta e pede para o cliente verificar o cardápio para saber as informações.', 'global', 1, NULL);

-- Instrução para a intent PROMOCOES
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('PROMOCOES', '${mensagemCupons}.', 'global', 1, NULL);

-- Instrução para a intent PEDIRPELOBOT, PEDIR_AQUI_BOT, PEDIR_USANDO_BOT, PEDIRCITOUPRODUTO, FAZERPEDIDO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('PEDIRPELOBOT, PEDIR_AQUI_BOT, PEDIR_USANDO_BOT, PEDIRCITOUPRODUTO, FAZERPEDIDO', 'Você não sabe o conteúdo do cardápio digital, qualquer pergunta sobre o conteúdo do cardápio da empresa deve ser respondida deixando claro que não sabe sobre os itens do cardápio e pedindo ao cliente que acesse o link do cardápio.', 'global', 1, NULL);

-- Instrução para a intent PEDIRPELOBOT, PEDIR_AQUI_BOT, PEDIR_USANDO_BOT
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('PEDIRPELOBOT, PEDIR_AQUI_BOT, PEDIR_USANDO_BOT', 'Se quiser pedir por aqui, responda com o link do cardápio e cite vantagens, mas pergunte se o cliente quer falar com um atendente.', 'global', 1, NULL);

-- Instrução para a intent FUNCOES_ROBO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('FUNCOES_ROBO', 'Responda que você sabe: Informar Link do Cardápio, Informações de Pedidos Feitos, Promoções, ofertas e cupons, Horários, endereço e pode chamar atendente.', 'global', 1, NULL);

-- Instrução para a intent CONFIRMACAO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('CONFIRMACAO', 'Se o cliente confirmar o pedido: responda de forma bem curta confirmando o pedido, agradeça e diga que já avisou o estabelecimento e dúvidas falar por aqui.', 'global', 1, NULL);

-- Instrução para a intent CONFIRMACAO, AGRADECIMENTO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('CONFIRMACAO, AGRADECIMENTO', 'Confirmação de pedido, Agradecimento, você não envia o link.', 'global', 1, NULL);

-- Instrução para a intent PROBLEMAS
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('PROBLEMAS', 'Ajuda, transfira para um atendente.', 'global', 1, NULL);

-- Instrução para a intent AGRADECIMENTO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('AGRADECIMENTO', 'Se for um agradecimento, você responde de forma bem gentil e calorosa, com bastante emojis.', 'global', 1, NULL);

-- Instrução para a intent REPETIR_PEDIDO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('REPETIR_PEDIDO', 'Se o cliente quiser repetir o último pedido, exiba os dados do pedido e confirme com ele se deseja repetir o pedido.', 'global', 1, NULL);

-- Instrução para a intent REPETIR_PEDIDO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('REPETIR_PEDIDO', 'Dados do último pedido: ${dadosPedido}.', 'global', 1, NULL);

-- Instrução para a intent Horario_Atendimento
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('Horario_Atendimento', 'Horário funcionamento: ${horarioFuncionamento}. Status da loja: ${statusLoja}. Horários do Dia: ${descricaoHorario}. Separe os horários em linhas separadas com emojis.', 'global', 1, NULL);

-- Instrução para a intent FazerPedido, ENVIAR_CARDAPIO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('FazerPedido, ENVIAR_CARDAPIO', 'Novos Pedidos: Enviar o link do cardápio e pedir para fazer por ele.', 'global', 1, NULL);

-- Instrução para a intent INFORMACOES_PEDIDO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('INFORMACOES_PEDIDO', 'Informações sobre Pedidos: Perguntar se deseja falar com um atendente.', 'global', 1, NULL);

-- Instrução para a intent INFORMACOES_PEDIDO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('INFORMACOES_PEDIDO', 'Tempo de Entrega: ${tempoDeEntrega} minutos.', 'global', 1, NULL);

-- Instrução para a intent INFORMACOES_PEDIDO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('INFORMACOES_PEDIDO', 'Se a resposta perguntar sobre falar com atendente, termine a resposta com [ATENDENTE].', 'global', 1, NULL);

-- Instrução para a intent ATENDENTE, PROBLEMAS
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('ATENDENTE, PROBLEMAS', 'Se o cliente pedir para falar, chamar um atendente, você deve informar que vai transferir para o atendente e finalizar com [FimBot].', 'global', 1, NULL);

-- Instrução para a intent ATENDENTE, PROBLEMAS
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('ATENDENTE, PROBLEMAS', 'Se disser atendente, você transfere para o atendente e finaliza com [FimBot].', 'global', 1, NULL);

-- Instrução para a intent ENDERECO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('ENDERECO', 'Endereço da Loja: ${endereco}.', 'global', 1, NULL);

-- Instrução para a intent ENDERECO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('ENDERECO', 'Convide o cliente a visitar a loja.', 'global', 1, NULL);

-- Instrução para a intent ENTREGA
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('ENTREGA', 'Formas de entrega: Fazemos delivery ou retiradas na loja (ir buscar).', 'global', 1, NULL);

-- Instrução para a intent ENTREGA
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('ENTREGA', 'Para verificar se entrega em um endereço, precisa acessar o link do cardápio.', 'global', 1, NULL);

-- Instrução para a intent PROBLEMAS
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('PROBLEMAS', 'Se o cliente disser que está ocorrendo um erro no site, peça desculpas, informe que está transferindo para um atendente e finalize o bot.', 'global', 1, NULL);

-- Instrução para a intent FORA_DO_ESCOPO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('FORA_DO_ESCOPO', 'Fora do Escopo: Diga que você não sabe nada do assunto e diga que você sabe qual é o Link do Cardápio, Informações de Pedidos Feitos, Promoções, ofertas e cupons, Horários, endereço e pode chamar atendente e sugira o link do cardápio.', 'global', 1, NULL);

-- Instrução para a intent FORA_DO_ESCOPO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('FORA_DO_ESCOPO', 'Se perguntarem quem é você, diga que é a ${nomeMia}.', 'global', 1, NULL);

-- Instrução para a intent FORA_DO_ESCOPO
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('FORA_DO_ESCOPO', 'Se perguntarem, diga que você foi construída pela Empresa MeuCardápio https://meucardapio.ai.', 'global', 1, NULL);

-- Instrução para a intent Imagem
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('Imagem', 'Se enviar imagem, responda que como você é uma IA, você não consegue ver imagens e peça para ele dizer textualmente.', 'global', 1, NULL);

-- Instrução para a intent Audio
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('Audio', 'Se o usuário enviar áudio, responda que você ainda não pode ouvir áudios e peça para ele dizer textualmente.', 'global', 1, NULL);

-- Instrução para a intent Video
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('Video', 'Se enviar vídeo, responda que como você é uma IA, você não consegue ver vídeos e peça para ele dizer textualmente.', 'global', 1, NULL);


INSERT INTO template_de_prompt_db (nome, descricao, tipo, empresa_id)
VALUES ('prompt_chatbot_intents', 'Prompt chatbot classficar as intents', 'global', NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Você pré-classifica mensagens que serão enviadas para um chatbot do estabelecimento ${empresa.nome} através do Whatsapp. As mensagens são escritas por pessoas dialogando com o bot.', 'global', 2, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Intents do bot:', 'global', 2, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'PEDIRCITOUPRODUTO CUMPRIMENTO AGRADECIMENTO CONFIRMACAO NEGACAO REPETIR_PEDIDO FUNCOES_ROBO ENVIAR_CARDAPIO FAZERPEDIDO INFORMACOES_CARDAPIO INFORMACOES_PEDIDO PROMOCOES TAXASDEENTREGA ATENDENTE HORARIO_ATENDIMENTO PEDIR_USANDO_BOT PEDIR_AQUI_BOT PROBLEMAS ENTREGA ENDERECO FORA_DO_ESCOPO', 'global', 2, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', 'Dentre as intents acima, responda com apenas uma palavra a classificação da resposta:', 'global', 2, NULL);

-- Instrução para a intent SEMPRE
INSERT INTO trecho_de_prompt (intent, texto, tipo, template_id, empresa_id)
VALUES ('SEMPRE', '${mensagem}', 'global', 2, NULL);

update categoria join catalogo on catalogo.id = catalogo_id join empresa on empresa.catalogo_id = catalogo.id
  set sincronizar_modelo = true
  where codigo_pdv in (2,4,7,8,9,10,11,12,13,14,33,34,43) and rede_id = 1  ;


update categoria set sincronizar_modelo = true  where codigo_pdv != 42 and codigo_pdv != 8013
          and  catalogo_id in (1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476);

alter table produto add column sincronizar_modelos bit(1) default null;
alter table pedido add column tempo_preparacao int null;
alter table order_event add column reason varchar(255) null;
alter table order_event add column reason_deny varchar(255) null;
alter table order_event add index idx_pedido (order_id);


-- adiciona coluna intent para chamada_ia
alter table chamada_ia add column intent varchar(255) null;



alter table empresa_formas_de_entrega add column nao_enviar_bairro bit(1) null default 0;

create table integracao_opendelivery_logistica(
  id bigint(20) NOT NULL auto_increment,
  auth_url varchar(100) not null,
  base_url varchar(100) not null,
  client_id varchar (100) null,
  client_secret  varchar (100) null,
  appid varchar(100) null,
  merchant_id varchar(100) null,
  token varchar(200) not null,
  token_data_expiracao datetime null,
   notificar_retirada bit(1) null,
   notificar_conclusao bit(1) null,
   retornar_na_loja bit(1) null,
   tempo_limite_retirada int null,
   tempo_limite_entrega int null,
   veiculo_padrao longtext not null,
   horario_cadastro datetime not null,
   horario_ativacao datetime null,
   operador_id bigint(20) not null,
   empresa_id bigint(20) not null,
  ativa bit(1) default 0,
  primary key (id),
     foreign key(empresa_id) references empresa(id),
     foreign key(operador_id) references usuario(id)
)  engine=innodb;


CREATE TABLE notificacao_delivery (
   id bigint(20) NOT NULL auto_increment,
   tipo varchar(20)  NOT NULL,
   delivery_id varchar(255) not null,
   horario datetime NOT NULL,
   dados longtext  null,
   executada bit(1) DEFAULT NULL,
   erro varchar(255)  null,
   ignorar bit(1) DEFAULT NULL,
   pedido_id bigint NOT NULL,
   empresa_id bigint NOT NULL,
  primary key (id),
       foreign key(empresa_id) references empresa(id),
       foreign key(pedido_id) references pedido(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;

CREATE TABLE  delivery_pedido (
   id bigint(20) NOT NULL auto_increment,
   delivery_id varchar(255) not null ,
   status varchar(20)  NOT NULL,
   dados longtext  not null,
   foi_aceita bit default null,
   horario datetime NOT NULL,
   erro varchar(255)  null,
   pedido_id bigint NOT NULL,
   empresa_id bigint NOT NULL,
  primary key (id),
       foreign key(empresa_id) references empresa(id),
       foreign key(pedido_id) references pedido(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;


alter table pedido add column delivery_pedido_id bigint(20) null;
alter table pedido add FOREIGN KEY (delivery_pedido_id) REFERENCES delivery_pedido(id);
alter table pedido add column erro_externo_delivery longtext null;


/* adiciona uma coluna timestamp unix com horário atual na tabela mensagem_whatsapp_web */
alter table mensagem_whatsapp_web add column horario int null default (UNIX_TIMESTAMP());


alter table integracao_opendelivery_logistica modify client_secret varchar (255) null;

create table integracao_uberdirect(
  id bigint(20) NOT NULL auto_increment,
  client_id varchar (100) null,
  client_secret  varchar (100) null,
  costumer_id varchar(100) null,
  signing_key varchar(200) not null,
  token varchar(500) not null,
  token_data_expiracao datetime null,
  horario_cadastro datetime not null,
  operador_id bigint(20) not null,
  empresa_id bigint(20) not null,
  ativa bit(1) default 0,
  primary key (id),
     foreign key(empresa_id) references empresa(id),
     foreign key(operador_id) references usuario(id)
)  engine=innodb;


alter table integracao_uberdirect add column restaurante bit(1) default null;
alter table integracao_uberdirect add column tempo_preparo int default null;
alter table integracao_uberdirect add column instrucoes_retirada longtext default null;


update integracao_opendelivery set ativa = true;
update integracao_opendelivery_logistica set ativa = true;
alter table taxa_de_entrega_calculada add column simulacao_id varchar(50) null;
alter table pedido add column taxa_de_entrega_calculada_id bigint(20) null;
alter table pedido add FOREIGN KEY (taxa_de_entrega_calculada_id) REFERENCES taxa_de_entrega_calculada(id);

alter table delivery_pedido add column origem varchar(20) not null;
update delivery_pedido set origem = 'opendelivery';
alter table notificacao_delivery add column origem varchar(20) not null;
update notificacao_delivery set origem = 'opendelivery';


alter table empresa_formas_de_entrega add column taxa_extra_retorno int null;
alter table taxa_de_entrega_calculada add column taxa_retorno int null;
alter table forma_de_pagamento add column cobrar_taxa_retorno bit(1) null;


insert into integracao_delivery(token,sistema,empresa_id,data,ativa)
    values ('253e51e782de4dd8ad6f7813aec0acc2','foodydelivery',694,'2021-05-11 10:00:00',true);
CREATE TABLE intent (
   id INT AUTO_INCREMENT,
   nome VARCHAR(255) NOT NULL,
   data_criacao BIGINT NOT NULL,
   empresa_id INT NOT NULL,
   PRIMARY KEY (id),
   FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE trecho_de_prompt CHANGE tipo escopo ENUM('global', 'empresa') NOT NULL DEFAULT 'empresa';

CREATE TABLE exemplo_prompt (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  trecho_de_prompt_id BIGINT(20) NOT NULL,
  pergunta LONGTEXT NOT NULL,
  resposta LONGTEXT NOT NULL,
  empresa_id BIGINT(20),
  PRIMARY KEY (id),
  FOREIGN KEY (trecho_de_prompt_id) REFERENCES trecho_de_prompt(id),
  FOREIGN KEY (empresa_id) REFERENCES empresa(id)
);


ALTER TABLE trecho_de_prompt ADD COLUMN tipo ENUM('texto', 'exemplos') DEFAULT 'texto';


CREATE TABLE caixa (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  empresa_id BIGINT(20) NOT NULL,
  operador_id BIGINT(20) NOT NULL,
  horario_abertura DATETIME NOT NULL,
  horario_fechamento DATETIME NULL,
  saldo_inicial_em_centavos DECIMAL(12) NOT NULL,
  saldo_final_em_centavos DECIMAL(12) NULL,
  saldo_apurado_em_centavos DECIMAL(12) NULL,
  saldo_final_total DECIMAL(10,2) NULL,
  forma_dinheiro_id BIGINT(20) NOT NULL,
  status varchar(20) NOT NULL,

  PRIMARY KEY (id),
  FOREIGN KEY (empresa_id) REFERENCES empresa(id),
  FOREIGN KEY (operador_id) REFERENCES usuario(id),
  FOREIGN KEY (forma_dinheiro_id) REFERENCES forma_de_pagamento(id)
);

alter table caixa add column saldo_apurado_em_centavos decimal(12) null;

CREATE TABLE transacao (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  descricao varchar(255) NOT NULL,
  caixa_id BIGINT(20) NOT NULL,
  operador_id BIGINT(20) NULL,
  horario DATETIME NOT NULL,
  valor_em_centavos DECIMAL(12) NOT NULL,
  tipo varchar(20) NOT NULL,
  pedido_id BIGINT(20) NULL,
  comanda_id BIGINT(20) NULL,
  estornavel bit(1) NOT NULL,
  forma_de_pagamento_id BIGINT(20) NOT NULL,

  PRIMARY KEY (id),
  FOREIGN KEY (caixa_id) REFERENCES caixa(id),
  FOREIGN KEY (operador_id) REFERENCES usuario(id),
  FOREIGN KEY (pedido_id) REFERENCES pedido(id),
  FOREIGN KEY (comanda_id) REFERENCES comanda(id),
  FOREIGN KEY (forma_de_pagamento_id) REFERENCES forma_de_pagamento(id)
);

CREATE TABLE saldo_da_forma_de_pagamento (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  caixa_id BIGINT(20) NOT NULL,
  forma_de_pagamento_id BIGINT(20) NOT NULL,
  ultima_atualizacao DATETIME NOT NULL,
  saldo_em_centavos DECIMAL(12) NOT NULL,

  PRIMARY KEY (id),
  FOREIGN KEY (caixa_id) REFERENCES caixa(id),
  FOREIGN KEY (forma_de_pagamento_id) REFERENCES forma_de_pagamento(id)
);


insert into modulo(id, nome) values (9, 'pdv');

CREATE TABLE sangria (
  id BIGINT(20) AUTO_INCREMENT,
  valor_retirado_em_centavos INT NOT NULL,
  data_hora_realizada TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  operador_id bigint(20) NOT NULL,
  justificativa TEXT NOT NULL,
  caixa_id BIGINT(20) NOT NULL,
  empresa_id BIGINT(20) NOT NULL,
  PRIMARY KEY(id),
  FOREIGN KEY(operador_id) REFERENCES usuario(id),
  FOREIGN KEY(caixa_id) REFERENCES caixa(id),
  FOREIGN KEY(empresa_id) REFERENCES empresa(id)
);


CREATE TABLE integracao_ifood (
  id BIGINT(20) AUTO_INCREMENT,
  id_loja varchar(255) not null,
  token longtext not null,
  validade_token datetime not null,
  data_ativacao datetime not null,
  empresa_id BIGINT(20) NOT NULL,
  aceitar_automatico bit(1) not null,
     PRIMARY KEY(id),
       FOREIGN KEY(empresa_id) REFERENCES empresa(id)
);

alter table produto_template_opcao add column removido bit(1) null;

alter table empresa add column access_token_api_conversoes varchar(255) null;
alter table integracao_opendelivery_logistica add column automatico bit(1) default null;

//adiciona coluna idw (id_whatsapp) para a tabela mensagem_enviada

alter table mensagem_enviada add column id_whatsapp varchar(255) null;


INSERT INTO template_de_prompt_db (nome, descricao, tipo, empresa_id)
VALUES ('prompt_chatbot_fazer_pedido', 'Prompt chatbot fazer pedido', 'global', NULL);


ALTER TABLE trecho_de_prompt_empresa ADD COLUMN ordem INT NOT NULL DEFAULT 0;


SET @row_number = 0;
UPDATE trecho_de_prompt_empresa
SET ordem = (@row_number:=@row_number + 1)
ORDER BY id ASC;


alter table estado_chatbot add column historico longtext null;
alter table integracao_opendelivery  add column app_id varchar(255) null;

alter table estado_chatbot add column estado varchar(100) null;

alter table estado_chatbot add column desativar_para_sempre bit(1) null;


CREATE TABLE produto_embeddings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id bigint(20) not null,
    produto VARCHAR(255) NOT NULL,
    produto_id bigint(20) not null,
    embedding longtext NOT NULL,
    foreign key(empresa_id) references empresa(id)
);
ALTER TABLE produto_embeddings ADD UNIQUE INDEX empresa_produto_index (empresa_id, produto_id);

alter table configuracoes_mia add column responder_sobre_produtos bit(1) default 0;

alter table configuracoes_mia add column recuperar_carrinho bit(1) default 0;

alter table configuracoes_mia add column tempo_recuperar_carrinho int default 1200;


alter table sessao_link_saudacao add column dados_produto longtext null;

alter table produto_embeddings add column descricao text null;


insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  false, id, 'Carrinho Abandonado',  'Altere a mensagem', true, 7,-1
        from empresa where removida is not true;

ALTER TABLE notificacao ADD tem_menu bit(1) NOT NULL default 0;
ALTER TABLE notificacao ADD texto_botao VARCHAR(255) NULL default '';
ALTER TABLE notificacao ADD menu TEXT NULL DEFAULT NULL;

ALTER TABLE campanha ADD tem_menu bit(1) NOT NULL default 0;
ALTER TABLE campanha ADD texto_botao VARCHAR(255) NULL  default '';
ALTER TABLE campanha ADD menu TEXT NULL;

ALTER TABLE mensagem_enviada ADD tem_menu bit(1) NOT NULL default 0;
ALTER TABLE mensagem_enviada ADD texto_botao VARCHAR(255) NULL default '';
ALTER TABLE mensagem_enviada ADD menu TEXT NULL;
alter table opcao_adicional_produto add column qtde_minima int default null;

alter table integracao_uberdirect add column acaoretorno int default 1;
alter table integracao_opendelivery_logistica add column nao_enviar_localizacao bit(1) null;
alter table numero_whatsapp add column ocultar bit(1) null;

alter table grupo_de_lojas add column favicon varchar(255) null;
alter table grupo_de_lojas add column gtag varchar(255) null;


alter table empresa_formas_de_entrega add column bloquear_bairro_apos_cep bit(1) null default 0;

alter table caixa modify column operador_id bigint(20) null;


<!-- integracao ifood ->
CREATE TABLE notificacao_ifood(
   id varchar(255) NOT NULL,
   code varchar(10)  NOT NULL,
   full_code varchar(20)  NOT NULL,
   order_id varchar(255) not null,
   horario  datetime NOT NULL,
   horario_criacao datetime NOT NULL,
   executada bit(1) DEFAULT NULL,
   erro varchar(255)  null,
   ignorar bit(1) DEFAULT NULL,
   dados longtext null,
  primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;


CREATE TABLE pedido_disputa(
  id BIGINT(20) AUTO_INCREMENT,
  pedido_id BIGINT(20) not null,
  guid varchar(200) not null,
  acao varchar(50) not null,
  tipo varchar(50) not null,
  acao_timeout varchar(50) not null,
  justificativa varchar(400) null,
  horario datetime not null,
  data_criacao datetime not null,
  data_expiracao datetime not null,
  dados_disputa longtext null,
  dados_acordo longtext null,
  resultado_disputa varchar(100)  null,
  motivo_rejeicao  varchar(255)  null,
  empresa_id BIGINT(20) not null,
  aguardando_proposta bit(1) default null,
  legado bit(1) default null,
  finalizada bit(1) default null,
  PRIMARY KEY(id),
    FOREIGN KEY(pedido_id) REFERENCES pedido(id),
    FOREIGN KEY(empresa_id) REFERENCES empresa(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;


CREATE TABLE pedido_falha_integracao(
  id BIGINT(20) AUTO_INCREMENT,
  horario datetime not null,
  empresa_id BIGINT(20) not null,
  sistema varchar(20) not null,
  payload longtext not null,
  erro longtext not null,
  pedido_id  BIGINT(20) null,
  lido bit(1) default null,
    PRIMARY KEY(id),
      FOREIGN KEY(empresa_id) REFERENCES empresa(id),
            FOREIGN KEY(pedido_id) REFERENCES pedido(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;


create table pedido_beneficio(
 id BIGINT(20) AUTO_INCREMENT,
 valor decimal(9,2 ) not null,
 target varchar(50) not null ,
 patrocinio varchar(100) not null,
 pedido_id  BIGINT(20)  not null,
    PRIMARY KEY(id),
          FOREIGN KEY(pedido_id) REFERENCES pedido(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;


CREATE TABLE  bandeira (
    id BIGINT(20) AUTO_INCREMENT,
    nome varchar(50)  NOT NULL,
    tipo varchar(50)  NOT NULL,
    imagem LONGBLOB NULL,
    opendelivery_brand varchar(50) NULL,
    PRIMARY KEY(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;


CREATE TABLE  forma_de_pagamento_pdv (
    id BIGINT(20) AUTO_INCREMENT,
    nome varchar(50)  NOT NULL,
    tipo varchar(10)  NOT NULL,
    metodo varchar(20)  NOT NULL,
    opendelivery_method varchar(50) DEFAULT NULL,
    opendelivery_method_info varchar(50) DEFAULT NULL,
    removido bit(1) null,
      PRIMARY KEY(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;


CREATE TABLE forma_de_pagamento_pdv_forma_integrada(
 forma_de_pagamento_pdv_id BIGINT(20) not null,
 forma_de_pagamento_integrada_id  BIGINT(20) not null,
      FOREIGN KEY(forma_de_pagamento_pdv_id) REFERENCES forma_de_pagamento_pdv(id),
      FOREIGN KEY(forma_de_pagamento_integrada_id) REFERENCES forma_de_pagamento_integrada_nova(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;


CREATE TABLE  forma_de_pagamento_pdv_bandeira (
   id BIGINT(20) AUTO_INCREMENT,
   forma_de_pagamento_pdv_id BIGINT(20) not null,
   bandeira_id BIGINT(20) not null,
   ativo bit(1) default  b'1',
        PRIMARY KEY(id),
     FOREIGN KEY(forma_de_pagamento_pdv_id) REFERENCES forma_de_pagamento_pdv(id),
              FOREIGN KEY(bandeira_id) REFERENCES bandeira(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ;

alter table forma_de_pagamento add column forma_de_pagamento_pdv_id  BIGINT(20) null;
alter table forma_de_pagamento add FOREIGN KEY (forma_de_pagamento_pdv_id) REFERENCES forma_de_pagamento_pdv(id);
alter table forma_de_pagamento add column bandeira_id  BIGINT(20) null;
alter table forma_de_pagamento add FOREIGN KEY (bandeira_id) REFERENCES bandeira(id);

alter table forma_de_pagamento add column chave_pix varchar(100) null;
alter table forma_de_pagamento add column  taxa_recebimento DECIMAL(10,2) default 0;
alter table forma_de_pagamento add column  dias_recebimento int default 0;

alter table contato add column codigo_ifood varchar(40) null;
ALTER TABLE contato ADD INDEX idx_codifood (codigo_ifood);

alter table pedido  add  column  ifood_telefone_contato varchar(300) null;
alter table pedido  add  column  ifood_taxa_servico decimal(9,2) null;
alter table pedido  add  column  ifood_codigo_retirada varchar(50) null;

alter table empresa add column exibir_bandeiras bit(1) null;
alter table horario_funcionamento add column servico varchar(100) default 'site';

alter table categoria add column imagem varchar(255) null;
alter table empresa add column imagem_categoria_destaque varchar(255) null;
alter table cardapio add column exibir_selecao_categorias bit(1) null;


ALTER TABLE pedido CHANGE ifood_telefone_contato ifood_extra VARCHAR(300);

CREATE TABLE produto_vendido (
  id BIGINT(20)  NOT NULL AUTO_INCREMENT,
  nome varchar(255) NOT NULL,
  codigo varchar(255) NULL,
  valor decimal(10,2) NOT NULL,
  quantidade  decimal(9,3) NOT NULL,
  total decimal(10,2) NOT NULL,
  tipo varchar(20) NOT NULL,
  indice int NOT NULL,
  empresa_id bigint NOT NULL,
  pedido_id bigint NOT NULL,
  produto_id BIGINT(20)   NULL,
  opcao_id BIGINT(20)   NULL,
  adicional_nome varchar(255)   NULL,
  PRIMARY KEY (id),
  FOREIGN KEY(empresa_id) REFERENCES empresa(id),
  FOREIGN KEY(produto_id) REFERENCES produto(id),
  KEY opcao_id (opcao_id),
  KEY produto_id (produto_id)
) ENGINE=InnoDB;


CREATE TABLE `registro_de_login` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ip` varchar(45) DEFAULT NULL,
  `id_sessao` varchar(255) DEFAULT NULL,
  `dia_acesso` date DEFAULT NULL,
  `horario_acesso` time DEFAULT NULL,
  `tipo_dispositivo` varchar(255) DEFAULT NULL,
  `detalhes_dispositivo` text,
  `nome_navegador` varchar(255) DEFAULT NULL,
  `versao_navegador` varchar(50) DEFAULT NULL,
  `url` text,
  `origem` varchar(255) DEFAULT NULL,
  `empresa_id` bigint NOT NULL,
  `dataCriacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dia_logout` date DEFAULT NULL,
  `horario_logout` time DEFAULT NULL,
  `sessao_ativa` tinyint(1) NOT NULL DEFAULT '1',
  `usuario_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `empresa_id` (`empresa_id`),
  KEY `usuario_id` (`usuario_id`),
  CONSTRAINT `registro_de_login_ibfk_1` FOREIGN KEY (`empresa_id`) REFERENCES `empresa` (`id`),
  CONSTRAINT `registro_de_login_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuario` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into operacao_do_sistema(nome,descricao) values ('Alterar preços locais da rede', 'Permissão para alterar preços locais em um catálogo compartilhado de rede.');

alter table produto_na_empresa modify column preco decimal(10,2) null;


alter table cupom add column escopo varchar(50) null default 'livre';
