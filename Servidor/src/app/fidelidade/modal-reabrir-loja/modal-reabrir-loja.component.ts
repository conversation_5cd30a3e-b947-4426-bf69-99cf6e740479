import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import {EmpresasService} from "../../superadmin/services/empresas.service";
import {NgForm} from "@angular/forms";

@Component({
  selector: 'app-modal-reabrir-loja',
  templateUrl: './modal-reabrir-loja.component.html',
  styleUrls: ['../modal-fechar-loja/modal-fechar-loja.component.scss', './modal-reabrir-loja.component.scss']
})
export class ModalReabrirLojaComponent implements OnInit {
  @Output() onFecharModal = new EventEmitter<any>();
  @Input() empresa: any = {};
  @ViewChild('frm',  {static: false} ) frm: NgForm;
  tipoReaberturaSelecionado: 'agora' | 'programar' | null = null;

  // Campos para programação
  dataFechamento: Date ;
  horaFechamento: Date;
  dataReabertura: Date ;
  horaReabertura: Date;
  salvando = false;
  msgErro: string;
  novaPausaProgramada: any = {};
  constructor(private empresasService: EmpresasService) {}

  ngOnInit() {
    this.dataFechamento = new Date();
  }

  selecionarTipoReabertura(tipo: 'agora' | 'programar') {
    this.tipoReaberturaSelecionado = tipo;
  }

  voltarSelecao() {
    this.tipoReaberturaSelecionado = null;
  }

  reabrirAgora() {
    this.salvando = true;
    this.empresasService.reabraLojaDaPausa(this.empresa, null).then(() => {
      this.salvando = false;
      this.fechar(true);
     }).catch((err) => {
      this.salvando = false;
      this.msgErro = err.message || err;
    })
  }

  reprogramarNova(){
    return this.tipoReaberturaSelecionado === 'programar';
  }

  confirmarReabertura() {
    if(!this.frm.valid) return;

    this.salvando = true;
    this.dataFechamento.setHours(this.horaFechamento.getHours(), this.horaFechamento.getMinutes(), 0, 0);

    // Configura data e hora de reabertura (se preenchido)
    if (this.horaReabertura)
      this.dataReabertura.setHours( this.horaReabertura.getHours(),  this.horaReabertura.getMinutes(), 0, 0);

    this.novaPausaProgramada.dataInicio = this.dataFechamento;
    this.novaPausaProgramada.dataFim = this.dataReabertura;

    this.empresasService.reabraLojaDaPausa(this.empresa,  this.novaPausaProgramada).then(() => {
      this.salvando = false;
      this.fechar(true);
    }).catch((err) => {
      this.salvando = false;
      this.msgErro = err.message || err;
    })
  }

  fechar(abriu: boolean = false) {
    this.onFecharModal.emit({ abriu: abriu });
  }
}
