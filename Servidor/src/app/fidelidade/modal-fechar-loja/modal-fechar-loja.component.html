<div class="modal-fechar-loja">
  <div class="header">
    <h4><PERSON><PERSON><PERSON></h4>
    <button class="btn btn-link btn-fechar" (click)="fechar()">
      <i class="fas fa-times" style="    font-size: 20px;"></i>
    </button>
  </div>

  <div class="content">
    <!-- Header com título dinâmico e ícone -->
    <div class="tipo-fechamento-header">
      <div class="header-content">
        <i *ngIf="!tipoFechamentoSelecionado" class="fas fa-store"></i>
        <i *ngIf="tipoFechamentoSelecionado === 'agora'" class="fas fa-power-off"></i>
        <i *ngIf="tipoFechamentoSelecionado === 'intervalo'" class="fas fa-clock"></i>
        <i *ngIf="tipoFechamentoSelecionado === 'agendar'" class="fas fa-calendar-alt"></i>

        <h5 *ngIf="!tipoFechamentoSelecionado">Como você deseja fechar a loja?</h5>
        <h5 *ngIf="tipoFechamentoSelecionado === 'agora'">Fechamento Imediato</h5>
        <h5 *ngIf="tipoFechamentoSelecionado === 'intervalo'">Fechamento por Intervalo</h5>
        <h5 *ngIf="tipoFechamentoSelecionado === 'agendar'">Agendar Fechamento</h5>
      </div>
    </div>

    <!-- Opções de Fechamento -->
    <div class="options-container" *ngIf="!tipoFechamentoSelecionado">
        <!-- Opção 1: Fechar Agora -->
      <!--  <div class="option-card" (click)="selecionarTipoFechamento('agora')">
         <i class="fas fa-power-off"></i>
         <div class="option-content">
           <h5>Fechar Agora</h5>
           <p>Fechar a loja imediatamente sem previsão de retorno</p>
         </div>
       </div>-->

       <!-- Opção 2: Fechar por Intervalo -->
      <div class="option-card" (click)="selecionarTipoFechamento('intervalo')">
        <i class="fas fa-clock"></i>
        <div class="option-content">
          <h5>Fechar por Intervalo</h5>
          <p>Fechar a loja imediatamente por um tempo pré-definido</p>
        </div>
      </div>

      <!-- Opção 3: Agendar Fechamento -->
      <div class="option-card" (click)="selecionarTipoFechamento('agendar')">
        <i class="fas fa-calendar-alt"></i>
        <div class="option-content">
          <h5>Agendar Fechamento</h5>
          <p>Definir data e hora específica</p>
        </div>
      </div>

      <a [routerLink]="'/admin/loja/tab/pausasprogramadas'" class="text-center mt-3" (click)="fecheComDelay()">Ver todas já cadastradas</a>

    </div>

    <!-- Conteúdo específico para cada tipo de fechamento -->
    <div class="fechamento-content" *ngIf="tipoFechamentoSelecionado">

      <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
            novalidate #frm="ngForm" (ngSubmit)="onSalvar()">
        <!-- Opção 2: Fechar por Intervalo -->
        <div class="option-section" *ngIf="tipoFechamentoSelecionado === 'intervalo'">
          <h5>Selecione o intervalo</h5>
          <div class="intervalo-buttons">
            <button *ngFor="let intervalo of intervalos" type="button"
                    class="btn btn-outline-primary intervalo-btn"
                    [class.selected]="intervaloSelecionado === intervalo.valor"
                    (click)="selecionarIntervalo(intervalo.valor)">
              {{intervalo.label}}
            </button>
          </div>

          <p *ngIf="mensagemErroIntervalo" class="text-danger"><b>{{mensagemErroIntervalo}}</b></p>

        </div>

        <!-- Opção 3: Agendar Fechamento -->
        <div class="option-section" *ngIf="agendamento()">
          <h5>Defina o período de fechamento</h5>

          <!-- Data e Hora de Início -->
          <div class="periodo-grupo">
            <div class="periodo-header">
              <i class="fas fa-play-circle"></i>
              <span>Início</span>
            </div>
            <div class="periodo-campos">
              <div class="form-group">
                <label>Data</label>
                <kendo-datepicker required #dtInicio="ngModel" name="dtInicio"
                  [(ngModel)]="pausaProgramada.dataInicio"
                  [format]="'dd/MM/yyyy'">
                </kendo-datepicker>
                <div class="invalid-feedback">
                  <p *ngIf="dtInicio.errors?.required">Data obrigatório</p>
                </div>
              </div>
              <div class="form-group">
                <label>Hora</label>
                <kendo-timepicker required #hrInicio="ngModel" name="hrInicio"
                  [(ngModel)]="pausaProgramada.horaInicio"
                  format="HH:mm">
                </kendo-timepicker>
                <div class="invalid-feedback">
                  <p *ngIf="hrInicio.errors?.required">Horário obrigatório</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Data e Hora de Fim -->
          <div class="periodo-grupo">
            <div class="periodo-header">
              <i class="fas fa-stop-circle"></i>
              <span>Fim</span>
            </div>
            <div class="periodo-campos">
              <div class="form-group">
                <label>Data</label>
                <kendo-datepicker
                  [(ngModel)]="pausaProgramada.dataFim" #dtFim="ngModel" name="dtFim"
                  [format]="'dd/MM/yyyy'">
                </kendo-datepicker>

              </div>
              <div class="form-group">
                <label>Hora</label>
                <kendo-timepicker  [required]="pausaProgramada.dataFim != null"
                  [(ngModel)]="pausaProgramada.horaFim" #hrFim="ngModel" name="hrFim"
                  format="HH:mm">
                </kendo-timepicker>
                <div class="invalid-feedback">
                  <p *ngIf="hrFim.errors?.required">Horário obrigatório</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Campos comuns para todos os tipos -->
        <div class="row mt-2" *ngIf="!fecharAgora()">
          <div class="form-group mb-3 col">
            <label for="descricao">Descrição da pausa programada</label>
            <input type="text" class="form-control" autocomplete="off"
                   id="descricao" name="descricao" [(ngModel)]="pausaProgramada.descricao" #descricao="ngModel"
                   placeholder="Descricao da Pausa Programada" value="" required   appAutoFocus [autoFocus]="true">
            <div class="invalid-feedback">
              <p *ngIf="descricao.errors?.required">Descrição é obrigatório</p>
            </div>
          </div>
        </div>

        <div class="row mt-2">
          <div class="form-group mb-3 col">
            <label for="mensagem">Mensagem para cliente (opcional)</label>
            <input type="text" class="form-control" autocomplete="off"
                   id="mensagem" name="mensagem" [(ngModel)]="pausaProgramada.mensagem" #mensagem="ngModel"
                   placeholder="Mensagem que será exibida no cardápio durante a pausa" value="">
          </div>
        </div>

        <div class="submit mt-3">
          <div class="alert alert-danger mb-3" *ngIf="mensagemErro">
            {{mensagemErro}}
          </div>

          <button type="submit" class="btn btn-primary btn-block waves-effect waves-light" [disabled]="salvando" >
            <i class="k-icon k-i-loading mr-2" *ngIf="salvando"></i>

              {{agendamento() ? '  Agendar Fechamento da Loja' : 'Fechar Loja Agora'}}
          </button>


          <button class="btn btn-link mb-3 btn-block" type="button" (click)="voltarSelecao()">
            <i class="fas fa-arrow-left"></i> Voltar
          </button>


        </div>
      </form>


    </div>
  </div>
</div>
