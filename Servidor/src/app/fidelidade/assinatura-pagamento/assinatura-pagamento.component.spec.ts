import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AssinaturaPagamentoComponent } from './assinatura-pagamento.component';

describe('AssinaturaPagamentoComponent', () => {
  let component: AssinaturaPagamentoComponent;
  let fixture: ComponentFixture<AssinaturaPagamentoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ AssinaturaPagamentoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AssinaturaPagamentoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
