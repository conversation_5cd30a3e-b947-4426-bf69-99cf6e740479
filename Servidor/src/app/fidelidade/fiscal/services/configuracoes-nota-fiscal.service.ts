import { Injectable } from '@angular/core';
import {ServerService} from "../../../services/ServerService";
import {HttpClient} from "@angular/common/http";
import {TributacaoNaturezaOperacao} from "../../../../../server/domain/nfce/TributacaoNaturezaOperacao";

@Injectable({
  providedIn: 'root'
})
export class ConfiguracoesNotaFiscalService extends ServerService {

  constructor(http: HttpClient) {
    super(http);
  }

  obtenhaConfiguracoesNotaFiscal() {
    return this.obtenha('/nfce/configuracoes', {});
  }

  salveConfiguracoesNotaFiscal(configuracoes: any) {
    return this.salve('/nfce/configuracoes', configuracoes);
  }

  salveTributacaoNaturezaOperacao(tributacao: any, tipo: string) {
    return this.salve('/nfce/tribnatop', {
      dados: tributacao,
      tipo: tipo
    })
  }


}
