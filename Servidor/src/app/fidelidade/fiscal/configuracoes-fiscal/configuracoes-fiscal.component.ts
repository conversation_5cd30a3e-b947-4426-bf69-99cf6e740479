import {Component, OnInit, ViewChild} from '@angular/core';
import {ConfiguracoesNotaFiscalService} from "../services/configuracoes-nota-fiscal.service";
import {NgForm} from "@angular/forms";
import {FileRestrictions, SuccessEvent, UploadEvent} from "@progress/kendo-angular-upload";
import {FiscalService} from "../services/fiscal.service";

@Component({
  selector: 'app-configuracoes-fiscal',
  templateUrl: './configuracoes-fiscal.component.html',
  styleUrls: ['./configuracoes-fiscal.component.scss']
})
export class ConfiguracoesFiscalComponent implements OnInit {
  @ViewChild('frmConfigNotasFiscais')   frmConfigNotasFiscais: NgForm;
  files: any;
  restricoes: FileRestrictions = {
    allowedExtensions: ['.pfx', '.p12' ]
  };
  configuracoes: any = {};
  salvando: boolean;
  mensagemSucesso: string;
   erro: string;
  exibirSenha: boolean;
  arquivoCertificado: any = {};
  regimesTributarios: any = [];

  constructor(private configuracoesNotaService: ConfiguracoesNotaFiscalService,
              private fiscalService: FiscalService) {
    this.configuracoesNotaService.obtenhaConfiguracoesNotaFiscal().then((configuracoes: any) => {
      if(configuracoes) {
        this.configuracoes = configuracoes;
        //o carregamento abaixo é apenas para o regime selecionado aparecer assim que a tela for carregada
        if(this.configuracoes.regimeTributario) {
          this.regimesTributarios = [this.configuracoes.regimeTributario];
        }
      }

      this.fiscalService.listeRegimesTributarios().then((regimes: any) => {
        this.regimesTributarios = regimes;

        if(this.configuracoes.regimeTributario) {
          for(let regime of this.regimesTributarios) {
            if(regime.id === this.configuracoes.regimeTributario.id) {
              this.configuracoes.regimeTributario = regime;
            }
          }
        }
      });
    });
  }

  ngOnInit(): void {

  }

  uploadFalhou($event: any) {

  }

  progressoUpload($event: any) {

  }

  evtTerminouUpload($event: any) {

  }

  usuarioEnviouArquivo($event: UploadEvent) {
    const respUpload = $event;

    this.configuracoes.nomeArquivoPfx = respUpload.files[0].name;
  }


  onSubmitConfigNotasFiscais() {
    this.mensagemSucesso = '';
    delete this.erro;
    if( !this.frmConfigNotasFiscais.valid || this.salvando)   {
      this.erro = "Preencha todos os campos antes de salvar"
      return;
    }

    this.salvando = true;
    this.configuracoesNotaService.salveConfiguracoesNotaFiscal(this.configuracoes).then( (configuracoesSalvas) => {
        this.salvando = false;
        this.mensagemSucesso = "As configurações de notas fiscais foram atualizadas";
    }).catch( (erro) => {
      this.salvando = false;
      this.erro = erro;
    });

  }

  exibirSenhaTela() {
    this.exibirSenha = !this.exibirSenha;
  }
}
