import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import {PedidosService} from "../services/pedidos.service";
import {ComandosImprimirComanda} from "../pedidos/ComandosImprimirComanda";
import {ConstantsService} from "../fidelidade/ConstantsService";
import {ImpressaoService} from "../services/impressao.service";
import {EntregadorService} from "../services/entregador.service";
import {ControleKanbamComponent} from "../controle-kanbam/controle-kanbam.component";

@Component({
  selector: 'app-kanban-pedidos',
  templateUrl: './kanban-pedidos.component.html',
  styleUrls: ['./kanban-pedidos.component.scss']
})
export class KanbanPedidosComponent extends ComandosImprimirComanda implements OnInit {
  @Input() comanda;
  @Input() modoGrupoLojas: any = false;
  @Input() modoSimples = false;
  @Output() onAtualizouComanda = new EventEmitter();
  entregadores: any = [];
  todo: any[] = [];
  @ViewChild('ctlKanban', { static: true }) ctlKanban: ControleKanbamComponent;
  colunas: string[] = [];

  constructor(protected pedidosService: PedidosService, protected constantsService: ConstantsService,
              protected impressao: ImpressaoService, private entregadorService: EntregadorService) {
    super(pedidosService, constantsService, impressao);
  }

  ngOnInit(): void {
    this.colunas = [
      'Novo',
      'Em preparação',
      'Saiu para entrega',
      'Entregue'
    ];

    super.ngOnInit();

    this.entregadorService.liste().then((entregadores: any) => {
      this.entregadores = entregadores
    });

    this.pedidosService.listePedidos(0, 1000).then((respPedidos) => {
      const pedidos = respPedidos.pedidos;

      const lista = [];
      for( let i = 0; i < pedidos.length; i++ ) {
        const pedido = pedidos[i];
        lista.push(pedido);
      }

      this.todo = lista;

      setTimeout( () => {
        this.ctlKanban.atualize();
      }, 0);
    });
  }

  protected readonly status = status;
}
