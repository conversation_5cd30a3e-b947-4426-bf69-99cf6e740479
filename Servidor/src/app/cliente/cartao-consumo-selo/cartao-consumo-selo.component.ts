import {Component, Input, OnInit} from '@angular/core';
import {TelaCartaoComTabs} from "../cartao-pontos/cartao-pontos.component";

@Component({
  selector: 'app-cartao-consumo-selo',
  templateUrl: './cartao-consumo-selo.component.html',
  styleUrls: [ '../cartao-selo/cartao-selo.component.scss' , './cartao-consumo-selo.component.scss']
})
export class CartaoConsumoSeloComponent extends TelaCartaoComTabs implements OnInit {
  @Input() cartao: any = { pontos: 0};
  matrizSelos: Array<Array<any>>;
  listaMatrizSelos = [];
  pontosNecessarios = 12;
  qtdePorLinha = 5;

  constructor() { super() }

  ngOnInit() {
    this.configureSelos();
  }

  private configureSelos() {
    this.listaMatrizSelos = [];

    if(this.cartao.pontos > this.pontosNecessarios)
      this.pontosNecessarios = this.cartao.pontos;

    let totalPontos = 0;
    this.listaMatrizSelos.push(this.obtenhaMatrizSelos(totalPontos, this.cartao.pontos));

  }

  obtenhaMatrizSelos(inicio, pontosSobrando): Array<Array<any>> {
    let quantidadeLinhas = Math.ceil(this.pontosNecessarios / this.qtdePorLinha);
    let selosPorLinha = Math.ceil(this.pontosNecessarios / quantidadeLinhas)
    this.matrizSelos = [];

    for(let linha = 0; linha < quantidadeLinhas; linha++) {
      let selos = [];

      for (let coluna = 0; coluna < selosPorLinha; coluna++)  {
        let i = coluna + (linha * selosPorLinha);
        let selo = {
          status: 1,
          valor: inicio + i + 1
        }

        if(i < pontosSobrando)
          selo.status = 1;
        else if(i < this.pontosNecessarios)
          selo.status = 0;
        else
          selo.status = -1;

        selos.push(selo)
      }

      if(selos.length === 2) {
        let selo = {
          status: -1,
          valor: 0
        }

        selos.unshift(selo);

        selo = {
          status: -1,
          valor: 0
        }

        selos.push(selo)
      }

      this.matrizSelos.push(selos);
    }


    return this.matrizSelos;
  }

  obtenhaQuantidadeDeLinhas( ) {
    return Math.ceil(this.pontosNecessarios / this.qtdePorLinha);
  }
}
