import { Component, OnInit } from '@angular/core';
import {DialogRef} from "@progress/kendo-angular-dialog";
import {EmpresasService} from "../../superadmin/services/empresas.service";

@Component({
  selector: 'app-tela-usuario-permissoes',
  templateUrl: './tela-usuario-permissoes.component.html',
  styleUrls: ['./tela-usuario-permissoes.component.scss']
})
export class TelaUsuarioPermissoesComponent implements OnInit {
  empresa: any = {};
  usuario: any = {};
  windowRef: DialogRef;
  papeis: any = [];
  carregando = true;
  constructor(private empresasService: EmpresasService) { }

  ngOnInit(): void {

    this.empresasService.listePapeis({id: this.usuario.idEmpresa}).then((papeis) => {
      this.carregando = false;
      this.papeis = papeis;

      this.papeis.forEach((papel: any) => {
        papel.ativo = this.usuario.papeis.find((item: any) => item.id === papel.id) != null;
      })

    })
  }

  alterouPapel(papel: any) {
    if(papel.salvando ) return;

    papel.salvando = true;

    this.empresasService.salvePapelUsuario(this.empresa, this.usuario, papel).then(( ) => {
      papel.salvando = false;
      this.usuario.papeis = this.papeis.filter((item: any) => item.ativo);
    }).catch((erro) => {
      papel.salvando = false;
      papel.ativo =  !papel.ativo ;
      alert(erro)
    })
  }
}
