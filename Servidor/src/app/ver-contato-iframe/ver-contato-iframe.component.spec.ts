import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { VerContatoIframeComponent } from './ver-contato-iframe.component';

describe('VerContatoIframeComponent', () => {
  let component: VerContatoIframeComponent;
  let fixture: ComponentFixture<VerContatoIframeComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ VerContatoIframeComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VerContatoIframeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
