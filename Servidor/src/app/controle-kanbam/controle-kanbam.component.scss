.kanban-board {
  display: flex;
  justify-content: flex-start;
  overflow-x: auto;
  white-space: nowrap;
}

.kanban-column {
  border-right: 1px dashed #ccc;
  padding: 10px;
  box-sizing: border-box;
  flex: 1;
  flex-shrink: 0;
}

.container-scroll {
  overflow-y: auto;
  overflow-x: scroll;
  height: 800px;
  margin-right: 5px;
}

.kanban-column::-webkit-scrollbar-track {
  background: transparent;
}

.example-custom-placeholder {
  background: #ccc;
  border: none;
  min-height: 150px;
  margin-bottom: 10px;
}

.kanban-column::-webkit-scrollbar-thumb {

}

.no-margin-bottom {
  margin-bottom: 0 !important;
}

.kanban-card {
  margin-bottom: 10px;
}

.mat-card-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 5px;
}

.mat-card-subtitle {
  font-size: 13px;
  color: #333;
  margin-bottom: 5px;
}

.preco {
  color: green;
}

.card-footer {
  padding: 8px 16px !important;
  display: flex;
  justify-content: space-between;
}

.card-time {
  font-size: 12px;
  color: #333;
  justify-content: space-between;  /* Para alinhamento horizontal */
  align-items: center;
  height: 30px;
  display: flex;
}


.container-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.container-scroll::-webkit-scrollbar-thumb {
  background: #dde2e6;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  opacity: .4;
  -webkit-transition: .2s;
  transition: .2s;
}

.container-scroll::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.container-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.hovering {
  background-color: rgba(57, 100, 6, 0.05);
  border: 2px dashed rgba(27, 51, 0, 0.2);
}

.flag-container {
  position: relative;
}

.flag-text {
  padding: 5px 10px;
  background-color: green;
  color: white;
  line-height: 1.5em;

  &.cor0 { background-color: #7a7a7a;}
  &.cor1 { background-color: #BB4430;}
  &.cor2 { background-color: #006d77;}
  &.cor3 { background-color: #7EC284;}
  &.cor4 { background-color: #F3DFA2;}
  &.cor5 { background-color: #F39536;}
  &.cor6 { background-color: #85C7F2;}
  &.cor7 { background-color: #4C4C4C;}
  &.cor8 { color: blue;}
  &.cor9 { color: blue;}
}

.flag-container::after {
  content: "";
  position: absolute;
  right: -30px;
  top: 0px;
  border-width: 15px;
  border-color: transparent transparent transparent green;
  border-style: solid;
}

.cor0::after { border-color: transparent transparent transparent #7a7a7a; }
.cor1::after { border-color: transparent transparent transparent #BB4430;}
.cor2::after { border-color: transparent transparent transparent #006d77;}
.cor3::after { border-color: transparent transparent transparent #7EC284;}
.cor4::after { border-color: transparent transparent transparent #F3DFA2;}
.cor5::after { border-color: transparent transparent transparent #F39536;}
.cor6::after { border-color: transparent transparent transparent #85C7F2;}
.cor7::after { border-color: transparent transparent transparent #4C4C4C;}
.cor8::after { color: blue;}
.cor9 { color: blue;}
