import {ApplicationRef, Component, Input, OnInit} from '@angular/core';
import {RecebimentoService} from "../services/recebimento.service";
import {process, State} from "@progress/kendo-data-query";
import {AutorizacaoService} from "../../services/autorizacao.service";

declare  let moment;
@Component({
  selector: 'app-tela-gerenciar-recebimentos',
  templateUrl: './tela-gerenciar-recebimentos.component.html',
  styleUrls: ['./tela-gerenciar-recebimentos.component.scss']
})
export class TelaGerenciarRecebimentosComponent implements OnInit {
  filtro: any = { };
  faturas: any = [];
  assinaturas: any = [];
  assinaturasFiltradas: any = []
  resumoAssinaturas: any = { total: 0, ativas: 0, aguardandoAtivacao: 0,  suspensas: 0, boleto: 0, cartao: 0, pix: 0};
  resumoFinanceiro: any = { ativas: 0, totalMensal: 0, mediaAssinaturas: 0 , totalAtivas: 0}
  carregando = false;
  buscaTexto: any;
  usuario: any;
  constructor(private recebimentoService: RecebimentoService ,
              private autorizacaoService: AutorizacaoService) {
      this.usuario =  this.autorizacaoService.getUser();
  }

  ngOnInit(): void {
    this.filtro.inicio = moment().startOf('month').toDate();
    this.filtro.fim = moment().endOf('month').toDate();
    this.carregueAssinaturas();
  }

  carregueAssinaturas(){
    this.carregando = true;
    this.recebimentoService.obtenhaAssinaturas(0 , 25, {}).then( (assinaturas) => {
      this.carregando = false;
      this.assinaturas = assinaturas;
      this.filtreAssinaturasTela();

    })
  }

  carregueFaturas() {

    let filtro  = {
      div: moment(    this.filtro.inicio).format('YYYYMMDD'),
      dfv: moment(   this.filtro.fim ).format('YYYYMMDD')
    }

    this.recebimentoService.obtenhaFaturas(0 , 25, filtro).then( (faturas) => {
        this.faturas = faturas;
    })
  }

  mudouTab($event: any) {
     if(!this.faturas.lenth)
       this.carregueFaturas();
  }

  filtreAtivas() {
     this.filtro.ativas = !  this.filtro.ativas;
     this.filtreAssinaturasTela();
  }

  filtreAguardandoAtivacao(){
    this.filtro.aguardandoAtivacao =  !this.filtro.aguardandoAtivacao;
    this.filtreAssinaturasTela();
  }

  filtreInativas() {
     this.filtro.suspensas = !  this.filtro.suspensas;
     this.filtreAssinaturasTela();
  }

  filtreCartao(){
    this.filtro.cartao = !  this.filtro.cartao;
    this.filtreAssinaturasTela();
  }

  filtreBoleto(){
    this.filtro.boleto = !  this.filtro.boleto;
    this.filtreAssinaturasTela();
  }

  filtrePix(){
    this.filtro.pix = !  this.filtro.pix;
    this.filtreAssinaturasTela();
  }

  onFilter(event: any) {
    if(this.buscaTexto) clearTimeout(this.buscaTexto);

   this.buscaTexto =   setTimeout( () => {
      this.filtreAssinaturasTela();
    }, 500)


  }

  public filtreAssinaturasTela() {
    let estado: State = {
      filter: {
        logic: "or",
        filters: [],
      }
    };

    if(this.filtro.ativosQueUsamNaoPagos){
      estado  = {
        filter: {
          logic: "and",
          filters: [],
        }
      };

      estado.filter.filters.push( {
        field: 'statusTexto',
        operator: 'contains',
        value: 'Ativada'
      })

      estado.filter.filters.push( {
          field: 'totalFaturasVencidas',
          operator: 'gt',
          value: 0
        })

      estado.filter.filters.push( {
          field: 'ultimaAcaoEmpresa.statusUso',
          operator: 'lt',
          value: 3
        })

      estado.filter.filters.push( {
        field: 'ultimaAcaoEmpresa.statusUso',
        operator: 'gt',
        value: 0
      })


    } else if (this.filtro.somenteNovos) {
      estado.filter.filters.push( {
        field: 'nova',
        operator: 'equal',
        value: true
      })
    } else {
      if(this.filtro.ativas){
        estado.filter.filters.push( {
          field: 'statusTexto',
          operator: 'contains',
          value: 'Ativada'
        })
      } else if(this.filtro.suspensas){
        estado.filter.filters.push( {
          field: 'statusTexto',
          operator: 'contains',
          value: 'Suspensa'
        })
      }  else if(this.filtro.aguardandoAtivacao){
        estado.filter.filters.push( {
          field: 'statusTexto',
          operator: 'contains',
          value: 'Aguardando'
        })
      }

      if(this.filtro.nome){
        estado.filter.filters.push( {
          field: 'nomeEmpresa',
          operator: 'contains',
          value: this.filtro.nome
        })
      }

      if(this.filtro.cartao)
        estado.filter.filters.push( {
          field: 'formasDePagamento',
          operator: 'contains',
          value: 'cartao-credito'
        })

      if(this.filtro.boleto)
        estado.filter.filters.push( {
          field: 'formasDePagamento',
          operator: 'contains',
          value: 'boleto'
        })

      if(this.filtro.pix)
        estado.filter.filters.push( {
          field: 'formasDePagamento',
          operator: 'contains',
          value: 'pix'
        })
    }

    this.assinaturasFiltradas = []
    this.assinaturasFiltradas = process(this.assinaturas, estado);

    this.resumoAssinaturas.total = this.assinaturasFiltradas.data.length;
    this.resumoAssinaturas.ativas =
      this.assinaturasFiltradas.data.filter( (assinatura: any) =>  !assinatura.suspensa && !assinatura.aguardandoAtivacao).length;
    this.resumoAssinaturas.aguardandoAtivacao =
      this.assinaturasFiltradas.data.filter( (assinatura: any) =>  assinatura.aguardandoAtivacao).length;
    this.resumoAssinaturas.suspensas = this.assinaturasFiltradas.data.filter( (assinatura: any) =>   assinatura.suspensa).length;
    this.resumoAssinaturas.cartao =
      this.assinaturasFiltradas.data.filter( (assinatura: any) =>  assinatura.formasDePagamento.indexOf('cartao-credito') >= 0 ).length;

    this.resumoAssinaturas.boleto =
      this.assinaturasFiltradas.data.filter( (assinatura: any) =>  assinatura.formasDePagamento.indexOf('boleto') >= 0).length;

    this.resumoAssinaturas.pix =
      this.assinaturasFiltradas.data.filter( (assinatura: any) =>  assinatura.formasDePagamento.indexOf('pix') >= 0 ).length;
  }


  abriuAssinatura(event: any) {
    let assinatura: any = event.dataItem;

    if(!assinatura.faturas){
      assinatura.carregando = true;

      this.recebimentoService.obtenhaFaturasComResumo(assinatura).then( (resposta) => {
        assinatura.carregando = false;
        assinatura.faturas = resposta.faturas;
        assinatura.resumoPago = {
          total: resposta.total,
          qtde: resposta.qtde,
          media: resposta.media
        }
      })
    }


  }
}
