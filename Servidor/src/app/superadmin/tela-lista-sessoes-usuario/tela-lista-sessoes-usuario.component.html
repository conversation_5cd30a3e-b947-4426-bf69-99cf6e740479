<h5><span class="text-muted">Nome do Usuário: </span> {{usuarioSelecionado?.nome}} </h5>
<h5>ID do Usuário: {{usuarioSelecionado?.id}}</h5>
<p class="text-muted">Esta tela exibe todas as sessões ativas do usuário selecionado. Cada sessão representa um acesso único ao sistema, permitindo deslogar essas sessões.</p>

<div class="d-flex justify-content-between align-items-center mt-3 mb-2">
  <button class="btn btn-danger" (click)="deslogarTodasSessoes()" [disabled]="isLoadingDeslogar">Deslogar Todos</button>
  <h5 style="margin-left: 10px; font-size: 1.2em;">Total de Sessões: {{registrosDeLogin.length}}</h5>
</div>

<ng-container *ngIf="carregou && registrosDeLogin.length === 0">
  <div class="card mb-3">
  <div class="text-center mt-5">
    <i class="fas fa-exclamation-circle text-warning font-50" style="font-size: 50px;"></i>
    <p class="mt-3">Não há sessões ativas para este usuário.
  </div>
</div>
</ng-container>
<kendo-listview [data]="registrosDeLogin">
  <ng-template kendoListViewItemTemplate let-dataItem let-index="index">
    <div class="card mb-3">
      <div class="card mb-3 shadow-sm border-0">
        <div class="card-body pb-1">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h4 class="card-title mb-0"><strong>Sessão {{index + 1}}:</strong> {{dataItem.id}}</h4>
              <small><strong>Data de Acesso:</strong> {{dataItem.diaAcesso | date:'dd/MM/yyyy'}} {{dataItem.horarioAcesso}}</small>
            </div>
            <div class="d-flex align-items-center">
              <span class="badge badge-pill" [ngClass]="{'badge-success': dataItem.sessaoAtiva, 'badge-danger': !dataItem.sessaoAtiva}">
                {{dataItem.sessaoAtiva ? 'Ativa' : 'Inativa'}}
              </span>
              <button class="btn btn-outline-danger btn-sm ml-2" *ngIf="dataItem.sessaoAtiva" [disabled]="dataItem.processando"
              (click)="deslogarSessao(dataItem)">Deslogar</button>
            </div>
          </div>
        </div>
        <div class="card-footer bg-white">
          <div class="row">
            <div class="col">
              <p class="mb-1">
                <strong>IP:</strong> {{dataItem.ip}}
              </p>
              <p class="mb-1"><strong>ID Sessão:</strong> {{dataItem.idSessao}}</p>
              <p><strong>URL de Acesso:</strong> {{dataItem.url}}</p>
            </div>
            <div class="col">
              <p class="mb-1"><strong>Dispositivo: </strong>
                <span *ngIf="dataItem.tipoDispositivo === 'mobile'" class="font-18">
                  <i class="fas fa-mobile"></i>
                </span>
                  <span *ngIf="dataItem.tipoDispositivo === 'tablet'" class="font-18">
                  <i class="fas fa-tablet"></i>
                </span>
                  <span *ngIf="dataItem.tipoDispositivo === 'desktop'" class="font-18">
                  <i class="fas fa-desktop"></i>
                </span>
                {{dataItem.tipoDispositivo | titlecase}}
              </p>
              <p class="mb-1"><strong>Navegador:</strong> {{dataItem.nomeNavegador}} {{dataItem.versaoNavegador}}</p>
              <p><strong>Origem:</strong> {{dataItem.origem}}</p>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <p *ngIf="!dataItem.city"><strong>Localização: </strong>
                <button class="btn btn-sm btn-blue" (click)="buscarInformacoesLocalizacao(dataItem)" [disabled]="dataItem.processando">
                  <i class="fas fa-map-marker-alt mr-1"></i>
                  <span *ngIf="!dataItem.processando">Buscar Informações</span>
                  <span *ngIf="dataItem.processando">Carregando...</span>
                </button>
              </p>
              <p *ngIf="dataItem.city && dataItem.city === 'localhost'"><strong>Hostname:</strong> Desenvolvimento</p>
              <div *ngIf="dataItem.city && dataItem.city !== 'localhost'">
                <hr>
                <p><strong>Hostname:</strong> {{dataItem.hostname}}</p>
                <p><strong>Cidade:</strong> {{dataItem.city}}</p>
                <p><strong>Região:</strong> {{dataItem.region}}</p>
                <p><strong>País:</strong> {{dataItem.country}}</p>
                <p><strong>Localização:</strong> {{dataItem.loc}}</p>
                <p><strong>Organização:</strong> {{dataItem.org}}</p>
                <p><strong>CEP:</strong> {{dataItem.postal}}</p>
                <p><strong>Fuso Horário:</strong> {{dataItem.timezone}}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</kendo-listview>


