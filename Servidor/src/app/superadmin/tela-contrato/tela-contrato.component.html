
<div *ngIf="contrato.id">

  <div class="row justify-content-md-center" *ngIf="novoItem">
    <div class="col-12   col-sm-4">
      <kendo-window class="modal-success" title="Novo item da assinatura" [top]="0" [left]="0" (close)="canceleNovoItem()" [minWidth]="250" [width]="450">
        <form  class="k-form" [ngClass]="{'needs-validation': !frmni.submitted, 'was-validated': frmni.submitted}"
              novalidate #frmni="ngForm" (ngSubmit)="salvarNovoItem(frmni)">
          <div class="row">
            <div class="col-12">
              <div class="form-group mb-12">
                <label  >Descrição:</label><br>

                <kendo-textbox #descricao=ngModel  [(ngModel)]="novoItem.descricao" name="descricao" class="form-control" required>

                </kendo-textbox>

                <div class="invalid-feedback">
                  <p *ngIf="descricao.errors?.required" >Informe uma descrição</p>
                </div>

              </div>
            </div>

            <div class="col-3">
              <div class="form-group mb-12">
                <label  >Qtde:</label><br>
                <kendo-numerictextbox #qtdeItem=ngModel   [min]="1" class="" name="qtdeItem"   [format]="'n1'"  class="form-control"
                                      [(ngModel)]="novoItem.qtde" required>
                </kendo-numerictextbox>
                <div class="invalid-feedback">
                  <p *ngIf="qtdeItem.errors?.required">Obrigatório</p>
                </div>
              </div>
            </div>

            <div class="col-9">
              <div class="form-group mb-12">
                <label  >Valor:</label><br>
                <kendo-numerictextbox    #valorItem=ngModel name="valorItem"   [format]="'n2'"  class="form-control"
                                        [(ngModel)]="novoItem.valor" required>
                </kendo-numerictextbox>
                <div class="invalid-feedback">
                  <p  *ngIf="valorItem.errors?.required">Informe um valor</p>
                </div>
              </div>
            </div>

            <div class="col-12">
              <div class="form-group">
                <input id="recorrente" name="recorrente" type="checkbox"
                       [(ngModel)]="novoItem.recorrente"      class="k-checkbox" kendoCheckBox/>
                <label for="recorrente" class=" ml-1 d-inline-block mt-1"  >
                  Recorrente
                </label>
              </div>
            </div>

            <div class="col-12" *ngIf="novoItem.fatura">
              <div class="form-group">
                <input id="gerar2via" name="gerar2via" type="checkbox"
                       [(ngModel)]="novoItem.gerar2via"      class="k-checkbox" kendoCheckBox/>
                <label for="gerar2via" class=" ml-1 d-inline-block mt-1"  >
                  Alterar fatura pendente
                </label>
              </div>
            </div>


          </div>

          <p class="text-danger" *ngIf="erroOperacao">
            <b>{{erroOperacao}}</b>
          </p>

          <div class="linha mt-2 mb-2"></div>

          <div class="row">
            <div class="col-4">
              <label>Total</label> <br>
              <span class="font-18"> <b>{{obtenhaTotalItens(novoItem.valor) | currency: "BRL"}}</b></span>
            </div>
            <div class="col-8">
              <div class="mt-1">
                <button type="submit" class="btn btn-primary"    [disabled]="salvando"  >
                  <i class="k-icon k-i-loading ml-1" *ngIf="salvando"></i>
                  Confirmar</button>

                <button type="button" class="btn btn-secondary ml-2" (click)="canceleNovoItem()"  [disabled]="salvando">Cancelar</button>
             </div>

            </div>

          </div>


        </form>
      </kendo-window>
    </div>
  </div>

  <kendo-dialog title="Confirmar a remoção" *ngIf="itemRemover" (close)="canceleRemocaoItem()" [minWidth]="250" [width]="450">
    <p style="margin: 30px; text-align: center;">Você tem certeza que deseja item  da assinatura
      "<strong>{{itemRemover.descricao}} - {{itemRemover.valor | currency: "BRL"}}</strong>"?

     <span *ngIf="itemRemover.faturaPendente" class="text-dark">
         Obs: <b>Item será removido também da fatura </b>
     </span>
    </p>
    <kendo-dialog-actions>
      <button kendoButton (click)="confirmeRemocaoItem()" [primary]="true">
        <i class="k-icon k-i-loading"     *ngIf="salvando"></i>
        Confirmar</button>
      <button kendoButton (click)="canceleRemocaoItem()">Não</button>
    </kendo-dialog-actions>
  </kendo-dialog>



  <div class="card-box ">
    <div class="row" >
      <div class="col-12">
        <label> Plano: </label> <br>
        <ng-container *ngIf="!contrato.alterarPlano">
          <span class="nomeplano ml-2 text-success"><b>{{contrato.plano.nome}}</b>
            <i class="fa fa-edit fa-lg pointer-event ml-2"  (click)="contrato.alterarPlano = true;"> </i>
          </span>

          <span class="nomeplano ml-2 text-black-50"> |
             <span class="riscado mr-1 ml-2 text-muted" *ngIf="contrato.valorNegociado">{{contrato.plano.valor | currency: 'BRL'}}</span>
            {{contrato.valorMensalidade   | currency: 'BRL'}} por mês</span>
          <span class="nomeplano ml-2 text-blue">| {{contrato.limiteContatos  | number}} contatos</span>


        </ng-container>

        <ng-container *ngIf="contrato.alterarPlano">
          <form class="k-form"   novalidate #frmnp="ngForm"    (ngSubmit)="alterarPlano()"  >
            <div class="row"  >
              <div class="col-8" >
                <div class="form-group mt-2">
                  <kendo-dropdownlist id="novoPlano" name="novoPlano" [(ngModel)]="contrato.novoPlano" [data]="planos"
                                      placeholder="Selecione um novo plano" class="form-control ml-2" textField="nome"
                                      required    >

                    <ng-template kendoDropDownListItemTemplate let-dataItem>
                      <span class="template">{{ dataItem.nome }}</span> -  {{ dataItem.valor | currency: "BRL" }}
                    </ng-template>
                  </kendo-dropdownlist>
                </div>

              </div>
              <div class="col-4">
                <div class="btn-group  mt-2"  >
                  <button type="submit" class="btn btn-success waves-effect"  [disabled]="salvando || !frmnp.dirty">
                    <i class="fa fa-save" *ngIf="!salvando"></i>
                    <i class="k-icon k-i-loading"     *ngIf="salvando"></i>
                  </button>
                  <button type="button" class="btn btn-light waves-effect"   (click)="contrato.alterarPlano = false;">
                    <i class="fa fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </form>

        </ng-container>
      </div>
    </div>

    <div class="row mt-3">

      <div class="col-3">
        <label> Valor negociado: </label>

        <div *ngIf="!contrato.alterarValorNegociado" class="ml-2">
          <span class="ml-2 text-success"> <b>{{ (contrato.valorNegociado) | currency: 'BRL'}}</b></span>
          <span *ngIf="!contrato.valorNegociado">-</span>
          <i class="fa fa-edit fa-lg pointer-event  ml-2 "   (click)="editarValorNegociado()"
             [hidden]="assinatura.jaIniciouCiclo"></i>
        </div>

        <div *ngIf="contrato.alterarValorNegociado">
          <form class="k-form"  novalidate #frmfp="ngForm"    (ngSubmit)="alterarValorNegociado()">
            <div class="row">
              <div class="col-8">
                <div class="form-group">

                  <kendo-numerictextbox   [min]="0" class="" name="novoValor"   [format]="'n2'"  class="form-control"
                                          [(ngModel)]="contrato.novoValor">
                  </kendo-numerictextbox>
                </div>

              </div>
              <div class="col-4">
                <div class="btn-group"  >
                  <button type="submit" class="btn btn-success waves-effect"  [disabled]="salvando || !frmfp.dirty">
                    <i class="fa fa-save" *ngIf="!salvando"></i>
                    <i class="k-icon k-i-loading"     *ngIf="salvando"></i>
                  </button>
                  <button type="button" class="btn btn-light waves-effect"   (click)="contrato.alterarValorNegociado = false;">
                    <i class="fa fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

          </form>
        </div>

      </div>

      <div class="col-3">
        <label> Taxa de adesão </label>
        <div *ngIf="!contrato.alterarTaxaAdesao" class="ml-2">
          <span class="ml-2  "> <b>{{ (contrato.taxaAdesao) | currency: 'BRL'}}</b></span>
          <span *ngIf="!contrato.taxaAdesao">-</span>
          <i class="fa fa-edit fa-lg pointer-event  ml-2 "   (click)="editarTaxaAdesao()" [hidden]="assinatura.jaIniciouCiclo" ></i>
        </div>

        <div *ngIf="contrato.alterarTaxaAdesao">
          <form class="k-form"  novalidate #frmfp="ngForm"    (ngSubmit)="alterarTaxaAdesao()">
            <div class="row">
              <div class="col-8">
                <div class="form-group">

                  <kendo-numerictextbox   [min]="0" class="" name="novoValor"   [format]="'n2'"  class="form-control"
                                          [(ngModel)]="contrato.novaTaxaAdesao">
                  </kendo-numerictextbox>
                </div>

              </div>
              <div class="col-4">
                <div class="btn-group"  >
                  <button type="submit" class="btn btn-success waves-effect"  [disabled]="salvando || !frmfp.dirty">
                    <i class="fa fa-save" *ngIf="!salvando"></i>
                    <i class="k-icon k-i-loading"     *ngIf="salvando"></i>
                  </button>
                  <button type="button" class="btn btn-light waves-effect"   (click)="contrato.alterarTaxaAdesao = false;">
                    <i class="fa fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

          </form>
        </div>
      </div>

      <div class="col-3" *ngIf="contrato.pagaNoCartao">
        <label> Número de parcelas cartão </label>
        <div *ngIf="!contrato.alterarNumeroParcelas" class="ml-2">
          <span class="ml-2"> <b>{{ (contrato.numeroParcelas) | number}}</b></span>
          <span *ngIf="!contrato.numeroParcelas">-</span>
          <i class="fa fa-edit fa-lg pointer-event  ml-2 "   (click)="editarNumeroParcelas()"  ></i>
        </div>

        <div *ngIf="contrato.alterarNumeroParcelas">
          <form class="k-form"   novalidate #frmnp="ngForm"    (ngSubmit)="alterarNumeroParcelas()">
            <div class="row">
              <div class="col-8">
                <div class="form-group">
                  <kendo-numerictextbox  [min]="1" [max]="contrato.plano.intervalo" class="" name="novoNumeroParcelas"   [format]="'n0'"  class="form-control"
                                         [(ngModel)]="contrato.novoNumeroParcelas">
                  </kendo-numerictextbox>

                </div>
              </div>
              <div class="col-4">
                <div class="btn-group"  >
                  <button type="submit" class="btn btn-success waves-effect"  [disabled]="salvando || !frmnp.dirty">
                    <i class="fa fa-save" *ngIf="!salvando"></i>
                    <i class="k-icon k-i-loading"     *ngIf="salvando"></i>
                  </button>
                  <button type="button" class="btn btn-light waves-effect"   (click)="contrato.alterarNumeroParcelas = false;">
                    <i class="fa fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>


      </div>

      <div class="col-3">
        <label> Limite contatos negóciado: </label>
        <div *ngIf="!contrato.alterarLimiteContatos" class="ml-2">
          <span class="ml-2"> <b>{{ (contrato.limiteContatosNegociado) | number}}</b></span>
          <span *ngIf="!contrato.limiteContatosNegociado">-</span>
          <i class="fa fa-edit fa-lg pointer-event  ml-2 "   (click)="editarLimiteContatos()"  ></i>
        </div>

        <div *ngIf="contrato.alterarLimiteContatos">
          <form class="k-form"   novalidate #frmfp="ngForm"    (ngSubmit)="alterarLimiteContatos()">
            <div class="row">
              <div class="col-8">
                <div class="form-group">
                  <kendo-numerictextbox  [min]="0" class="" name="novoLimiteContatos"   [format]="'n2'"  class="form-control"
                                         [(ngModel)]="contrato.novoLimiteContatos">
                  </kendo-numerictextbox>

                </div>
              </div>
              <div class="col-4">
                <div class="btn-group"  >
                  <button type="submit" class="btn btn-success waves-effect"  [disabled]="salvando || !frmfp.dirty">
                    <i class="fa fa-save" *ngIf="!salvando"></i>
                    <i class="k-icon k-i-loading"     *ngIf="salvando"></i>
                  </button>
                  <button type="button" class="btn btn-light waves-effect"   (click)="contrato.alterarLimiteContatos = false;">
                    <i class="fa fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>


      </div>
    </div>

    <div class="row mt-3" >
      <div class="col-3">
        <label>Próximo Vencimento: </label> <br>
        <span  class="ml-2 text-primary"><b>{{contrato.dataProximoVencimento  | date: "dd/MMMM/yyyy"}}</b></span>

        <span *ngIf="!contrato.dataProximoVencimento">-</span>
        <i class="fa fa-edit fa-lg pointer-event  ml-2" (click)="alterarDataVencimento()"  *ngIf="contrato.dataAtivacao && !assinatura?.suspensa"  ></i>

      </div>

      <div class="col-3">
        <label>Data Bloqueio: </label> <br>
        <span  >
           <span  class="ml-2 text-danger" *ngIf="empresa.dataBloqueioAuto "><b>{{empresa.dataBloqueioAuto | date: 'dd/MMMM/yyyy'}}</b></span>

           <span class="ml-2" *ngIf="!empresa.dataBloqueioAuto">-</span>

           <i class="fa fa-edit fa-lg pointer-event  ml-2"  (click)="definirBloqueio(empresa)"
              *ngIf="contrato.dataAtivacao && !assinatura?.suspensa"  ></i>

        </span>

      </div>

      <div class="col-3">
        <label>Data Ativação: </label> <br>
        <span *ngIf="!contrato.alterarDataAtivacao">
           <span  class="ml-2"><b>{{contrato.dataAtivacao}}</b></span>
            <i class="fa fa-edit fa-lg pointer-event ml-2"  (click)="contrato.alterarDataAtivacao = true;"
               *ngIf="false"> </i>
        </span>

        <div class="" *ngIf="contrato.alterarDataAtivacao">

          <form class="k-form"   novalidate #frm="ngForm"    (ngSubmit)="alterarDataAtivacao()"  >
            <div class="row"  >
              <div class="col-8" >

                <kendo-datepicker [(ngModel)]="contrato.novaDataAtivacao"     class="form-control"   name="dataAtivacao">
                </kendo-datepicker>

              </div>
              <div class="col-4">
                <div class="btn-group"  >
                  <button type="submit" class="btn btn-success waves-effect"  [disabled]="salvando || !frm.dirty"> <i class="fa fa-save"></i></button>
                  <button type="button" class="btn btn-light waves-effect"   (click)="contrato.alterarDataAtivacao = false;">
                    <i class="fa fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>

      </div>
      <div class="col-3" >
        <label> Data fim trial: </label><br>

        <span *ngIf="!contrato.alterarDataTrial">
           <span  class="ml-2"><b>{{contrato.dataFimTrial }}</b></span>

            <i class="fa fa-edit fa-lg pointer-event ml-2" *ngIf="!contrato.dataAtivacao"  (click)="contrato.alterarDataTrial = true;">  </i>
        </span>

        <div class="" *ngIf="contrato.alterarDataTrial">

          <form class="k-form"   novalidate #frm="ngForm"    (ngSubmit)="alterarDataTrial()"  >
            <div class="row"  >
              <div class="col-8" >

                <kendo-datepicker [(ngModel)]="contrato.novaDataFimTrial"     class="form-control"   name="dataFimTrial">
                </kendo-datepicker>

              </div>
              <div class="col-4">
                <div class="btn-group"  >
                  <button type="submit" class="btn btn-success waves-effect"  [disabled]="salvando || !frm.dirty"> <i class="fa fa-save"></i></button>
                  <button type="button" class="btn btn-light waves-effect"   (click)="contrato.alterarDataTrial = false;">
                    <i class="fa fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>

      </div>

    </div>


  </div>

  <div class="row mt-4">
    <div class="col-12">
      <label class="label-titulo"> Faturamento Aumático


        <ng-container *ngIf="assinatura?.suspensa">
          <span class="badge badge-danger" >Assinatura suspensa</span>

          <span class="text-primary d-inline-block ml-1" *ngIf="temFaturaPendente() ">Aguardando pagamento da <b>fatura</b>  para ativação</span>
        </ng-container>

        <span *ngIf="assinatura.codigo" class="ml-1">
          <button class="btn btn-danger btn-sm" *ngIf="!assinatura.suspensa" (click)="confirmeSupensaoAssinatura()">
                  Suspender
                  <i class="k-icon k-i-loading"     *ngIf="assinatura.suspendendo"></i>
          </button>

          <button class="btn btn-success btn-sm" *ngIf="assinatura.suspensa && !temFaturaPendente() "
                  (click)="reativarAssinatura()">
            Ativar
            <i class="k-icon k-i-loading"     *ngIf="assinatura.reativando"></i>
          </button>


        </span>


        <a href="https://{{empresa.dominio}}.promokit.com.br/admin/assinatura/pagamento/{{assinatura.codigo}}"
           *ngIf="assinatura.codigo && !contrato?.dataAtivacao"
           class="btn btn-outline-blue ml-2"  target="_blank" >
          Acessar link pagamento assinatura
        </a>

      </label>


    </div>

  </div>

  <div class="row  mt-2" *ngIf="!assinatura.codigo" >
    <div class="col-6" >
      <label>Gerar nova assinatura </label><br>
      <button class="btn btn-danger" (click)="criarAssinatura()"  *ngIf="!assinatura.nova">Gerar no Iugu</button>

      <form  class="k-form"  novalidate (ngSubmit)="assinarContrato()" #frma="ngForm" *ngIf="assinatura?.nova"
             [ngClass]="{'needs-validation': !frma.submitted, 'was-validated': frma.submitted}" >

        <div class="row">
          <div class="col-12" >

            <div class="form-group">
              <label>Formas de pagamento:</label>

              <kendo-multiselect  name="novaFormaPagamento" [(ngModel)]="assinatura.formaDePagamento" [data]="formasDePagamento"
                                  placeholder="Selecione uma forma de pagamento" class="form-control"
                                  required >
              </kendo-multiselect>
              <div class="invalid-feedback" >
                Informe uma forma de pagamento
              </div>
            </div>

            <label class="text-muted k-text-info" *ngIf="contrato.plano.descontoCartao && !contrato.valorNegociado">
              ** Esse plano tem desconto de <b>{{contrato.plano.descontoCartao | currency: "BRL"}}</b> para assinatura no cartão de crédito
            </label>

          </div>
          <div class="col-12">
            <div class="btn-group mt-2"  >
              <button type="submit" class="btn btn-success waves-effect"  [disabled]="assinatura.gerando" >
                Gerar assinatura Iugu
                <i class="k-i-loading k-icon" *ngIf="assinatura.gerando"></i>
              </button>
              <button type="button" class="btn btn-light waves-effect" (click)="cancelarCriarAssinatura()" [disabled]="assinatura.gerando">
                Voltar
              </button>
            </div>
          </div>
        </div>

      </form>

    </div>

    <div class="col-6  " >

      <div class="row">
        <div class="col-12">
          <label>Código assinatura existente</label>
        </div>
        <div class="col-6">
          <input type="text" class="form-control" [(ngModel)]="codigoAssinaturaExistente" name="codigoAssinaturaExistente">
        </div>
        <div class="col-4">

          <button class="btn btn-blue" type="button" (click)="associarAssinatura()" [disabled]="!codigoAssinaturaExistente || associando">
            Associar existente
            <i class="k-icon k-i-loading" *ngIf="associando"></i>
          </button></div>
      </div>

      <div class="row mt-2">
        <div class="col-12">
          <label>Código assinatura pai</label>
        </div>

        <div class="col-6">
          <input type="text" class="form-control" [(ngModel)]="codigoAssinaturaPai" name="codigoAssinaturaPai">
        </div>
        <div class="col-4">

          <button class="btn btn-blue " type="button" (click)="associarAssinaturaPai()" [disabled]="!codigoAssinaturaPai || associando">
            Associar assinatura Pai
            <i class="k-icon k-i-loading" *ngIf="associando"></i>
          </button></div>
      </div>

    </div>

  </div>

  <div class="row  mt-2" *ngIf="assinatura.codigo" >

    <div class="col-4">
      <label> Iugu:</label><br>
      <a href="https://alia.iugu.com/receive/billing/{{assinatura.codigo}}" target="_blank" class="ml-2">
        {{assinatura.codigo}}
      </a>

      <button type="button" class="btn btn-warning btn-sm ml-4" (click)="sincronizarAssinatura()" kendoTooltip title="sincronizar com iugu"
              [disabled]="assinatura.sincronizando"  [hidden]="assinatura.vinculada" >
        <i class="fa fa-sync" *ngIf="!assinatura.sincronizando"></i>
        <i class="k-icon k-i-loading"     *ngIf="assinatura.sincronizando"></i>
      </button>

    </div>

    <div class="col-4"  >
      <label> Formas de Pagamento:</label>
      <div *ngIf="!assinatura.alterarFormaPagamento" class="ml-2">
        <b>{{assinatura.formasDePagamento || '-'}}</b>
        <i class="fa fa-edit fa-lg pointer-event  ml-2 " *ngIf="assinatura.codigo"
           (click)="editarFormaDePagamento()"  [hidden]="assinatura.vinculada" ></i>

        <button class="btn btn-danger btn-xs ml-1" (click)="removerCartao()" *ngIf="assinatura.cartao"
          [disabled]="assinatura.removendoCartao">
         <i class="k-icon k-i-loading mr-1" *ngIf="assinatura.removendoCartao"></i>  remover cartão</button>

        <a href="https://{{empresa.dominio}}.promokit.com.br/admin/assinatura/trocarcartao/{{assinatura.codigo}}"
           class="btn btn-warning btn-xs ml-1"  *ngIf="contrato.pagaNoCartao"
           target="_blank"  >
            Trocar  cartão
        </a>

      </div>

      <div *ngIf="assinatura.alterarFormaPagamento">
        <form class="k-form"   novalidate #frmfp="ngForm"    (ngSubmit)="alterarFormaPagamento()">
          <div class="row">
            <div class="col-8">
              <div class="form-group">

                <kendo-multiselect  name="novaFormaPagamento" [(ngModel)]="assinatura.novaFormaPagamento" [data]="formasDePagamento"
                                    placeholder="Selecione uma forma de pagamento" class="form-control"
                                    required >
                </kendo-multiselect>

                <div class="invalid-feedback" >
                  Informe uma forma de pagamento
                </div>
              </div>

            </div>
            <div class="col-4">
              <div class="btn-group"  >
                <button type="submit" class="btn btn-success waves-effect"  [disabled]="salvando || !frmfp.dirty">
                  <i class="fa fa-save" *ngIf="!salvando"></i>
                  <i class="k-icon k-i-loading"     *ngIf="salvando"></i>
                </button>
                <button type="button" class="btn btn-light waves-effect"   (click)="assinatura.alterarFormaPagamento = false;">
                  <i class="fa fa-times"></i>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div class="col-2">
      <label>Vencimento:</label> <br>
      <span  class="ml-2" >
          <b>{{assinatura.dataVencimento}}</b>
      </span>
    </div>

    <div class="col-12 mt-2">
      <label class="d-block"><b>Itens Da Assinatura:</b></label>

      <table class="table table-striped">
        <thead>
        <tr>
          <th>Recorrente</th>
          <th>Descrição</th>
          <th>Valor</th>
          <th>Qtde</th>
          <th>Total</th>
          <th></th>

        </tr>
        </thead>
        <tbody>
        <tr>
          <td>Sim</td>
          <td>Mensalidade plano {{contrato.plano.nome}}</td>
          <td>{{this.getValorPagoPlano() | currency: "BRL"}}</td>
          <td>1</td>
          <td>{{this.getValorPagoPlano() | currency: "BRL"}}</td>
          <td></td>

        </tr>
        <tr *ngFor="let item of assinatura.itens">
          <td>
            <span *ngIf="item.recorrente" class="font-11" >Sim</span>
            <span *ngIf="!item.recorrente" class="font-11" >Não</span>
          </td>
          <td> {{item.descricao}}</td>
          <td> {{item.valor | currency: "BRL"}}</td>
          <td> {{item.quantidade}}</td>
          <td> {{item.total | currency: "BRL"}}</td>
          <td>
           <button class="btn btn-danger btn-xs" (click)="confirmeRemoverItemAssinatura(item)">
             <i class="k-icon k-i-trash r" ></i>
           </button>
          </td>
        </tr>
        </tbody>
        <tfoot>
           <tr>
             <td colspan="3"><label class="float-right">Total</label></td>
             <td colspan="2"><b>{{obtenhaTotalItens() | currency: "BRL"}}</b></td>
           </tr>
        </tfoot>
      </table>

      <button class="btn btn-sm btn-blue" style="position: relative; top: -45px" (click)="abraModalNovoItemFatura();" *ngIf="!assinatura.vinculada">
        <i class="k-icon k-i-plus mr-1"></i>Adicionar novo item</button>


    </div>

    <div class="col-12 mt-2" [hidden]="assinatura.vinculada" >
      <app-grid-faturas [faturas]="contrato.faturas"  [assinatura]="contrato.assinatura"></app-grid-faturas>
    </div>

    <div class="col-12 mt-2" [hidden]="!assinatura.vinculada" >
       <div class="alert  alert-info">
         Esta assinatura está vinculado a outro contrato.
       </div>
    </div>
  </div>

</div>

<div *ngIf="!contrato.id && !carregando">
  <label class="text-muted">Nenhum contrato cadastrado! crie um agora</label>
  <br>
  <form class="mt-1" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
        novalidate #frm="ngForm" (ngSubmit)="criarContrato()" style="max-width: 450px">
    <div class="form-group mb-12">
      <label for="plano">Plano:</label>

      <kendo-dropdownlist id="plano" name="plano" [(ngModel)]="contrato.plano" [data]="planos"
                          placeholder="Selecione um plano" class="form-control" textField="nome"
                          required>

        <ng-template kendoDropDownListItemTemplate let-dataItem>
          <span class="template">{{ dataItem.nome }}</span> -  {{ dataItem.valor | currency: "BRL" }}
        </ng-template>
      </kendo-dropdownlist>

      <div class="invalid-feedback" >
        Plano é obrigatório
      </div>

      <label class="text-muted k-text-info mt-1" *ngIf="contrato.plano.id">
        * Valor do plano: <b>{{(contrato.plano.valor) | currency:"BRL"}}</b>
      </label>

      <label class="text-muted k-text-info mt-1" *ngIf="contrato.plano.descontoCartao && !contrato.valorNegociado">
        **<b>{{contrato.plano.descontoCartao | currency: "BRL"}} desconto</b>   no cartão de crédito
      </label>

    </div>

    <div class="form-group mb-12">
      <label for="plano">Dia Vencimento: </label>

      <kendo-dropdownlist id="diaVencimento" name="diaVencimento" [(ngModel)]="contrato.diaVencimento" [data]="diasVencimento"
                          placeholder="Selecione um dia" class="form-control"
                          required>
      </kendo-dropdownlist>

      <div class="invalid-feedback" >
        Dia de vencimento é obrigatório
      </div>
    </div>


    <label class="text-muted">Esses valores foram negociados?</label>

    <div class="row">
        <div class="form-group mb-3 col"  >
          <label for="valorNegociado">Valor Negociado</label>
          <kendo-numerictextbox id="valorNegociado" [min]="0" class="" name="valorNegociado"   [format]="'n2'"  class="form-control"
                                [(ngModel)]="contrato.valorNegociado">
          </kendo-numerictextbox>
        </div>

        <div class="form-group mb-12 col">
          <label for="limiteContatosNegociado">Contatos negociados</label>
          <kendo-numerictextbox id="limiteContatosNegociado" [min]="0" class="" name="limiteContatosNegociado"   [format]="'n2'"  class="form-control"
                                [(ngModel)]="contrato.limiteContatosNegociado">
          </kendo-numerictextbox>

        </div>
      </div>




    <div class="alert alert-danger alert-dismissible fade show mt-2" *ngIf="mensagemErro" role="alert">
      <i class="mdi mdi-check-all mr-2"></i> {{mensagemErro}}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="mt-2">
      <button type="submit" class="btn btn-primary waves-effect waves-light" [disabled]="salvando"  >Salvar</button>
      <button type="button" class="btn btn-light waves-effect" data-dismiss="modal" (click)="canceleNovo()">Cancelar</button>
    </div>
  </form>
</div>



<kendo-dialog title="Data próximo vencimento" *ngIf="contrato.alterarDataVencimento" (close)="contrato.alterarDataVencimento = false" [minWidth]="250" [width]="450">


  <div class="form-group mb-12 mt-2">
    <label  >Data vencimento</label>
    <kendo-datepicker [(ngModel)]="contrato.dataNovoVencimento"  (valueChange)="calculeNovoBloqueio()"
                      [min]="hoje"
                      class="form-control mb-2"   name="dataNovoVencimento">
    </kendo-datepicker>

    <small class=" mt-2">Proximo bloqueio sera programado para
    <b>  {{contrato.dataNovoBloqueio | date: 'dd/MMMM/yyyy'}}</b></small>


    <p class="mt-2">
      Vencimento da assinatura no Iugu é em     <b>  {{assinatura.dataVencimento  }}</b>,
      essa data também sera altera.

    </p>

  </div>

  <div class="text-danger mt-2 mb-2" *ngIf="erroOperacao">{{erroOperacao}} </div>

  <kendo-dialog-actions>
    <div class="mt-2 mb-2 mr-2">
      <button  class="btn btn-primary"  (click)="altereDataVencimento()" kendoButton [primary]="true" [disabled]="salvando">
        <i class="k-icon k-i-loading mr-1" *ngIf="salvando"></i>Salvar </button>
      <button class="btn btn-light"   (click)="contrato.alterarDataVencimento = false">Cancelar</button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>


<kendo-dialog title="Reativar Assinatura" *ngIf="confirmarAtivacao" (close)="this.confirmarAtivacao = null" [minWidth]="250" [width]="450">
  <h5> Confirme a nova data de vencimento </h5>

  <div class="form-group mb-12 mt-2">
    <label  >Data vencimento</label>

    <kendo-datepicker [(ngModel)]="contrato.dataNovoVencimento"     [min]="hoje" required
                      class="form-control mb-2"   name="dataNovoVencimento">
    </kendo-datepicker>
  </div>


  <p class="text-muted" *ngIf="assinatura.dataUltimoPagamento">
    Ultimo pagamento foi em <b>{{assinatura.dataUltimoPagamento}}</b></p>

  <div class="text-danger mt-2 mb-2" *ngIf="erroOperacao">{{erroOperacao}} </div>

  <kendo-dialog-actions>
    <div class="mt-2 mb-2 mr-2">
      <button  class="btn btn-primary"  (click)="reativeAssinatura()" kendoButton [primary]="true" [disabled]="this.assinatura.reativando">
        <i class="k-icon k-i-loading mr-1" *ngIf="this.assinatura.reativando"></i>Ativar </button>



      <button class="btn btn-light"   (click)="this.confirmarAtivacao = null">Cancelar</button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<kendo-dialog title="Suspender Assinatura" *ngIf="confirmarSupensao" (close)="fecheConfirmacao()" [minWidth]="250" [width]="450">
  <p  >
    Ao suspender assinatura, a fatura atual será cancelada e nenhum outra fatura será gerado. Deseja realmente
    suspender a assinatura?
  </p>
  <kendo-dialog-actions>
    <button kendoButton (click)="fecheConfirmacao()" [disabled]=" assinatura.suspendendo" >Não</button>
    <button kendoButton (click)="suspenderAssinatura()" [primary]="true" [disabled]=" assinatura.suspendendo"> Sim
      <i class="k-icon k-i-loading " *ngIf="assinatura.suspendendo"></i>

    </button>
  </kendo-dialog-actions>
</kendo-dialog>

<kendo-dialog title="Bloqueio da empresa" *ngIf="empresaBloquear" (close)="canceleBloqueio()" [minWidth]="250" [width]="450">
  <h4  >{{empresaBloquear.nome}}</h4>

  <div class="form-group mb-12 mt-2">
    <label  >Data programada do bloqueio</label>
    <kendo-datepicker [(ngModel)]="empresaBloquear.dataBloqueioAuto"
                      class="form-control"   name="dataBloqueioAuto">
    </kendo-datepicker>

    <small class="text-muted mt-1">Para bloqueio imediato configurar para  hoje ou data anterior</small>

  </div>

  <div class="text-danger mt-2 mb-2" *ngIf="erroBloqueio">{{erroBloqueio}} </div>

  <kendo-dialog-actions>
    <div class="mt-2 mb-2 mr-2">
      <button  class="btn btn-primary"  (click)="atualizeBloqueioEmpresa()" kendoButton [primary]="true">Salvar </button>
      <button class="btn btn-light"   (click)="canceleBloqueio()">Cancelar</button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

