import {Component, Input, OnInit} from '@angular/core';
import {FileRestrictions, SelectEvent} from "@progress/kendo-angular-upload";
import {EmpresasService} from "../services/empresas.service";
import {DropDownFilterSettings} from "@progress/kendo-angular-dropdowns";

declare var _;
@Component({
  selector: 'app-tela-importar-contatos',
  templateUrl: './tela-importar-contatos.component.html',
  styleUrls: ['./tela-importar-contatos.component.scss']
})
export class TelaImportarContatosComponent implements OnInit {
  @Input() empresa: any;
  uploadUrl: any = '/upload/contatos';
  contatos: any = [];
  restricoes:  FileRestrictions = {
    allowedExtensions: ['.txt', '.csv']
  };
  files: any = [];
  tiposImportacao = [
    {tipo: 'Contato', colunas: [  { nome: 'nome', descricao: 'Nome'},
                                  { nome: 'telefone', descricao: 'Telefone Celular'},
                                  { nome: 'cpf',   descricao: "CPF"},
                                  { nome: 'email', descricao: "E-mail"},
                                  { nome: 'pontos', descricao: "Pontuação fidelidade"},
                                  { nome: 'dataNascimento', descricao: "Data de nascimento (DD/MM/YYYY)"},
                                  { nome: 'dataNascimento2', descricao: "Data de nascimento (MM/DD/YYYY)"},
                                  { nome: 'dataNascimento3', descricao: "Data de nascimento (YYYY-MM-DD)"},
                                  { nome: 'dataNascimento4', descricao: "Data de nascimento (DD-MM-YYYY)"},
                                  { nome: 'cep', descricao: "CEP"},
                                  { nome: 'logradouro', descricao: "Logradouro"},
                                  { nome: 'numero', descricao: "Número"},
                                  { nome: 'bairro', descricao: "Bairro"},
                                  { nome: 'complemento', descricao: "Complemento"},
                                  { nome: 'pontoreferencia', descricao: "Ponto de referência"},
                                  { nome: 'estado', descricao: "Estado"},
                                  { nome: 'cidade', descricao: "Cidade"}

                                ]
    },
    {tipo: 'Pet', colunas: [{ nome: 'nome', descricao: 'Nome'}, { nome: 'genero', descricao: 'Genero'},
                            { nome: 'telefone', descricao: 'Telefone  Responsável'} ,
                            { nome: 'tipo', descricao: 'Tipo de Pet'} ,
                            { nome: 'dataNascimento', descricao: "Data de nascimento (DD/MM/YYYY)"},
                            { nome: 'dataNascimento2', descricao: "Data de nascimento (MM/DD/YYYY)"},
                            { nome: 'dataNascimento3', descricao: "Data de nascimento (YYYY-MM-DD)"},
                            { nome: 'dataNascimento4', descricao: "Data de nascimento (DD-MM-YYYY)"}]
    }
  ]
  tipoImportar: any = {};
  linhasRemovidas = [];
  colunasImportar = [];
  colunasRemovidas = [];
  linhasExibir = 10;
  importando = false;
  resposta: any = {};
  erro: string;
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains'
  };
  constructor(private empresaService: EmpresasService) {

  }

  ngOnInit() {
  }

  selecionouArquivo(e: SelectEvent){
    e.files.forEach((file) => {
      console.log(`File selected: ${file.name}`);
    });
  }

  successUpload(e: any){
    this.contatos = e.response.body;
    let id = new Date().getTime();

    this.contatos.forEach( (contato) => { contato.splice(0, 0, id++)})

    if(this.contatos.length){
      this.colunasRemovidas = [];
      this.colunasImportar = [];

      let  i = 0;
      this.contatos[0].forEach( (dados) => {
        let coluna: any = { indice: i, coluna: (i === 0 ? '_id' : null)};
        this.colunasImportar.push( coluna )
        i++
      })
    } else {
      this.erro = 'Nenhum contato no arquivo informado foi carregado.'
    }
  }


  removaColuna(coluna: any) {
    coluna.removida = true
    this.colunasRemovidas.push(coluna.indice)
  }

  removaLinha(linha: any, ilinha: number) {
    this.linhasRemovidas.push(ilinha);
    this.contatos.splice(ilinha, 1);
  }

  importarContatos($event: any) {
    delete this.erro;
    let colunasNaoConfigurados = _.filter(this.colunasImportar, item => !item.coluna && !item.removida);
    let colunasImportar = _.filter(this.colunasImportar, item => item.coluna && !item.removida);

    if(colunasNaoConfigurados.length ){
      this.erro = 'Configure as colunas que serão importadas'
      return;
    }

    if(colunasImportar.length === 0){
      this.erro = 'Nenhuma coluna de importação foi configurada.'
      return;
    }

    this.importando = true;
    let contatos = [];

    this.contatos.forEach( (dadosContato) => {
      delete dadosContato.erro;
      let contato = {_id: dadosContato[0]}

      colunasImportar.forEach( (item) => {
        if(!contato[item.coluna.nome] || this.ehCampoTelefoneMelhor(item, contato, dadosContato))
         contato[item.coluna.nome] = dadosContato[item.indice]
      })

      contatos.push(contato)
    })

    if(this.tipoImportar.tipo === 'Contato'){
      this.empresaService.importeContatos(this.empresa, contatos).then( (resposta) => {
        this.setRespostaImportacao(resposta);
      }).catch( erro => {
        this.erro = erro
        this.importando = false;
      })
    } else {
      this.empresaService.importePet(this.empresa, contatos).then( (resposta) => {
        this.setRespostaImportacao(resposta);
      }).catch( erro => {
        this.erro = erro
        this.importando = false;
      })
    }


  }

  setRespostaImportacao(resposta){
    this.importando = false;
    this.resposta = resposta;
    this.resposta.importado = true;

    this.resposta.erros.forEach( (infoErro) => {
      let contato = _.find(this.contatos, dados => dados[0] === infoErro._id)

      if(contato) contato.erro = infoErro.erro;
    })
  }

  ehCampoTelefoneMelhor(item, contato, dados){
     if(item.coluna.nome !== 'telefone' || !contato[item.coluna.nome])
       return false;

     let iniciosDdd = {
       '62': [ 7 , 8 , 9 ]
     }

     let telefoneAtual = contato[item.coluna.nome].toString().replace(/\D/g, '');
     let telefoneNovo = dados[item.indice].toString().replace(/\D/g, '');

     if(telefoneNovo.length <= 8) return false;

     if(telefoneAtual.length <= 8) return true;

     let ddd = telefoneAtual.substr(0, 2)

     let primeiroDigito = this.getPrimeiroDigito(telefoneAtual);

     if(ddd === '62' && primeiroDigito === '9' && this.getSegundoDigito(telefoneAtual) === '3' )
         return true;

     if ( primeiroDigito === '3' || primeiroDigito === '0'  )
        return true;

     if(iniciosDdd[ddd] && iniciosDdd[ddd].indexOf(Number(primeiroDigito)) === -1 )
       return true;

     return telefoneAtual.length <  telefoneNovo.length
  }

  getPrimeiroDigito(telefone){
    let indicePrimero = telefone.length <= 9 ? 0 : 2

    return telefone[ indicePrimero ];
  }

  getSegundoDigito(telefone){
    let indicePrimero = telefone.length <= 9 ? 0 : 2

    return telefone[ indicePrimero + 1 ];
  }


  importarNovo() {
    this.contatos = [];
    this.colunasImportar = [];
    this.colunasRemovidas = [];
    this.resposta = {};
    this.tipoImportar = {}
  }

  corrigirErro() {
    this.contatos = _.filter(this.contatos, (contato: any) => contato.erro)
    this.resposta = {};
  }

  selecioneTipoImportacao(tipoDeImportacao: any) {
    this.tipoImportar  = tipoDeImportacao;
  }
}
