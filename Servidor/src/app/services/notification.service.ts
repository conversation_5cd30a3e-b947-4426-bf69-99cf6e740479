import { Injectable } from '@angular/core';
import { NotificationService as KendoNotificationService } from '@progress/kendo-angular-notification';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  constructor(private notificationService: KendoNotificationService) {}

  showSuccess(message: string) {
    this.notificationService.show({
      content: message,
      cssClass: 'success',
      animation: { type: 'slide', duration: 400 },
      position: { horizontal: 'right', vertical: 'top' },
      type: { style: 'success', icon: true },
      hideAfter: 3000
    });
  }

  showError(message: string) {
    this.notificationService.show({
      content: message,
      cssClass: 'error',
      animation: { type: 'slide', duration: 400 },
      position: { horizontal: 'right', vertical: 'top' },
      type: { style: 'error', icon: true },
      hideAfter: 5000
    });
  }
}
