import { TestBed } from '@angular/core/testing';

import { BotsService } from './bots.service';
import {HttpClientTestingModule} from "@angular/common/http/testing";

describe('BotsService', () => {
  let service: BotsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
    });
    service = TestBed.inject(BotsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('deve desativar a sessao', () => {
    const empresa = {
      cardapio: {
        botAtivo: true
      }
    };

    let sessaoAberta = service.possuiSessaoAberta(empresa, '622234343');
    expect(sessaoAberta).toBeTruthy();

    const contato = {
      nome: 'Eu',
      telefone: '622234343'
    };

    service.desativeComunicacao(empresa, contato);
    sessaoAberta = service.possuiSessaoAberta(empresa, '622234343');
    expect(sessaoAberta).toBeFalsy();

    service.ativarComunicacao(contato.telefone);
    sessaoAberta = service.possuiSessaoAberta(empresa, '622234343');
    expect(sessaoAberta).toBeTruthy('Sessão deve ser ativada');

    service.desativeComunicacao(empresa, contato);
    sessaoAberta = service.possuiSessaoAberta(empresa, '622234343');
    expect(sessaoAberta).toBeFalsy();
  })
});
