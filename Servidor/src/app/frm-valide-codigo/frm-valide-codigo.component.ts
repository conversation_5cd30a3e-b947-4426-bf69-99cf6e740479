import {AfterViewInit, Component, Input, OnInit, ViewChild} from '@angular/core';
import {NgForm} from "@angular/forms";
import * as textMask from "vanilla-text-mask/dist/vanillaTextMask";
import {ClienteService} from "../services/cliente.service";
import {ClienteComponent} from "../cliente/cliente.component";
import {ArmazenamentoService} from "../services/armazenamento.service";

@Component({
  selector: 'app-frm-valide-codigo',
  templateUrl: './frm-valide-codigo.component.html',
  styleUrls: ['./frm-valide-codigo.component.scss']
})
export class FrmValideCodigoComponent implements OnInit, AfterViewInit {
  @ViewChild('frmValidarCodigo')  frmValidarCodigo: NgForm;
  @ViewChild('txtCodigo') private txtCodigo: any;

  public maskCodigo: Array<string | RegExp>;
  mensagemAguarde: string;
  mensagemFalhaEnvio: string;
  codigo = '';
  private maskedInputController2: any;
  mensagemErroEnvio = '';
  enviando = false;
  confirmando = false;
  contato: any;
  confirmou = true;

  @Input()
  parent: ClienteComponent;

  constructor(private clienteService: ClienteService,
              private armazenamentoService: ArmazenamentoService) {
    this.maskCodigo = [/\d/, /\d/, /\d/, /\d/, /\d/, /\d/];
  }

  ngOnInit() {
  }

  ngAfterViewInit() {
    setTimeout( () => {
      this.maskedInputController2 = textMask.maskInput({
        inputElement: this.txtCodigo.nativeElement,
        mask: this.maskCodigo
      });
    });
  }

  onSubmitValidarCodigo() {
    this.enviando = true;

    if ( !this.frmValidarCodigo.valid ) {
      this.mensagemErroEnvio = "Existem erros no preenchimento. Por favor, verifique e tente novamente!";
      window.scrollTo(0, 0);
      this.enviando = false;
      return;
    }

    this.clienteService.valideCodigo(this.unmask(this.codigo),
      this.parent.unmask(this.parent.dadosContato.telefone)).then((contato: any) => {
      this.enviando = false;
      this.contato = contato;

      if( this.contato.id ) {
        this.armazenamentoService.salveSemExpirar("idCliente", contato.id);
        this.parent.validouCliente();

        this.mensagemAguarde =
          "Você receberá uma mensagem com um código para você acessar seu cartão. Informe o código no campo abaixo:";
      } else {
        this.parent.validouTelefone();
      }
    }).catch((mensagem) => {
      this.enviando = false;
      //this.mensagemErroEnvio = mensagem;
      this.mensagemFalhaEnvio = mensagem;
    });
  }

  unmask(val) {
    return val.replace(/\D+/g, '');
  }
}
