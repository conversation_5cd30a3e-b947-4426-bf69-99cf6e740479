import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SuporteService, EstatisticasSuporte } from '../services/suporte.service';

@Component({
  selector: 'app-suporte-home',
  templateUrl: './suporte-home.component.html',
  styleUrls: ['./suporte-home.component.css']
})
export class SuporteHomeComponent implements OnInit {
  estatisticas: EstatisticasSuporte | null = null;
  loading = true;
  error: string | null = null;

  constructor(
    private suporteService: SuporteService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.carregarEstatisticas();
  }

  carregarEstatisticas(): void {
    this.loading = true;
    this.error = null;

    this.suporteService.getEstatisticas().then(data => {
      this.estatisticas = data;
      this.loading = false;
    }).catch(error => {
      console.error('Erro ao carregar estatísticas:', error);
      this.error = 'Erro ao carregar estatísticas do suporte';
      this.loading = false;
    });
  }

  navegarPara(rota: string): void {
    this.router.navigate(['/suporte', rota]);
  }

  criarNovoTicket(): void {
    this.router.navigate(['/suporte/tickets/novo']);
  }

  verTodosTickets(): void {
    this.router.navigate(['/suporte/tickets']);
  }

  verTicketsAbertos(): void {
    this.router.navigate(['/suporte/tickets'], { queryParams: { status: 'aberto' } });
  }

  verTicketsAndamento(): void {
    this.router.navigate(['/suporte/tickets'], { queryParams: { status: 'andamento' } });
  }

  verTicketsResolvidos(): void {
    this.router.navigate(['/suporte/tickets'], { queryParams: { status: 'resolvido' } });
  }

  atualizarDados(): void {
    this.carregarEstatisticas();
  }

  getPercentualResolucao(): number {
    if (!this.estatisticas || this.estatisticas.totalTickets === 0) {
      return 0;
    }
    return Math.round((this.estatisticas.ticketsResolvidos / this.estatisticas.totalTickets) * 100);
  }
}
