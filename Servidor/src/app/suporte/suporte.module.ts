import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

// Kendo UI Angular Modules
import { GridModule } from '@progress/kendo-angular-grid';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { LayoutModule } from '@progress/kendo-angular-layout';
import { DialogsModule } from '@progress/kendo-angular-dialog';
import { NotificationModule } from '@progress/kendo-angular-notification';
import { TooltipModule } from '@progress/kendo-angular-tooltip';
import { LabelModule } from '@progress/kendo-angular-label';

// Routing
import { SuporteRoutingModule } from './suporte-routing.module';

// Components
import { SuporteHomeComponent } from './suporte-home/suporte-home.component';
import { TicketListComponent } from './ticket-list/ticket-list.component';
import { TicketDetailsComponent } from './ticket-details/ticket-details.component';
import { TicketCrudComponent } from './ticket-crud/ticket-crud.component';
import { NovoTicketComponent } from './novo-ticket/novo-ticket.component';

// Services
import { SuporteService } from './services/suporte.service';
import { TicketService } from './services/ticket.service';

@NgModule({
  declarations: [
    SuporteHomeComponent,
    TicketListComponent,
    TicketDetailsComponent,
    TicketCrudComponent,
    NovoTicketComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    SuporteRoutingModule,
    
    // Kendo UI Angular Modules
    GridModule,
    ButtonsModule,
    InputsModule,
    DropDownsModule,
    LayoutModule,
    DialogsModule,
    NotificationModule,
    TooltipModule,
    LabelModule
  ],
  providers: [
    SuporteService,
    TicketService
  ]
})
export class SuporteModule { }
