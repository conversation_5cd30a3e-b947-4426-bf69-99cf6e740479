.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-title {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.filters-card {
  margin-bottom: 2rem;
}

.filters-container {
  display: flex;
  gap: 1rem;
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

.filter-actions {
  display: flex;
  align-items: end;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-container p {
  margin-top: 1rem;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  margin: 2rem 0;
}

.alert {
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem 2rem;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  font-size: 4rem;
  color: #ccc;
  margin-bottom: 1rem;
}

.empty-content h3 {
  color: #666;
  margin-bottom: 1rem;
}

.empty-content p {
  color: #999;
  margin-bottom: 2rem;
}

.tickets-container {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.ticket-card {
  margin-bottom: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ticket-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.ticket-title {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
  flex: 1;
  margin-right: 1rem;
}

.ticket-id {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  background-color: #f5f5f5;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.ticket-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ticket-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.ticket-meta {
  margin-bottom: 1rem;
}

.meta-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-label {
  color: #999;
  font-size: 0.9rem;
}

.meta-value {
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
}

.ticket-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.ticket-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #eee;
  margin-top: auto;
}

/* Status Colors */
.status-aberto {
  background-color: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

.status-andamento {
  background-color: #e1bee7;
  color: #4a148c;
  border-color: #ce93d8;
}

.status-resolvido {
  background-color: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.status-fechado {
  background-color: #f8f9fa;
  color: #495057;
  border-color: #dee2e6;
}

/* Priority Colors */
.prioridade-baixa {
  background-color: #d1ecf1;
  color: #0c5460;
  border-color: #bee5eb;
}

.prioridade-media {
  background-color: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

.prioridade-alta {
  background-color: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.prioridade-critica {
  background-color: #f5c6cb;
  color: #721c24;
  border-color: #f1b0b7;
}

.results-summary {
  text-align: center;
  padding: 2rem 0;
  color: #666;
  border-top: 1px solid #eee;
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .filter-actions {
    align-items: stretch;
  }
  
  .ticket-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .ticket-title {
    margin-right: 0;
  }
  
  .ticket-actions {
    flex-direction: column;
  }
}
