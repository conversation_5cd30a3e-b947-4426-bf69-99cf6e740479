import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ServerService } from '../../services/ServerService';

export interface EstatisticasSuporte {
  totalTickets: number;
  ticketsAbertos: number;
  ticketsAndamento: number;
  ticketsResolvidos: number;
  ticketsFechados: number;
  tempoMedioResolucao: number;
  satisfacaoMedia: number;
}

@Injectable({
  providedIn: 'root'
})
export class SuporteService extends ServerService {

  constructor(http: HttpClient) {
    super(http);
  }

  getEstatisticas(): Promise<EstatisticasSuporte> {
    // Mock data for development - replace with actual API call
    const mockData: EstatisticasSuporte = {
      totalTickets: 156,
      ticketsAbertos: 23,
      ticketsAndamento: 12,
      ticketsResolvidos: 98,
      ticketsFechados: 23,
      tempoMedioResolucao: 2.5,
      satisfacaoMedia: 4.2
    };

    return Promise.resolve(mockData);

    // Uncomment when backend is ready:
    // return this.obtenha('/suporte/estatisticas', {});
  }

  getConfiguracoes(): Promise<any> {
    return this.obtenha('/suporte/configuracoes', {});
  }

  salvarConfiguracoes(configuracoes: any): Promise<any> {
    return this.salve('/suporte/configuracoes', configuracoes);
  }

  gerarRelatorio(filtros: any): Promise<any> {
    return this.obtenha('/suporte/relatorios', filtros);
  }

  exportarDados(formato: 'excel' | 'pdf', filtros: any): Promise<Blob> {
    const params = {
      formato,
      ...filtros
    };
    return this.obtenha('/suporte/exportar', params);
  }
}
