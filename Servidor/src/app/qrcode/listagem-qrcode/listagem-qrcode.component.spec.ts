import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ListagemQrcodeComponent } from './listagem-qrcode.component';

describe('ListagemQrcodeComponent', () => {
  let component: ListagemQrcodeComponent;
  let fixture: ComponentFixture<ListagemQrcodeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ListagemQrcodeComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ListagemQrcodeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
