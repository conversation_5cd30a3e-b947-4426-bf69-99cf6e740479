.page-title-box {
  margin-bottom: 3rem;
  text-align: center;
  
  .page-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 2.25rem;
    margin-bottom: 0.75rem;
    letter-spacing: -0.025em;
    
    i {
      color: #E4405F;
      margin-right: 0.75rem;
    }
  }
  
  p {
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 400;
    margin-bottom: 0;
  }
}

.card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  background: #ffffff;
  overflow: hidden;
  
  &.border-success {
    border-left: 4px solid #28a745;
    background: #f8fff9;
    box-shadow: 0 4px 20px rgba(40, 167, 69, 0.15);
  }
  
  &.border-warning {
    border-left: 4px solid #ffc107;
    background: #fffbf0;
    box-shadow: 0 4px 20px rgba(255, 193, 7, 0.15);
  }
  
  &.border-info {
    border-left: 4px solid #17a2b8;
    background: #f0fcff;
    box-shadow: 0 4px 20px rgba(23, 162, 184, 0.15);
  }
  
  .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
    
    .card-title {
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 0.5rem;
    }
  }
  
  .card-body {
    padding: 1.5rem;
  }
}

.list-group-item {
  border: none;
  padding: 1.75rem;
  background: transparent;
  
  &:not(:last-child) {
    border-bottom: 1px solid #f1f3f4;
  }
  
  h6 {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    font-weight: 500;
    margin-bottom: 0.25rem;
  }
  
  small {
    font-weight: 500;
    font-size: 0.875rem;
  }
}

.avatar-md {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid #ffffff;
}

.avatar-lg {
  width: 72px;
  height: 72px;
  border-radius: 18px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  border: 3px solid #ffffff;
}

// Avatares para imagens do Instagram
img.rounded-circle {
  border-radius: 16px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid #ffffff;
}

.btn {
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-width: 2px;
  letter-spacing: 0.025em;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 16px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  &.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 10px;
  }
  
  // Botões primários
  &.btn-primary {
    background: #1877f2;
    border-color: #1877f2;
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
    
    i {
      color: #ffffff !important;
    }
    
    &:hover, &:focus, &:active {
      background: #166fe5;
      border-color: #166fe5;
      color: #ffffff !important;
      
      i {
        color: #ffffff !important;
      }
    }
  }
  
  &.btn-success {
    background: #28a745;
    border-color: #28a745;
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    
    i {
      color: #ffffff !important;
    }
    
    &:hover, &:focus, &:active {
      background: #218838;
      border-color: #218838;
      color: #ffffff !important;
      
      i {
        color: #ffffff !important;
      }
    }
  }
  
  &.btn-danger {
    background: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    
    i {
      color: #ffffff !important;
    }
    
    &:hover, &:focus, &:active {
      background: #c82333;
      border-color: #c82333;
      color: #ffffff !important;
      
      i {
        color: #ffffff !important;
      }
    }
  }
  
  // Botões outline modernos
  &.btn-outline-primary {
    border-color: #1877f2;
    color: #1877f2;
    background: rgba(24, 119, 242, 0.05);
    font-weight: 600;
    
    &:hover, &:focus, &:active {
      background: #1877f2;
      border-color: #1877f2;
      color: #ffffff !important;
      
      i {
        color: #ffffff !important;
      }
    }
  }
  
  &.btn-outline-secondary {
    border-color: #6c757d;
    color: #495057;
    background: rgba(108, 117, 125, 0.05);
    font-weight: 600;
    
    &:hover, &:focus, &:active {
      background: #6c757d;
      border-color: #6c757d;
      color: #ffffff !important;
      
      i {
        color: #ffffff !important;
      }
    }
  }
  
  &.btn-outline-info {
    border-color: #17a2b8;
    color: #17a2b8;
    background: rgba(23, 162, 184, 0.05);
    font-weight: 600;
    
    &:hover, &:focus, &:active {
      background: #17a2b8;
      border-color: #17a2b8;
      color: #ffffff !important;
      
      i {
        color: #ffffff !important;
      }
    }
  }
  
  &.btn-outline-warning {
    border-color: #ffc107;
    color: #856404;
    background: rgba(255, 193, 7, 0.05);
    font-weight: 600;
    
    &:hover, &:focus, &:active {
      background: #ffc107;
      border-color: #ffc107;
      color: #212529 !important;
      
      i {
        color: #212529 !important;
      }
    }
  }
  
  &.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    background: rgba(220, 53, 69, 0.05);
    font-weight: 600;
    
    &:hover, &:focus, &:active {
      background: #dc3545;
      border-color: #dc3545;
      color: #ffffff !important;
      
      i {
        color: #ffffff !important;
      }
    }
  }
  
  // Estados desabilitados
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    box-shadow: none;
  }
  
  // Ícones nos botões
  i {
    font-size: 1.1em;
  }
}

.spinner-border {
  width: 4rem;
  height: 4rem;
  border-width: 0.4em;
  
  &.text-primary {
    color: #1877f2 !important;
  }
  
  &.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.15em;
  }
}

.text-success {
  color: #28a745 !important;
  font-weight: 600;
}

.text-warning {
  color: #ffc107 !important;
  font-weight: 600;
}

.text-danger {
  color: #dc3545 !important;
  font-weight: 600;
}

.text-primary {
  color: #1877f2 !important;
  font-weight: 600;
}

.text-muted {
  color: #6c757d !important;
  font-weight: 400;
}

.text-info {
  color: #17a2b8 !important;
  font-weight: 500;
}

.alert {
  border-radius: 10px;
  border: none;
  
  &.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #ffffff 100%);
    color: #721c24;
  }
}

// Container principal
.container-fluid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

// Toolbar de Ações Moderna
.actions-toolbar {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  
  .card-title {
    color: #495057;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 1.5rem !important;
  }
}

.actions-container {
  display: flex;
  gap: 1.25rem;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}

.btn-action {
  min-width: 150px;
  padding: 0.65rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: 12px;
  border-width: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6rem;
  transition: all 0.3s ease;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  
  i {
    font-size: 1.1rem;
    flex-shrink: 0;
  }
  
  .btn-text {
    font-weight: 600;
    letter-spacing: 0.025em;
  }
  
  .loading-content {
    display: flex;
    align-items: center;
    gap: 0.6rem;
  }
  
  // Hover effects
  &:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
  
  // Estados específicos
  &.btn-primary {
    background: #1877f2;
    border-color: #1877f2;
    color: #ffffff;
    box-shadow: 0 4px 16px rgba(24, 119, 242, 0.3);
    
    i, .btn-text {
      color: #ffffff !important;
    }
    
    &:hover, &:focus, &:active {
      background: #166fe5;
      border-color: #166fe5;
      box-shadow: 0 6px 24px rgba(24, 119, 242, 0.4);
    }
  }
  
  &.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    background: rgba(220, 53, 69, 0.05);
    
    i, .btn-text {
      color: #dc3545 !important;
    }
    
    &:hover, &:focus, &:active {
      background: #dc3545;
      border-color: #dc3545;
      color: #ffffff !important;
      box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
      
      i, .btn-text {
        color: #ffffff !important;
      }
    }
  }
  
  // Estado desabilitado
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  }
}

// Responsividade
@media (max-width: 768px) {
  .page-title-box {
    text-align: center;
    margin-bottom: 1.5rem;
  }
  
  .actions-container {
    flex-direction: column;
    gap: 1rem;
    
    .btn-action {
      width: 100%;
      min-width: auto;
    }
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .list-group-item {
    padding: 1rem;
  }
}

// Melhorias para ícones
.mdi {
  &.mdi-instagram {
    color: #E4405F;
  }
  
  &.mdi-facebook {
    color: #1877f2;
    
    // Exceção: ícone branco em botões primários
    .btn-primary & {
      color: #ffffff !important;
    }
  }
  
  &.mdi-check-circle {
    color: #28a745;
  }
  
  &.mdi-alert-circle {
    color: #ffc107;
  }
}

// Melhorias para ícones grandes
.mdi {
  &[style*="font-size: 48px"] {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  }
  
  &[style*="font-size: 24px"] {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
  
  &.mdi-spin {
    animation: mdi-spin 1s infinite linear;
  }
}

// Animação de rotação
@keyframes mdi-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Estilos gerais
.page-title {
  i {
    font-size: 24px;
    vertical-align: middle;
  }
}

// Card de ações
.actions-toolbar {
  .actions-container {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .btn-action {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 160px;
    
    i {
      font-size: 18px;
    }

    .loading-content {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
}

// Lista de páginas
.list-group-item {
  .avatar-md {
    width: 56px;
    height: 56px;
  }

  img.rounded-circle {
    border: 2px solid #eee;
  }

  .btn {
    min-width: 120px;
  }
}

// Loading e estados
.spinner-border {
  width: 1.5rem;
  height: 1.5rem;

  &.text-primary {
    width: 3rem;
    height: 3rem;
  }
}

// Ajustes responsivos
@media (max-width: 576px) {
  .actions-container {
    flex-direction: column;
    
    .btn-action {
      width: 100%;
    }
  }

  .list-group-item {
    .row {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .col-auto {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
}

// Animações
.mdi-spin {
  animation: spin 1s infinite linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
