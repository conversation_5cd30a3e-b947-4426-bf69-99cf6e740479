.page{
  display: table;
  width: 500px;
  margin: 0 auto;

  padding: 20px;
  margin-top: 20px;
}

.paper{
  width: 88mm;
  display: block;

  background: #fff;
  border: 1px dashed #ccc;

  p,span,label,div{
    color: #000 !important;

    font-family: "luc<PERSON>", <PERSON><PERSON>, <PERSON><PERSON><PERSON> !important;;
  }

  padding: 10px;
  padding-right: 20px;
  .titulo{
    text-align: center;
  }

  p{
    color:black;
    font-weight: bold;
  }

  .row:not(:last-child){
    margin-bottom: 15px;
  }

  .assinatura{
    margin-top: 80px;

    .campo{
      width: 80%;
      margin: 0 auto;
      padding-bottom: 10px;
    }
    label{
      display: block;
    }
    span{
      font-size: 11px;
    }
  }
}

.separador{
  width: 100%;
  border-top: 1px dashed;
  padding: 0px;
  margin:0px;
  position: relative;
  display: table;
  line-height: 10px;
}

.linha{
  display: table;width: 100%;
}

.row.itens{
  div{
    padding-top: 5px;
    padding-bottom:0px;
    margin-bottom: 0px !important;
  }
}

html,body {
  margin: 0 !important;
}

@media print {

  .paper{
    p,span,label,div, h5{
      font-weight: bold  !important;
      font-style: normal !important;
    }
  }

  body {
    width: 88mm;

  }
}

@media not print {
  .paper{
    margin: 0 auto;
    border:  1px solid #A0A091;


  }

  body{
    min-height: 270mm;
    background: #525659 !important;;
  }
}

@page
{
  size: 88mm 297mm ; /* auto is the initial value */
  margin: 0;  /* this affects the margin in the printer settings */
}


