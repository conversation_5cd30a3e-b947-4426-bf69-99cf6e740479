.acoes ::ng-deep .icone  {
  display: inline-block;
  fill: #fff;
  vertical-align: middle;
  svg {
    width: 24px !important;
    height: 24px !important;
  }
}


.card.contato {
  >.card-body{
    padding-right: 0;
  }
  .ultima-visita{
    top: 30px;position: relative;
  }
}

.plano-info{
  border-left: 1px solid #e5e5e5;
  margin-top: -25px;
  margin-bottom: -25px;
  padding-left: 0;

  .card-box{
    padding-top: 0;
    padding-bottom: 0;
    box-shadow: none;

    &.plano{
      border-bottom: 1px solid #e5e5e5;
      margin-top: 21px;
      padding-bottom: 21px;
      border-radius: 0;
      padding-left: 20px;
      display: table;
      width: 100%;
    }

    p-dropdown{
      display: inline-block;
      position: relative;
      top: 10px;
      margin-left: 15px;
    }
  }

  .pontos{

    span {
      font-size: 40px;
      font-weight: 500;
      line-height: 0.5em;
      position: relative;
      top: 3px;
    }
  }

  label{
    margin-bottom: 0;
    line-height: 18px;
    font-size: 16px;
  }

  .por-selo{
    .lista-selos{
      position: relative;   top: -10px;     display: block;
    }
    label{
      line-height: 30px;
      width: 56px;
    }
  }
}

.acoes .btn{
  margin-right:15px
}

.fa-whatsapp{
  font-size: 25px;
}

@media (max-width: 768px){
  .acoes ::ng-deep .icone.tam1,.icone {
    width: 100% !important;
    height: 25px !important;
    svg {
      width: 25px !important;
      height: 25px !important;
    }
  }

  .card.contato {
    >.card-body{
      padding: 1rem !important;
    }
  }
  .plano-info{
    border: none;
    padding-top:0;
    margin-top: 0;
    margin-bottom:0;

    .card-box{
      &.plano{
        padding-bottom: 30px;
      }

      &.por-selo{
        padding-top: 10px;
      }
      p-dropdown{
        margin-left:0;
      }
    }

  }

  .acoes .btn{
    padding: 10px;
    float: left;
    width: 100%;
    height: 100%;
    margin-top: 5px;
    margin-right: 0px;
  }

  .card-box {
    padding: 1rem;
    .lista-selos{
      width: 100%;
      display: table;
      padding-top: 10px;
    }
  }
}

.novo-contato {
  td {
    color: #66a719;
  }
}

.troca {
  td {
    color: #ff3242;
  }
}

td a{
  color: inherit;
}

.tamanho-botao {
  width: 135px;
}


.pontos {
  text-align: center;
  padding: 4px;
  border-bottom-left-radius: 20px;
  border-top-left-radius: 2px;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 2px;
}

.acumulo_pontos {
  background: #6DB31B;
  color: white;
}

.pode_trocar {
  background: #6DB31B;
  color: white;

  .icone.estrela {
    position: absolute;
    bottom: 11px;
    left: 12px;
  }
}
.icone {
  display: inline-block;
  fill: white;
  vertical-align: middle;
}


.pontos-interno {
  border: white solid 1px;
  line-height: 1.2em;
  font-size: 40px;
  font-weight: 600;

  border-bottom-left-radius: 20px;
  border-top-left-radius: 2px;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 2px;
}

.pontos-interno small{
  position: absolute;
  top: 5px;
  left: 63px;
}

.icone.estrela {
  position: absolute;
  left: 35px;
  bottom : 25%;
}

.col-6 {
  padding-left: 6px;
  padding-right: 6px;
}
