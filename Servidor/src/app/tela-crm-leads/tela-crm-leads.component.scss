.mat-card {
  overflow: hidden;
  box-shadow: none;
  border: solid 1px #e5e5e5;
}

.mat-card-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 5px;
}

.mat-card-subtitle {
  font-size: 13px;
  color: #333;
  margin-bottom: 5px;
}

.preco {
  color: green;
}

.card-footer {
  padding: 8px 16px !important;
  display: flex;
  justify-content: space-between;
}

.card-time {
  font-size: 11px;
  color: #333;
  justify-content: space-between;  /* Para alinhamento horizontal */
  align-items: center;
  height: 30px;
  display: flex;
}







.kanban-board {
  display: flex;
  justify-content: flex-start;
  overflow-x: auto;
  white-space: nowrap;
}

.kanban-column {
  border-right: 1px dashed #ccc;
  padding: 10px;
  box-sizing: border-box;
  flex: 1;  /* Ocupa todo o espaço disponível */
  max-width: 300px;
  min-width: 300px;
  width: 400px;
  min-height: 800px;
  flex-shrink: 0;
}

.container-scroll {
  overflow-y: auto;
  overflow-x: scroll;
  height: 800px;
  padding-right: 5px;
}

.kanban-column::-webkit-scrollbar-track {
  background: transparent;
}

.example-custom-placeholder {
  background: #ccc;
  border: dotted 3px #999;
  min-height: 150px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  margin-bottom: 10px;
}

.kanban-column::-webkit-scrollbar-thumb {

}

.no-margin-bottom {
  margin-bottom: 0 !important;
}

.kanban-card {
  margin-bottom: 10px;
}

.mat-card-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 5px;
}

.mat-card-subtitle {
  font-size: 13px;
  color: #333;
  margin-bottom: 5px;
}

.preco {
  color: green;
}

.card-footer {
  padding: 8px 16px !important;
  display: flex;
  justify-content: space-between;
}

.card-time {
  font-size: 12px;
  color: #333;
  justify-content: space-between;  /* Para alinhamento horizontal */
  align-items: center;
  height: 30px;
  display: flex;
}


.container-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.container-scroll::-webkit-scrollbar-thumb {
  background: #dde2e6;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  opacity: .4;
  -webkit-transition: .2s;
  transition: .2s;
}

.container-scroll::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.container-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.mat-card-actions, .mat-card-subtitle, .mat-card-content {
  margin-bottom: 0px;
}
