.left-side-menu{
  padding-top: 0px;
  overflow: auto;
}

.nav-user{
  padding-bottom: 10px !important;
}

.empresa{
  font-size: 11px;
}

.link-carpadio{
  display: inline-block;


}

.link-cardapio{
  border: solid 1px #DDDCE0;
  padding: 1px 8px;
  border-radius: 20px;
  margin-left: -5px;
}

:host-context(.enlarged) .dadosUsuario,
:host-context(.enlarged) .tipo-envio,
:host-context(.enlarged) .link-cardapio span ,
:host-context(.enlarged) .nav-second-level{
  display: none;
}

:host-context(.enlarged) .nav-user {
  padding-bottom: 30px !important;
}

:host-context(.enlarged) .link-cardapio{
  float: left;
  margin-left: inherit;
}


.nav-user {
  padding-left:18px !important;

}
.nav-link {
  padding-left: 0;
}



.text-primary{
  color:#3a44b9 !important;
}

.text-bold{
  font-weight: bold;
}

.bg-primary{
  background-color:#3a44b9  !important;
}

:host-context(.sidebar-enable) .pro-user-name {
  display: initial !important;
}


#sidebar-menu > ul > li > a {
  padding: 8px 20px !important;
}
.nav-second-level li a, .nav-thrid-level li a {
  padding: 5px 20px !important;
}
