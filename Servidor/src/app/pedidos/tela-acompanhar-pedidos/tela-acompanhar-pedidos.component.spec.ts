import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { TelaAcompanharPedidosComponent } from './tela-acompanhar-pedidos.component';

describe('TelaAcompanharPedidosComponent', () => {
  let component: TelaAcompanharPedidosComponent;
  let fixture: ComponentFixture<TelaAcompanharPedidosComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ TelaAcompanharPedidosComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TelaAcompanharPedidosComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
