<h4 class="page-title">
  <i class="fab fa-whatsapp fa-lg" style="color: green;font-weight: bold;"></i> Fluxos de Conversas para WhatsApp Oficial
</h4>

<div class="card col-xl-8 col-md-10 col-12">
  <div class="card-body">
    <div class="row">
      <div class="col">
        <div class="alert alert-success" role="alert" *ngIf="msgSucesso">
          <i class="mdi mdi-check-all mr-2"></i> {{msgSucesso}}
        </div>

        <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}" (ngSubmit)="onSubmit()"
              novalidate #frm="ngForm">
          
          <div class="form-group">
            <label for="usarFluxoTypebot" class="font-weight-bold font-14 mt-1">Usar Fluxo de Conversas:</label>
            <br>
            <kendo-switch id="usarFluxoTypebot" name="usarFluxoTypebot" [(ngModel)]="configuracoes.usarFluxoTypebot"
                          [onLabel]="'Sim'"
                          [offLabel]="'Não'"
                          (valueChange)="onUsarFluxoTypebotChange($event)"></kendo-switch>

            <div class="pt-2" *ngIf="configuracoes.usarFluxoTypebot">
              <kendo-expansionpanel [expanded]="true" [title]="'Como funciona o Fluxo de Conversas Padrão'" class="expansion-panel-custom">
                <div class="content">
                  <div class="collapse show" id="infoBox">
                    <div class="info-box pt-2 pb-2 pl-3 pr-3">
                      <h5>O fluxo de conversas padrão 🤖 começa oferecendo ao cliente as seguintes opções:</h5>

                      <ul class="list-unstyled mt-3">
                        <li class="mb-2">🛍️ <strong>Fazer Pedido</strong>: Inicia um novo pedido, coletando as informações necessárias.</li>
                        <li class="mb-2">📱 <strong>Ver Cardápio</strong>: Direciona o cliente para o cardápio digital.</li>
                        <li class="mb-2">❓ <strong>Tirar Dúvida</strong>: Responde as perguntas frequentes ou esclarece dúvidas.</li>
                        <li class="mb-2">👤 <strong>Falar com Atendente</strong>: Conecta o cliente diretamente com um atendente humano.</li>
                      </ul>

                      <!-- Highlighted Section -->
                      <div class="highlight-box">
                        <h5 class="text-success mb-4">Ao escolher "Fazer Pedido", o fluxo segue estas etapas:</h5>

                        <div class="row">
                          <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center">
                              <span class="step-number">1</span>
                              <div>
                                <strong>Forma de Entrega</strong>
                                <br>
                                <small class="text-muted">🚚 Escolha como receber</small>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center">
                              <span class="step-number">2</span>
                              <div>
                                <strong>Localização</strong>
                                <br>
                                <small class="text-muted">📍 Endereço de entrega</small>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center">
                              <span class="step-number">3</span>
                              <div>
                                <strong>Forma de Pagamento</strong>
                                <br>
                                <small class="text-muted">💳 Método de pagamento</small>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="text-center mt-3">
                          <p class="mb-0">
                            <strong>Próximo passo:</strong>
                            Cliente é direcionado para o 📱 <strong>cardápio digital</strong>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </kendo-expansionpanel>
            </div>
          </div>

          <ng-container *ngIf="configuracoes.usarFluxoTypebot && !configuracoes.typebotConfigurado">
            <div class="text-center mt-3 mb-3">
              <button type="button" class="btn btn-lg btn-primary" (click)="abrirTelaConfigTypebot()">
                <i class="fas fa-cogs"></i> Ativar Fluxos de Conversas
              </button>
            </div>
          </ng-container>

          <div class="card mb-3" *ngIf="criandoFluxosTypebot">
            <div class="card-body text-center">
              <h5>Fluxos de Conversas estão sendo criados...</h5>
            </div>
          </div>

          <div class="row mt-3" *ngIf="configuracoes.usarFluxoTypebot && configuracoes.typebotConfigurado">
            <div class="col-12 col-md-6">
              <div class="card">
                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                  <i class="fab fa-whatsapp me-2" style="color: #25D366; font-size: 2.5rem;"></i>
                  <h4 class="mb-3">WhatsApp</h4>
                  <button type="button" class="btn btn-success" (click)="editarFluxoTypebot('whatsapp', 'WhatsApp')">
                    Editar Fluxo de Conversas
                  </button>
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="card">
                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                  <i class="fab fa-instagram me-2" style="color: #E1306C; font-size: 2.5rem;"></i>
                  <h4 class="mb-3">Instagram</h4>
                  <button type="button" class="btn" style="background-color: #E1306C; color: white;" (click)="editarFluxoTypebot('instagram', 'Instagram')">
                    Editar Fluxo de Conversas
                  </button>
                </div>
              </div>
            </div>
          </div>

          <hr>
          <div class="form-group">
            <div class="alert alert-success" role="alert" *ngIf="msgSucesso">
              <i class="mdi mdi-check-all mr-2"></i> {{msgSucesso}}
            </div>

            <button type="submit" class="btn btn-primary mr-2">Salvar</button>
            <button type="reset" class="btn btn-light waves-effect">Cancelar</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<style>
  .highlight-box {
    background-color: #f0f8f0;
    border-left: 4px solid #28a745;
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
  }

  .step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #28a745;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
  }

  .info-box {
    background-color: #f8f9fe;
    border-radius: 8px;
    padding: 15px;
  }
</style>